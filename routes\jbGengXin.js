
const express = require('express')
const router = express.Router();

const fs = require('fs')
const { publicPath } = require('../serverOpt');



// 读取文件 返回当前版本信息
router.get('/gengXin', function (req, res) {

    fs.readFile(publicPath + '/static/version.txt', 'utf-8', function (err1, dataStr) {
        if (err1) {
            res.json({
                code: -1,
                msg: "失败" + err1
            })
        } else {
            res.json({
                code: 0,
                msg: dataStr
            })
        }
    });
})



module.exports = router;