const express = require('express');
const router = express.Router();
const { pool } = require('../db/mysql');
const debug = require('debug')('a-sokio-yun:logs');
const crypto = require('crypto');

// 响应格式中间件
router.use((req, res, next) => {
    res.apiSuccess = (data, message = 'success') => {
        res.json({ code: 0, msg: message, data });
    };
    res.apiError = (message = 'error', code = -1) => {
        res.status(500).json({ code, msg: message, data: null });
    };
    next();
});

// 短期缓存(5秒)
const cache = new Map();
const CACHE_TTL = 5000;

/**
 * 查询设备日志
 * GET /logs
 * 参数:
 *   - page: 页码(默认1)
 *   - limit: 每页数量(默认20)
 *   - device_id: 设备ID(可选)
 */
router.get('/', async (req, res) => {
    const { page = 1, limit = 20, device_id } = req.query;
    const offset = (Math.max(1, page) - 1) * limit;
    
    // 生成缓存键
    const cacheKey = `logs:${device_id || 'all'}:${page}:${limit}`;
    
    // 检查缓存
    if (cache.has(cacheKey)) {
        const cached = cache.get(cacheKey);
        if (Date.now() - cached.timestamp < CACHE_TTL) {
            debug('从缓存返回数据');
            res.set('ETag', cached.etag);
            return res.status(304).end();
        }
    }
    
    try {
        const connection = await pool.getConnection();
        debug('获取数据库连接');
        
        try {
            // 构建查询
            let query = 'SELECT id, device_id, status, execution_data, created_at FROM device_logs';
            let countQuery = 'SELECT COUNT(*) as total FROM device_logs';
            const params = [];
            
            if (device_id) {
                query += ' WHERE device_id = ?';
                countQuery += ' WHERE device_id = ?';
                params.push(device_id);
            }
            
            query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
            params.push(parseInt(limit), offset);
            
            // 并行执行查询
            const [rows, [[{total}]]] = await Promise.all([
                connection.query(query, params),
                connection.query(countQuery, params.slice(0, -2))
            ]);
            
            debug(`获取到${rows.length}条记录，总数: ${total}`);
            
            // 生成ETag
            const etag = crypto
                .createHash('md5')
                .update(JSON.stringify(rows))
                .digest('hex');
            
            // 设置缓存
            cache.set(cacheKey, {
                data: rows,
                total,
                etag,
                timestamp: Date.now()
            });
            
            // 设置响应头
            res.set('ETag', etag);
            
            res.apiSuccess({
                list: rows,
                pagination: {
                    total: Number(total),
                    page: Number(page),
                    limit: Number(limit),
                    pages: Math.ceil(total / limit)
                }
            });
        } finally {
            connection.release();
            debug('释放数据库连接');
        }
    } catch (err) {
        debug('查询失败:', err);
        res.apiError(err.message);
    }
});

/**
 * 插入设备日志
 * POST /logs
 * 请求体:
 *   - device_id: 设备ID(必填)
 *   - status: 状态(必填, pending/running/completed/failed)
 *   - execution_data: 执行数据(可选)
 */
router.post('/', async (req, res) => {
    const { device_id, status, execution_data } = req.body;
    
    // 验证必填字段
    if (!device_id || !status) {
        return res.apiError('device_id和status是必填字段');
    }
    
    // 验证status枚举值
    const validStatuses = ['pending', 'running', 'completed', 'failed'];
    if (!validStatuses.includes(status)) {
        return res.apiError(`status必须是以下值之一: ${validStatuses.join(', ')}`);
    }
    
    try {
        const connection = await pool.getConnection();
        debug('获取数据库连接');
        
        try {
            const [result] = await connection.query(
                'INSERT INTO device_logs (device_id, status, execution_data) VALUES (?, ?, ?)',
                [device_id, status, execution_data || null]
            );
            
            debug(`成功插入日志，ID: ${result.insertId}`);
            
            res.apiSuccess({ id: result.insertId }, '日志插入成功');
        } finally {
            connection.release();
            debug('释放数据库连接');
        }
    } catch (err) {
        debug('插入日志失败:', err);
        res.apiError(err.message);
    }
});

module.exports = router;
