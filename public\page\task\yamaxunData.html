<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <fieldset>
                <legend>【商品与关键词】</legend>

                <!-- 时间选择器 -->
                <div class="layui-form">
                    <div class="layui-inline">
                        <label class="layui-form-label">开始时间</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="startTime" placeholder="请选择开始时间">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">结束时间</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="endTime" placeholder="请选择结束时间">
                        </div>
                    </div>
                    <button class="layui-btn layui-btn-normal" id="searchBtn">查询</button>
                </div>

                <!-- 数据展示表格 -->
                <table class="layui-table" id="shoppingTable" lay-filter="shoppingTable"></table>
            </fieldset>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use(['table', 'jquery', 'laydate'], function () {
            var table = layui.table;
            var $ = layui.jquery;
            var laydate = layui.laydate;

            // 初始化时间选择器
            laydate.render({ elem: '#startTime', type: 'datetime', format: 'yyyy-MM-dd HH:mm:ss' });
            laydate.render({ elem: '#endTime', type: 'datetime', format: 'yyyy-MM-dd HH:mm:ss' });

            // 获取当前时间并计算 7 天前的日期
            function getDefaultTimeRange() {
                let now = new Date();
                let sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(now.getDate() - 7);

                let formatDate = (date) => {
                    return date.getFullYear() + '-' +
                        String(date.getMonth() + 1).padStart(2, '0') + '-' +
                        String(date.getDate()).padStart(2, '0') + ' ' +
                        String(date.getHours()).padStart(2, '0') + ':' +
                        String(date.getMinutes()).padStart(2, '0') + ':' +
                        String(date.getSeconds()).padStart(2, '0');
                };

                return {
                    startTime: formatDate(sevenDaysAgo),
                    endTime: formatDate(now)
                };
            }

            // 渲染数据表格
            function renderTable(startTime, endTime) {
                table.render({
                    elem: '#shoppingTable',
                    url: 'http://114.55.151.143:3101/getData',
                    method: 'GET',
                    where: { startTime, endTime }, // 传递时间参数
                    page: true,
                    limit: 10,
                    cols: [[
                        { field: 'id', title: 'ID', width: 80, sort: true },
                        { field: 'productName', title: '商品名称' },
                        { field: 'keyword', title: '关键词' },
                        { field: 'created_at', title: '创建时间', sort: true }
                    ]],
                    response: {
                        statusCode: 200
                    },
                    parseData: function (res) {
                        return {
                            "code": 200,
                            "msg": res.message,
                            "count": res.total,  // 使用后端返回的 total 字段
                            "data": res.data
                        };
                    }
                });
            }

            // 查询按钮事件
            $('#searchBtn').click(function () {
                var startTime = $('#startTime').val();
                var endTime = $('#endTime').val();
                if (!startTime || !endTime) {
                    alert("请选择时间范围");
                    return;
                }
                renderTable(startTime, endTime);
            });

            // 默认加载最近 7 天的数据
            let { startTime, endTime } = getDefaultTimeRange();
            $('#startTime').val(startTime);
            $('#endTime').val(endTime);
            renderTable(startTime, endTime);
        });
    </script>
</body>

</html>
