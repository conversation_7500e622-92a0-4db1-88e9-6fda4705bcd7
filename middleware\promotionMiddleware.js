const PromotionTracker = require('../utils/promotionTracker');

/**
 * 推广链接访问跟踪中间件
 * 用于跟踪通过推广链接访问的用户
 */
function promotionTrackingMiddleware(req, res, next) {
  // 异步处理推广跟踪，不阻塞主流程
  setImmediate(async () => {
    try {
      // 从URL参数或referer中提取推广用户ID
      let promotionUserId = req.query.id;
      
      if (!promotionUserId && req.headers.referer) {
        promotionUserId = PromotionTracker.extractPromotionUserId(req.headers.referer);
      }
      
      if (promotionUserId) {
        // 验证推广用户是否有效
        const isValid = await PromotionTracker.isValidPromotionUser(promotionUserId);
        
        if (isValid) {
          // 获取客户端信息
          const clientIP = PromotionTracker.getClientIP(req);
          const userAgent = req.headers['user-agent'] || '';
          const referer = req.headers.referer || '';
          
          // 记录访问
          await PromotionTracker.recordVisit(promotionUserId, clientIP, userAgent, referer);
          
          // 将推广用户ID存储到session中，供后续使用
          req.session.promotionUserId = promotionUserId;
          
          console.log(`🔗 推广访问跟踪: 用户${promotionUserId}, IP: ${clientIP}`);
        }
      }
    } catch (error) {
      console.error('推广跟踪中间件错误:', error);
    }
  });
  
  next();
}

/**
 * 推广操作记录中间件
 * 用于记录用户的具体操作行为
 */
function createPromotionActionMiddleware(actionType) {
  return function(req, res, next) {
    // 保存原始的res.json方法
    const originalJson = res.json;
    
    // 重写res.json方法以拦截响应
    res.json = function(data) {
      // 异步记录操作，不阻塞响应
      setImmediate(async () => {
        try {
          const promotionUserId = req.session.promotionUserId;
          
          if (promotionUserId) {
            const clientIP = PromotionTracker.getClientIP(req);
            const userAgent = req.headers['user-agent'] || '';
            
            // 根据响应数据判断操作结果
            let finalActionType = actionType;
            let options = {
              userAgent: userAgent
            };
            
            // 根据不同的操作类型和响应结果调整记录
            if (actionType === 'login') {
              if (data && data.code === 0) {
                finalActionType = 'login_success';
                // 如果响应中包含用户信息，记录下来
                if (data.data) {
                  options.userId = data.data.userId || data.data.username;
                  options.douyinName = data.data.douyinName;
                  options.douyinId = data.data.douyinId;
                  options.ckData = data.data.ck;
                }
              } else {
                finalActionType = 'login_fail';
              }
            } else if (actionType === 'scan') {
              finalActionType = 'scan';
            } else if (actionType === 'expire') {
              finalActionType = 'request_expire';
            }
            
            // 记录操作
            await PromotionTracker.recordAction(promotionUserId, finalActionType, clientIP, options);
            
            console.log(`📊 推广操作记录: 用户${promotionUserId}, 操作${finalActionType}, IP: ${clientIP}`);
          }
        } catch (error) {
          console.error('推广操作记录错误:', error);
        }
      });
      
      // 调用原始的json方法
      return originalJson.call(this, data);
    };
    
    next();
  };
}

/**
 * 手动记录推广操作的辅助函数
 * @param {object} req Express请求对象
 * @param {string} actionType 操作类型
 * @param {object} options 额外选项
 */
async function recordPromotionAction(req, actionType, options = {}) {
  try {
    const promotionUserId = req.session.promotionUserId;
    
    if (promotionUserId) {
      const clientIP = PromotionTracker.getClientIP(req);
      const userAgent = req.headers['user-agent'] || '';
      
      const finalOptions = {
        userAgent: userAgent,
        ...options
      };
      
      await PromotionTracker.recordAction(promotionUserId, actionType, clientIP, finalOptions);
      
      console.log(`📊 手动记录推广操作: 用户${promotionUserId}, 操作${actionType}, IP: ${clientIP}`);
    }
  } catch (error) {
    console.error('手动记录推广操作错误:', error);
  }
}

module.exports = {
  promotionTrackingMiddleware,
  createPromotionActionMiddleware,
  recordPromotionAction
};
