<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">

            <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="addGroup"> 新增分组 </button>
                <!-- <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="add"> 新增分组/添加设备(输入方式) </button> -->
            </div>
        </script>
            <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
            <script type="text/html" id="currentTableBar">
            <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="addDevices">添加设备</a>
            <a class="layui-btn layui-btn-warm layui-btn-xs data-count-edit" lay-event="editGroupName">改组名</a>
            <a class="layui-btn layui-btn-warm layui-btn-xs data-count-edit" lay-event="deviceList">设备列表</a>
            <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">删除分组</a>
        </script>

        </div>
    </div>
    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script>
        layui.use(['form', 'table'], function () {
            var $ = layui.jquery,
                form = layui.form,
                table = layui.table

            let tableIns = table.render({
                elem: '#currentTableId',
                url: '/indexGroup',
                toolbar: '#toolbarDemo',
                // defaultToolbar: ['filter', 'exports', 'print', {
                //     title: '提示',
                //     layEvent: 'LAYTABLE_TIPS',
                //     icon: 'layui-icon-tips'
                // }],
                cols: [[
                    { field: '_id', width: 80, title: '分组ID' },
                    { field: 'groupName', minWidth: 20, title: '组名', sort: true },
                    { field: 'created', minWidth: 20, title: '创建时间', sort: true },
                    { title: '操作', minWidth: 300, toolbar: '#currentTableBar', align: "center" },
                ]],
                done: function (res) {
                    //console.log(res.data)
                    window.localStorage.setItem("groupData", JSON.stringify(res.data))
                }
            });

            /**
             * toolbar事件监听
             */
            table.on('toolbar(currentTableFilter)', function (obj) {
                if (obj.event === 'addGroup') {   // 监听添加操作
                    layer.prompt({
                        formType: 2,
                        value: "",
                        title: '请输入组名',
                        area: ['150px', '40px'] //自定义文本域宽高
                    }, function (value, index, elem) {
                        layer.close(index);
                        //表格重载
                        // console.log(checkData)
                        tableIns.reload({
                            url: '/indexGroup/addGroup',
                            method: 'get',
                            where: {
                                groupName: value
                            }
                        })
                        layer.msg("添加成功")
                        return true

                    });
                }
            });

            table.on('tool(currentTableFilter)', function (obj) {
                let data = obj.data;
                // console.log(data);
                var groupName = data.groupName
                window.groupObj = {
                    groupName: groupName,
                    _id: data._id
                }
                // window.groupName = groupName
                if (obj.event === 'deviceList') {
                    var index = layer.open({
                        title: '分组:【' + groupName + '】下面的设备列表',
                        type: 2,
                        area: ['80%', '98%'],
                        content: '/page/devices/tableGroup_deviceList.html',
                        closeBtn: 1,
                        shadeClose: true,//其他区域关闭
                    });

                } else if (obj.event === 'delete') {
                    layer.confirm('真的删除[' + data.groupName + ']的分组么', function (index) {
                        tableIns.reload({
                            url: '/indexGroup/delGroup',
                            method: 'get',
                            where: {
                                groupName: data.groupName,
                                _id: data._id
                            }
                        })
                        layer.close(index);
                    });


                } else if (obj.event === "addDevices") {

                    var index = layer.open({
                        title: '未分组的设备',
                        type: 2,
                        area: ['85%', '98%'],
                        content: '/page/devices/tableGroup_addDevices.html',
                        closeBtn: 1,
                        shadeClose: true,//其他区域关闭
                        btn: ['添加到分组[' + groupName + ']', '取消'],
                        yes: function (index, layero) {
                            let iframeWin = $(layero).find('iframe')[0].contentWindow
                            let deviceIdArr = iframeWin.formData()
                            if (deviceIdArr) {
                                axios.post('/indexGroup/addDevices', {
                                    deviceIdArr: deviceIdArr,
                                    groupId: data._id
                                }).then(res => {
                                    layer.msg(res.data.msg)
                                    layer.close(index);
                                }).catch(err => {
                                    layer.alert(err.message)
                                })


                            }
                        },
                        // btn2: function () {//取消
                        //     layer.msg("取消添加设备")
                        // }
                    });


                } else if (obj.event === "editGroupName") {
                    layer.prompt({
                        formType: 2,
                        value: groupName,
                        title: '请输入组名',
                        area: ['150px', '40px'] //自定义文本域宽高
                    }, function (value, index, elem) {
                        layer.close(index);
                        //表格重载
                        // console.log(checkData)
                        tableIns.reload({
                            url: '/indexGroup/editGroupName',
                            method: 'get',
                            where: {
                                groupName: value,
                                _id: data._id
                            }
                        })

                        layer.msg("添加成功")
                        return true
                    });
                }
            });


        });
    </script>

</body>

</html>