console.log('开始调试路由加载...');

try {
  // 测试路由文件加载
  console.log('1. 测试路由文件加载...');
  const douyinRouter = require('./routes/douyinAccount');
  console.log('✓ douyinAccount.js 加载成功');
  console.log('路由类型:', typeof douyinRouter);
  console.log('是否为函数:', typeof douyinRouter === 'function');
  
  // 测试Express应用加载
  console.log('\n2. 测试Express应用加载...');
  const express = require('express');
  const app = express();
  
  // 添加中间件
  app.use(express.json());
  
  // 注册路由
  console.log('3. 注册路由...');
  app.use('/api/douyin', douyinRouter);
  console.log('✓ 路由注册成功');
  
  // 列出所有路由
  console.log('\n4. 检查注册的路由:');
  app._router.stack.forEach((middleware, index) => {
    if (middleware.route) {
      console.log(`  ${index}: ${middleware.route.path} [${Object.keys(middleware.route.methods).join(', ')}]`);
    } else if (middleware.name === 'router') {
      console.log(`  ${index}: ${middleware.regexp} (router middleware)`);
      if (middleware.handle && middleware.handle.stack) {
        middleware.handle.stack.forEach((route, routeIndex) => {
          if (route.route) {
            console.log(`    ${routeIndex}: ${route.route.path} [${Object.keys(route.route.methods).join(', ')}]`);
          }
        });
      }
    }
  });
  
  // 启动测试服务器
  console.log('\n5. 启动测试服务器...');
  const PORT = 3001;
  const server = app.listen(PORT, () => {
    console.log(`✓ 测试服务器启动成功: http://localhost:${PORT}`);
    
    // 测试API调用
    setTimeout(async () => {
      try {
        const axios = require('axios');
        console.log('\n6. 测试API调用...');
        const response = await axios.get(`http://localhost:${PORT}/api/douyin/accounts`);
        console.log('✓ API调用成功:', response.status);
        console.log('返回数据:', response.data);
      } catch (error) {
        console.error('✗ API调用失败:', error.response?.status, error.response?.data || error.message);
      }
      
      server.close();
      process.exit(0);
    }, 2000);
  });
  
} catch (error) {
  console.error('✗ 调试失败:', error.message);
  console.error('错误堆栈:', error.stack);
  process.exit(1);
}
