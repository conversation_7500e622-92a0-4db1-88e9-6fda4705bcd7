# 推广操作数据保存API文档

## 📋 概述

推广操作数据保存API用于接收和保存推广活动中的各种操作数据到`promotion_actions`表中。支持单条和批量数据保存。

## 🔗 API端点

- **基础URL**: `http://localhost:15001/api/promotion-actions`
- **单条保存**: `POST /save`
- **批量保存**: `POST /batch-save`

## 📝 数据结构

### promotion_actions表结构

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | INT | 自动 | 主键，自增ID |
| promotion_user_id | VARCHAR(50) | ✅ | 推广用户ID |
| action_type | ENUM | ✅ | 操作类型 |
| ip_address | VARCHAR(45) | ✅ | IP地址 |
| ip_province | VARCHAR(50) | ❌ | IP所属省份（自动解析） |
| douyin_name | VARCHAR(200) | ❌ | 抖音名 |
| douyin_id | VARCHAR(100) | ❌ | 抖音号 |
| ck_data | TEXT | ❌ | CK数据 |
| user_agent | TEXT | ❌ | 用户代理 |
| action_time | TIMESTAMP | 自动 | 操作时间（自动生成） |
| extra_data | JSON | ❌ | 额外数据 |

### 支持的操作类型

- `scan`: 扫码操作
- `login_success`: 登录成功
- `login_fail`: 登录失败
- `request_expire`: 请求过期

## 🚀 API详情

### 1. 单条数据保存

**端点**: `POST /api/promotion-actions/save`

**请求体**:
```json
{
  "promotion_user_id": "USER001",
  "action_type": "scan",
  "ip_address": "*************",
  "ip_province": "北京",
  "douyin_name": "测试用户",
  "douyin_id": "dy123456",
  "ck_data": "sessionid=abc123",
  "user_agent": "Mozilla/5.0...",
  "extra_data": {
    "custom_field": "custom_value"
  }
}
```

**成功响应**:
```json
{
  "code": 0,
  "msg": "数据保存成功",
  "data": {
    "id": 123,
    "promotion_user_id": "USER001",
    "action_type": "scan",
    "ip_address": "*************",
    "ip_province": "北京",
    "douyin_name": "测试用户",
    "douyin_id": "dy123456",
    "save_time": "2025-06-20T05:38:04.406Z"
  }
}
```

**错误响应**:
```json
{
  "code": -1,
  "msg": "推广用户ID不能为空",
  "data": null
}
```

### 2. 批量数据保存

**端点**: `POST /api/promotion-actions/batch-save`

**请求体**:
```json
{
  "actions": [
    {
      "promotion_user_id": "USER001",
      "action_type": "scan",
      "ip_address": "*************"
    },
    {
      "promotion_user_id": "USER001",
      "action_type": "login_success",
      "ip_address": "*************",
      "douyin_name": "用户1",
      "douyin_id": "dy123",
      "ck_data": "sessionid=xyz"
    }
  ]
}
```

**成功响应**:
```json
{
  "code": 0,
  "msg": "批量保存完成: 成功2条, 失败0条",
  "data": {
    "success_count": 2,
    "error_count": 0,
    "results": [
      {
        "index": 0,
        "id": 124,
        "promotion_user_id": "USER001",
        "action_type": "scan"
      },
      {
        "index": 1,
        "id": 125,
        "promotion_user_id": "USER001",
        "action_type": "login_success"
      }
    ],
    "errors": []
  }
}
```

## ✅ 参数验证

### 必填字段验证
- `promotion_user_id`: 不能为空
- `action_type`: 必须是有效的操作类型
- `ip_address`: 必须是有效的IP地址格式

### 操作类型验证
只接受以下操作类型：
- `scan`
- `login_success`
- `login_fail`
- `request_expire`

### IP地址验证
使用正则表达式验证IPv4地址格式：
```regex
^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$
```

## 🔧 特殊功能

### 1. 自动IP省份解析
如果未提供`ip_province`，系统会根据IP地址自动解析省份信息：

```javascript
// IP前缀到省份的映射
const ipProvinceMap = {
  "192.168.1": "北京",
  "192.168.2": "上海", 
  "192.168.3": "广东",
  "192.168.4": "浙江",
  "192.168.5": "江苏",
  "10.0.0": "内网",
  "127.0.0": "本地"
};
```

### 2. 自动用户代理获取
如果未提供`user_agent`，系统会自动从请求头中获取。

### 3. JSON数据处理
`extra_data`字段支持JSON格式数据，可以传入字符串或对象，系统会自动处理。

## 📊 使用示例

### JavaScript/Axios示例

```javascript
// 单条保存
const saveData = async (actionData) => {
  try {
    const response = await axios.post('/api/promotion-actions/save', {
      promotion_user_id: 'USER001',
      action_type: 'scan',
      ip_address: '*************',
      douyin_name: '测试用户'
    });
    
    if (response.data.code === 0) {
      console.log('保存成功:', response.data.data);
    } else {
      console.error('保存失败:', response.data.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};

// 批量保存
const batchSave = async (actionsArray) => {
  try {
    const response = await axios.post('/api/promotion-actions/batch-save', {
      actions: actionsArray
    });
    
    console.log(`批量保存完成: 成功${response.data.data.success_count}条`);
    if (response.data.data.errors.length > 0) {
      console.log('错误详情:', response.data.data.errors);
    }
  } catch (error) {
    console.error('批量保存失败:', error);
  }
};
```

### cURL示例

```bash
# 单条保存
curl -X POST http://localhost:15001/api/promotion-actions/save \
  -H "Content-Type: application/json" \
  -d '{
    "promotion_user_id": "USER001",
    "action_type": "scan",
    "ip_address": "*************"
  }'

# 批量保存
curl -X POST http://localhost:15001/api/promotion-actions/batch-save \
  -H "Content-Type: application/json" \
  -d '{
    "actions": [
      {
        "promotion_user_id": "USER001",
        "action_type": "scan",
        "ip_address": "*************"
      },
      {
        "promotion_user_id": "USER002",
        "action_type": "login_success",
        "ip_address": "*************"
      }
    ]
  }'
```

## ⚠️ 注意事项

1. **批量保存限制**: 单次批量保存最多支持100条记录
2. **事务处理**: 批量保存使用数据库事务，确保数据一致性
3. **错误处理**: 批量保存中的单条错误不会影响其他记录的保存
4. **数据长度**: 注意各字段的长度限制，超长数据会被拒绝
5. **IP省份**: 自动解析的IP省份基于简化映射，可能不够精确

## 🔍 错误代码

| 错误代码 | 说明 |
|----------|------|
| -1 | 参数错误或保存失败 |
| 0 | 成功 |

常见错误信息：
- "推广用户ID不能为空"
- "操作类型不能为空"
- "IP地址不能为空"
- "无效的操作类型"
- "IP地址格式不正确"
- "actions参数必须是非空数组"
- "批量保存最多支持100条记录"
