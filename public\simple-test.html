<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .stats { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 15px 0; }
        .stat { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007cba; }
        .stat-label { font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <h1>🧪 推广用户简化测试</h1>
    
    <button onclick="runFullTest()">运行完整测试</button>
    <button onclick="clearResults()">清空结果</button>
    
    <div id="results"></div>
    
    <div class="stats" id="statsDisplay" style="display: none;">
        <div class="stat">
            <div class="stat-number" id="visitCount">0</div>
            <div class="stat-label">访问次数</div>
        </div>
        <div class="stat">
            <div class="stat-number" id="successCount">0</div>
            <div class="stat-label">成功数量</div>
        </div>
        <div class="stat">
            <div class="stat-number" id="uniqueIpCount">0</div>
            <div class="stat-label">独立IP数</div>
        </div>
    </div>

    <script>
        function log(message, isError = false) {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(div);
            console.log(message);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('statsDisplay').style.display = 'none';
        }
        
        async function runFullTest() {
            clearResults();
            log('🚀 开始完整测试流程...');
            
            try {
                // 步骤1: 登录
                log('1️⃣ 正在登录...');
                const loginResponse = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: '1001', password: '123456' })
                });
                
                const loginData = await loginResponse.json();
                
                if (loginData.code === 0) {
                    log(`✅ 登录成功: ${loginData.data.username}`);
                    
                    // 检查cookie
                    const cookies = document.cookie;
                    log(`🍪 当前cookies: ${cookies || '无'}`);
                    
                    // 等待一下确保session生效
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // 步骤2: 获取统计数据
                    log('2️⃣ 正在获取统计数据...');
                    const statsResponse = await fetch('/api/promotion/promoter/today-stats', {
                        method: 'GET',
                        credentials: 'include',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const statsData = await statsResponse.json();
                    
                    if (statsData.code === 0) {
                        const stats = statsData.data;
                        log(`✅ 统计数据获取成功`);
                        log(`📊 数据详情: 访问=${stats.visit_count}, 成功=${stats.success_count}, IP=${stats.unique_ip_count}`);
                        
                        // 更新显示
                        document.getElementById('visitCount').textContent = stats.visit_count;
                        document.getElementById('successCount').textContent = stats.success_count;
                        document.getElementById('uniqueIpCount').textContent = stats.unique_ip_count;
                        document.getElementById('statsDisplay').style.display = 'grid';
                        
                        // 检查数据是否为0
                        const hasData = Object.values(stats).some(val => val > 0);
                        if (hasData) {
                            log('🎉 数据显示正常！问题已解决！');
                        } else {
                            log('⚠️ 数据仍然全部为0，需要进一步检查数据库', true);
                        }
                        
                    } else {
                        log(`❌ 统计数据获取失败: ${statsData.msg}`, true);
                        
                        // 如果是认证失败，尝试检查session
                        if (statsData.msg.includes('登录')) {
                            log('🔍 检查session状态...');
                            const sessionResponse = await fetch('/api/promotion/promoter/session-test', {
                                credentials: 'include'
                            });
                            const sessionData = await sessionResponse.json();
                            log(`Session状态: ${JSON.stringify(sessionData.data, null, 2)}`);
                        }
                    }
                    
                } else {
                    log(`❌ 登录失败: ${loginData.msg}`, true);
                }
                
            } catch (error) {
                log(`❌ 测试过程中发生错误: ${error.message}`, true);
            }
            
            log('🏁 测试完成');
        }
        
        // 页面加载时显示初始状态
        window.onload = function() {
            log('📋 页面已加载，点击"运行完整测试"开始测试');
        };
    </script>
</body>
</html>
