const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 检查推广用户数据
async function checkPromoterUsers() {
  console.log('🔍 检查推广用户数据...');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查user表中的推广用户
    console.log('\n1️⃣ 检查user表中的推广用户...');
    const [users] = await connection.execute(
      `SELECT promoter_id, username, password, status, user_type, promotion_link
       FROM user 
       WHERE user_type = 'promoter' OR promoter_id IS NOT NULL
       ORDER BY promoter_id`
    );
    
    console.log(`找到 ${users.length} 个推广用户:`);
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ID=${user.promoter_id}, 用户名=${user.username}, 状态=${user.status}, 类型=${user.user_type}`);
    });
    
    // 2. 检查特定用户1001
    console.log('\n2️⃣ 检查用户ID 1001...');
    const [user1001] = await connection.execute(
      `SELECT * FROM user WHERE promoter_id = '1001'`
    );
    
    if (user1001.length > 0) {
      console.log('✅ 用户1001存在:');
      const user = user1001[0];
      console.log(`   用户名: ${user.username}`);
      console.log(`   密码: ${user.password}`);
      console.log(`   状态: ${user.status}`);
      console.log(`   用户类型: ${user.user_type}`);
      console.log(`   推广链接: ${user.promotion_link}`);
    } else {
      console.log('❌ 用户1001不存在，创建测试用户...');
      
      // 创建测试用户
      await connection.execute(
        `INSERT INTO user (promoter_id, username, password, status, user_type, promotion_link)
         VALUES ('1001', 'test_promoter', '123456', 1, 'promoter', 'http://localhost:15001/promoter-login?user_id=1001')`
      );
      
      console.log('✅ 测试用户1001创建成功');
    }
    
    // 3. 测试登录查询
    console.log('\n3️⃣ 测试登录查询...');
    const [loginTest] = await connection.execute(
      `SELECT * FROM user 
       WHERE promoter_id = ? AND password = ? AND status = 1 AND user_type = 'promoter'`,
      ['1001', '123456']
    );
    
    if (loginTest.length > 0) {
      console.log('✅ 登录查询测试成功，用户可以登录');
      const user = loginTest[0];
      console.log(`   登录用户: ${user.username} (ID: ${user.promoter_id})`);
    } else {
      console.log('❌ 登录查询测试失败，用户无法登录');
      
      // 检查可能的问题
      const [statusCheck] = await connection.execute(
        `SELECT promoter_id, username, password, status, user_type 
         FROM user WHERE promoter_id = '1001'`
      );
      
      if (statusCheck.length > 0) {
        const user = statusCheck[0];
        console.log('用户存在但登录失败，检查原因:');
        console.log(`   密码匹配: ${user.password === '123456'}`);
        console.log(`   状态正常: ${user.status === 1}`);
        console.log(`   用户类型: ${user.user_type}`);
      }
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
  
  console.log('\n🎉 推广用户数据检查完成！');
}

// 运行检查
checkPromoterUsers().catch(console.error);
