{"name": "a-sokio-yun", "version": "0.0.0", "private": true, "scripts": {"start": "@powershell -Command $env:NODE_ENV = 'production';nodemon ./bin/ceshi", "dev": "@powershell -Command $env:NODE_ENV = 'development';nodemon ./bin/ceshi", "winDebug": "@powershell -Command $env:DEBUG='a-sokio-yun:*';$env:NODE_ENV = 'development';nodemon ./bin/ceshi", "macDebug": "DEBUG='a-sokio-yun:*';export NODE_ENV='development';nodemon ./bin/ceshi"}, "dependencies": {"axios": "^1.8.3", "body-parser": "^1.20.3", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "debug": "~2.6.9", "express": "~4.16.1", "express-session": "^1.17.3", "express-ws": "^5.0.2", "http-errors": "~1.6.3", "moment": "^2.30.1", "mongoose": "^8.1.1", "morgan": "~1.9.1", "multer": "^2.0.0", "multiparty": "^4.2.3", "mysql": "^2.18.1", "mysql2": "^3.14.1", "nodemon": "^3.1.7", "socks-proxy-agent": "^8.0.5"}, "description": "", "main": "app.js", "repository": {"type": "git", "url": "https://gitee.com/mcy-haha66/ec_yunkong_web.git"}, "keywords": [], "author": "", "license": "ISC"}