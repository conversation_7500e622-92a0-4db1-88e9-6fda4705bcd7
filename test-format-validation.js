const axios = require('axios');

// 测试修复后的代理地址格式验证
async function testFormatValidation() {
  console.log('🧪 测试修复后的代理地址格式验证...');
  
  const baseURL = 'http://localhost:15001';
  
  // 测试各种格式的代理地址
  const testCases = [
    {
      name: '标准IPv4格式',
      proxy: '127.0.0.1:1080',
      expectSuccess: true
    },
    {
      name: '域名格式',
      proxy: 'proxy.example.com:1080',
      expectSuccess: true
    },
    {
      name: 'localhost格式',
      proxy: 'localhost:1080',
      expectSuccess: true
    },
    {
      name: 'IPv6格式',
      proxy: '[::1]:1080',
      expectSuccess: true
    },
    {
      name: '带认证的格式',
      proxy: 'user:<EMAIL>:1080',
      expectSuccess: true
    },
    {
      name: '复杂域名',
      proxy: 'my-proxy.sub.domain.com:8080',
      expectSuccess: true
    },
    {
      name: '高端口号',
      proxy: '*************:65535',
      expectSuccess: true
    },
    {
      name: '空字符串',
      proxy: '',
      expectSuccess: false
    },
    {
      name: '只有主机名',
      proxy: 'localhost',
      expectSuccess: false
    },
    {
      name: '无效端口',
      proxy: 'localhost:abc',
      expectSuccess: false
    },
    {
      name: '端口超出范围',
      proxy: 'localhost:99999',
      expectSuccess: false
    },
    {
      name: '多个冒号但不是IPv6',
      proxy: 'host:with:colons:1080',
      expectSuccess: true  // 应该取最后一个冒号作为端口分隔符
    }
  ];
  
  console.log('\n📋 开始测试各种代理地址格式...\n');
  
  let successCount = 0;
  let totalCount = testCases.length;
  
  for (const testCase of testCases) {
    console.log(`🔍 测试: ${testCase.name}`);
    console.log(`   代理地址: "${testCase.proxy}"`);
    console.log(`   预期结果: ${testCase.expectSuccess ? '成功' : '失败'}`);
    
    try {
      const response = await axios.post(`${baseURL}/api/douyin/test-proxy-detection`, {
        sk5: testCase.proxy
      }, {
        timeout: 10000
      });
      
      // 检查是否是格式错误（而不是连接错误）
      const isFormatError = response.data.msg && (
        response.data.msg.includes('格式错误') ||
        response.data.msg.includes('格式无效') ||
        response.data.msg.includes('不能为空') ||
        response.data.msg.includes('端口号无效') ||
        response.data.msg.includes('认证信息不完整')
      );
      
      if (testCase.expectSuccess) {
        if (isFormatError) {
          console.log(`   ❌ 预期成功但格式验证失败: ${response.data.msg}`);
        } else {
          console.log(`   ✅ 格式验证通过 (${response.data.msg})`);
          successCount++;
        }
      } else {
        if (isFormatError) {
          console.log(`   ✅ 预期失败且正确失败: ${response.data.msg}`);
          successCount++;
        } else {
          console.log(`   ❌ 预期失败但格式验证通过`);
        }
      }
      
    } catch (error) {
      if (testCase.expectSuccess) {
        console.log(`   ❌ 预期成功但请求失败: ${error.message}`);
      } else {
        console.log(`   ✅ 预期失败且请求失败: ${error.message}`);
        successCount++;
      }
    }
    
    console.log(''); // 空行分隔
  }
  
  console.log(`🎉 测试完成！成功率: ${successCount}/${totalCount} (${Math.round(successCount/totalCount*100)}%)`);
  
  if (successCount === totalCount) {
    console.log('✅ 所有测试用例都通过了！代理地址格式验证修复成功。');
  } else {
    console.log('⚠️  部分测试用例失败，需要进一步调整。');
  }
  
  console.log('\n💡 现在可以在前端界面测试以下格式:');
  console.log('   - 127.0.0.1:1080');
  console.log('   - localhost:1080');
  console.log('   - proxy.example.com:8080');
  console.log('   - [::1]:1080 (IPv6)');
  console.log('   - user:<EMAIL>:1080 (带认证)');
}

// 运行测试
if (require.main === module) {
  testFormatValidation().catch(console.error);
}

module.exports = testFormatValidation;
