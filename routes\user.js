const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const debug = require('debug')('a-sokio-yun:server');
const userMod = require('../db/userMod');
const crypto = require('crypto');
const fs = require('fs');
const { localDate, layuiMenu, publicPath, ppxxxkkk, adminName, maxLevel } = require('../serverOpt');


//获取指定用户的角色菜单接口
router.get('/getOneUserMenu', async function (req, res) {
    debug("获取指定用户的角色菜单接口")
    const { userName, userId } = req.session;
    if (!userId) { return res.json({ code: -1, msg: "请先登录" }) }

    const userDoc = await userMod.findById(userId)
    if (!userDoc) {
        debug(`用户未找到(${userName})`)
        req.session.destroy();
        return res.end()
    }

    let newMenu = layuiMenu
    if (userName == adminName) {//超级管理员
        let menuStr = fs.readFileSync(publicPath + "/api/menu.json", 'utf-8')
        let menuJson = JSON.parse(menuStr)
        newMenu.menuInfo[0].child = menuJson
        return res.end(JSON.stringify(newMenu))
    } else {
        newMenu.menuInfo[0].child = userDoc.menuData
        res.end(JSON.stringify(newMenu))
    }
});

//添加用户接口
router.post('/addUser', async function (req, res) {

    //添加的时候不可以跨级添加,所以父账号一定是当前登录账号

    const { userName, userId, userLevel } = req.session;//父账号信息
    // debug("userLevel", userLevel)
    if (!userName || !userId) { return res.json({ code: -1, msg: '请先登录' }) }
    if (userLevel >= maxLevel) { return res.json({ code: -1, msg: '用户权限不足' }) }
    const reqBody = req.body;
    reqBody.creatTime = localDate();
    reqBody.parent = userId;
    reqBody.userPass = encrypt(reqBody.userPass);
    reqBody.userLevel = userLevel + 1
    reqBody.userDeviceNum = reqBody.userLevel >= maxLevel ? reqBody.maxDeviceNum : 0//本账号可用控数
    // debug("addUser reqBody", reqBody)

    debug(`父账号${userName}的占用控数`, reqBody.farterUserDeviceNu)
    debug(`父账号${userName}的可管理控数`, reqBody.farterMaxDeviceNum)

    let totalAmount = reqBody.farterUserDeviceNu;//当前父级账号的可用数量

    let resultArray = await userMod.aggregate([//查询本父账号已经开出的所有账号的设备数总和
        { $match: { parent: new mongoose.Types.ObjectId(userId) } },  // 匹配查询条件
        { $group: { _id: null, totalAmount: { $sum: "$maxDeviceNum" } } }  // 计算总和
    ])

    if (resultArray.length > 0) {
        totalAmount = totalAmount + resultArray[0].totalAmount;//加上当前父级账号下面所有子账号最大设备数总和
    }
    debug(`${userName} 开子账号前父账号的总开控数 ${totalAmount},总可控:${reqBody.farterMaxDeviceNum}`)
    totalAmount = totalAmount + reqBody.maxDeviceNum;//加上要开的本子账号的最大设备数量
    debug(`${userName} 开子账号后父账号的总开控数 ${totalAmount},总可控:${reqBody.farterMaxDeviceNum}`)
    if (totalAmount > reqBody.farterMaxDeviceNum) {
        return res.json({ code: -1, msg: `当前父账号(${userName}) 可开的控数不足,超出${totalAmount - reqBody.farterMaxDeviceNum}控` })
    }


    // let totalAmount = 0;
    // let childDocs = await userMod.find({ parent: userId })
    // if(childDocs && childDocs.length > 0){
    //     for(let i = 0; i < childDocs.length; i++){
    //         totalAmount = totalAmount + childDocs[i].maxDeviceNum;
    //     }
    // }
    // debug("fatherDoc", fatherDoc)

    try {
        await userMod.create(reqBody)
        debug(`[用户创建] 新用户创建成功: ${reqBody.userName}`)
        debug(`[用户创建] 注意: 新用户创建后需要单独调用 /indexDevice/assignDevice 接口分配设备`)
        res.json({ 
            code: 0, 
            msg: '添加用户成功',
            reminder: "请使用/indexDevice/assignDevice接口为新用户分配设备"
        })
    } catch (error) {
        console.error("添加用户错误", error);
        res.json({ code: -1, msg: `添加用户错误${error.message}请更换用户名` })
    }
})

router.post('/editUser', async function (req, res) {

    //编辑的时候可以跨级编辑,所以父账号不一定是当前登录账号

    const { userId, userLevel } = req.session;//父账号信息
    if (userLevel >= maxLevel) { return res.json({ code: -1, msg: '用户权限不足' }) }
    if (!userId) { return res.json({ code: -1, msg: '请先登录' }) }

    //下面的userName是子账号的userName
    const { userName, maxDeviceNum, beiZhu, checkStatus, touPing, menuData, oldMaxDeviceNum, parentId } = req.body;
    // console.log("editUser----", req.body);

    //通过子账号的parent的id获取当前账号的付帐号doc
    // let parentDoc = await userMod.findOne({ _id: parentId })
    let parentDoc = await userMod.findById(parentId)
    if (!parentDoc) {
        console.log(`未找到用户(${userName})的父级账户`)
        return res.json({ code: -1, msg: `未找到用户(${userName})的父级账户` })
    }
    // debug(parentId, parentDoc)

    debug(`父账号${parentDoc.userName}的占用控数`, parentDoc.userDeviceNum)
    debug(`父账号${parentDoc.userName}的可管理控数`, parentDoc.maxDeviceNum)
    let totalAmount = parentDoc.userDeviceNum;//当前父级账号的可用数量
    let resultArray = await userMod.aggregate([//查询本父账号已经开出的所有账号的设备数总和
        { $match: { parent: parentDoc._id } },  // 匹配查询条件
        { $group: { _id: null, totalAmount: { $sum: "$maxDeviceNum" } } }  // 计算总和
    ])
    if (resultArray.length > 0) {
        totalAmount = totalAmount + resultArray[0].totalAmount;//加上当前父级账号下面所有子账号最大设备数总和
    }
    debug(`${userName} 修改子账号前父账号的总开控数 ${totalAmount},总可控:${parentDoc.userDeviceNum}`)
    totalAmount = totalAmount - oldMaxDeviceNum + maxDeviceNum
    debug(`${userName} 修改子账号后父账号的总开控数 ${totalAmount},总可控:${parentDoc.userDeviceNum}`)
    if (totalAmount > parentDoc.maxDeviceNum) {
        return res.json({ code: -1, msg: `当前账号的父账号控数不足,超出${totalAmount - parentDoc.maxDeviceNum}控` })
    }

    try {
        await userMod.updateOne({ userName }, { maxDeviceNum, beiZhu, checkStatus, touPing, menuData })
        res.json({ code: 0, msg: '修改用户成功' })
    } catch (error) {
        console.error("修改用户错误", error);
        res.json({ code: -1, msg: '修改用户错误' + error.message })
    }
})

//修改本账号占用控数
router.post('/editKong', async function (req, res) {

    //编辑的时候可以跨级编辑,所以父账号不一定是当前登录账号

    const { userId, userLevel } = req.session;//父账号信息
    if (userLevel >= maxLevel) { return res.json({ code: -1, msg: '用户权限不足' }) }
    if (!userId) { return res.json({ code: -1, msg: '请先登录' }) }

    const { userName, _id, userDeviceNum, oldUserDeviceNum, maxDeviceNum } = req.body;

    debug(`用户(${userName})的占控要改为${userDeviceNum}`)
    let totalAmount = userDeviceNum
    let resultArray = await userMod.aggregate([//当前编辑账号的子账号所有最大设备数的和
        { $match: { parent: new mongoose.Types.ObjectId(_id) } },  // 匹配查询条件
        { $group: { _id: null, totalAmount: { $sum: "$maxDeviceNum" } } }  // 计算总和
    ])
    debug("resultArray", resultArray)
    if (resultArray.length > 0) {
        totalAmount = totalAmount + resultArray[0].totalAmount;//加上当前编辑账号的子账号所有最大设备数的和
    }
    debug(`本账号(${userName})以及子账号占控总数${totalAmount}`)
    if (totalAmount > maxDeviceNum) {
        return res.json({ code: -1, msg: `超出${totalAmount - maxDeviceNum}控,请再次修改` })
    }

    try {
        await userMod.updateOne({ _id }, { userDeviceNum })
        res.json({ code: 0, msg: '修改控数成功' })
    } catch (error) {
        console.error("修改用户错误", error);
        res.json({ code: -1, msg: '修改控数错误' + error.message })
    }
})


router.post('/changePass', async function (req, res) {
    const { userName, userId } = req.session;
    if (!userId) { return res.json({ code: -1, msg: '请先登录' }) }
    const reqBody = req.body;
    debug("userName", userName)
    debug("reqBody.userPass", reqBody.userPass)
    const userPass = encrypt(reqBody.userPass);

    try {
        await userMod.updateOne({ _id: userId }, { userPass })
        req.session.destroy();
        res.json({ code: 0, msg: '修改密码成功' })
    } catch (error) {
        console.error("修改密码错误", error);
        res.json({ code: -1, msg: '修改密码错误' + error.message })
    }
})


router.post('/delUser', async function (req, res) {
    const { userId } = req.session;
    if (!userId) { return res.json({ code: -1, msg: '请先登录' }) }
    //下面的_id是要删账号的_id
    const { _id } = req.body;
    debug("删除_id", _id)
    try {
        if (await userMod.findOne({ parent: _id })) {
            debug("此用户有子账号,禁止删除")
            return res.json({ code: -1, msg: '此用户有子账号,禁止删除' })
        }
        await userMod.deleteOne({ _id })
        debug("删除用户成功")
        res.json({ code: 0, msg: '删除用户成功' })
    } catch (error) {
        console.error("删除用户错误", error);
        res.json({ code: -1, msg: '删除用户错误' + error.message })
    }
})




//获取树形列表
router.get('/allUser', async function (req, res) {
    const { userName, userId } = req.session;
    if (!userId) { return res.json({ code: -1, msg: '请先登录' }) }
    let searchDic = {}
    const users = await userMod.find(searchDic).lean(); // 使用 lean() 以获取普通 JS 对象数组
    if (!users) { return res.json({ code: -1, msg: '没有账号信息' }) }
    
    console.log('===== 用户ID列表 =====');
    users.forEach(user => {
      console.log(`用户名: ${user.userName}, ID: ${user._id}`);
    });
    
    for (let user of users) {
        if (user._id.toString() === userId) {
            user.parent = "-1"; // 修改parent字段，不保存到数据库
            break
        }
    }
    // debug("修改后users", JSON.stringify(users, null, 2))
    res.json({
        code: 0,
        msg: "获取成功",
        count: users.length,
        data: users
    })
})

router.get('/touPing', function (req, res) {

})
router.get('/userLog', function (req, res) {
    res.json({
        code: -1,
        msg: "暂未开放",
    })
})


function encrypt(text) {
    const iv = crypto.randomBytes(16); // 初始化向量
    const cipher = crypto.createCipheriv('aes-256-cbc', ppxxxkkk, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return { iv: iv.toString('hex'), encryptedData: encrypted };
}

// 更新用户设备分配权限
router.post('/updateAssignPermission', async (req, res) => {
    const { userName: adminName } = req.session;
    if (adminName !== 'cjroot') {
        return res.json({ code: 1, msg: '只有管理员可以修改权限' });
    }

    const { userName, canAssignDevice } = req.body;
    if (!userName || typeof canAssignDevice !== 'boolean') {
        return res.json({ code: 1, msg: '参数错误' });
    }

    try {
        await userMod.updateOne(
            { userName },
            { $set: { canAssignDevice } }
        );
        res.json({ code: 0, msg: '权限更新成功' });
    } catch (err) {
        res.json({ code: 1, msg: '更新失败: ' + err.message });
    }
});

module.exports = router;