const express = require("express");
const router = express.Router();
const mongoose = require("../db/db-disabled.js"); // 使用禁用版本
const deviceShe = require("../db/deviceShe-disabled"); // 使用禁用版本
const groupShe = require("../db/groupShe-disabled"); // 使用禁用版本
const userMod = require("../db/userMod-disabled"); // 使用禁用版本
const debug = require("debug")("a-sokio-yun:server");

router.get("/", async function (req, res) {
  let userName = req.session.userName;
  if (!userName) {
    return res.json({
      msg: "请刷新浏览器重新登录",
    });
  }

  const userDoc = await userMod.findById(req.session.userId);
  let deviceMod;
  let query = req.query; //{ page, limit }
  let searchDic = {};

  // 根据账号类型选择设备集合
  if (userDoc.userName === "cjroot") {
    // 管理员查询主设备集合
    deviceMod = mongoose.model("device_cjroot", deviceShe);
    // 管理员查看所有设备，包括已分配和未分配
    if (query.taskStatus && query.taskStatus != "") {
      searchDic.taskStatus = query.taskStatus;
    }
  } else {
    // 子账号查询自己的设备集合
    deviceMod = mongoose.model(`device_${userDoc.userName}`, deviceShe);
    // 添加分配用户条件
    searchDic.assignedUser = req.session.userId;
    // 确保状态查询条件
    if (query.taskStatus && query.taskStatus != "") {
      searchDic.taskStatus = query.taskStatus;
    }
  }
  if (query.type == "arr") {
    // console.log(query.deviceName);
    // console.log(typeof (query.deviceName));
    if (query.deviceName && query.deviceName != "") {
      // searchDic.deviceName = { $in: query.deviceName }
      searchDic.deviceName = { $in: deviceArr(query.deviceName) };
    }
  } else {
    if (query.deviceName && query.deviceName != "") {
      // searchDic.deviceName = query.deviceName
      searchDic.deviceName = { $regex: query.deviceName };
    }
  }

  if (query.taskStatus && query.taskStatus != "") {
    searchDic.taskStatus = query.taskStatus;
  }

  // if (query.groupId && query.groupId != "") {
  //   searchDic.groupId = null//{ $exists: false }
  // }

  if (query.groupId) {
    if (query.groupId == "空的") {
      searchDic.groupId = null; //{ $exists: false }
    } else {
      searchDic.groupId = query.groupId;
    }
  }

  // 查询设备并填充分配用户信息
  let result = await deviceMod
    .find(searchDic)
    .populate({
      path: "assignedUsers",
      select: "userName",
      model: userMod,
    })
    .sort({ deviceName: 1 })
    .skip((query.page - 1) * query.limit)
    .limit(parseInt(query.limit) || 20)
    .lean()
    .exec();

  // 处理分配状态显示
  result = result.map((device) => {
    const assignedUsers = device.assignedUsers || [];
    const userNames = assignedUsers.map((u) => u.userName).filter(Boolean);
    const status = userNames.length > 0 ? userNames.join(", ") : "未分配";

    console.log(`设备 ${device.deviceName} 分配状态: ${status}`, {
      deviceId: device._id,
      assignedUsers: userNames,
    });

    return {
      ...device,
      assignmentStatus: status,
      assignedUsers: userNames, // 确保前端可以访问
    };
  });

  console.log("最终设备查询结果:", JSON.stringify(result, null, 2));

  console.log("===== 设备ID列表 =====");
  result.forEach((device) => {
    console.log(
      `设备名称: ${device.deviceName}, 分配状态: ${device.assignmentStatus}, ID: ${device._id}`
    );
  });
  res.json({
    code: 0,
    msg: "",
    count: result.length,
    data: result,
  });
});

/**
 * @api {post} /indexDevice/assignDevice 分配设备给子账号
 * @apiName AssignDevice
 * @apiGroup Device
 *
 * @apiDescription
 * 注意：
 * 1. 只能分配已存在的设备，不能通过此接口创建新设备
 * 2. 创建子用户时不会自动分配设备，需要单独调用此接口进行分配
 * 3. 超级管理员(cjroot)可以分配设备给任何子账号
 * 4. 设备必须先通过设备管理接口创建后才能分配
 *
 * @apiParam {String[]} deviceNames 设备名称数组(必须存在于设备列表中)
 * @apiParam {String[]} userNames 子账号用户名数组(必须已存在的账号)
 *
 * @apiSuccess {Number} code 0表示成功
 * @apiSuccess {String} msg 返回消息
 * @apiSuccess {Object} data 分配结果
 * @apiSuccess {String} data.deviceName 设备名称
 * @apiSuccess {String[]} data.assignedUsers 成功分配的用户名列表
 *
 * @apiExample 完整分配流程:
 * 1. 创建子账号(POST /user/addUser)
 * 2. 获取设备列表(GET /indexDevice)查看可用设备
 * 3. 获取用户列表(GET /user/allUser)查看可分配账号
 * 4. 调用本接口进行分配(POST /indexDevice/assignDevice)
 *
 * @apiExample 调用示例:
 * POST /indexDevice/assignDevice
 * {
 *   "deviceNames": ["001", "002"],
 *   "userNames": ["test01", "test02"]
 * }
 *
 * @apiExample 成功响应:
 * {
 *   "code": 0,
 *   "msg": "成功分配2个设备",
 *   "data": {
 *     "assignedDevices": ["001", "002"],
 *     "assignedUsers": ["test01", "test02"],
 *     "assignmentDetails": [
 *       {
 *         "deviceName": "001",
 *         "previousAssignments": ["oldUser"]
 *       },
 *       {
 *         "deviceName": "002",
 *         "previousAssignments": []
 *       }
 *     ]
 *   }
 * }
 *
 * @apiExample 前端调用示例:
 * // 使用jQuery实现多设备分配
 * function assignDevices() {
 *   const deviceNames = $('#device-select').val(); // 多选值
 *   const userNames = $('#user-select').val(); // 多选值
 *
 *   if (!deviceNames || deviceNames.length === 0 || !userNames || userNames.length === 0) {
 *     alert('请选择至少一个设备和至少一个账号');
 *     return;
 *   }
 *
 *   $.ajax({
 *     url: '/indexDevice/assignDevice',
 *     type: 'POST',
 *     contentType: 'application/json',
 *     data: JSON.stringify({
 *       deviceName: deviceName,
 *       userNames: userNames
 *     }),
 *     success: function(res) {
 *       if (res.code === 0) {
 *         alert(`成功将设备 ${res.data.deviceName} 分配给: ${res.data.assignedUsers.join(', ')}`);
 *         // 刷新设备列表
 *         loadDevices();
 *       } else {
 *         alert('分配失败: ' + res.msg);
 *       }
 *     },
 *     error: function(xhr) {
 *       alert('请求错误: ' + xhr.statusText);
 *     }
 *   });
 * }
 */
router.post("/assignDevice", async function (req, res) {
  try {
    console.log(
      "开始处理设备分配请求，请求体:",
      req.body ? JSON.stringify(req.body) : "空请求体"
    );

    // 1. 基础验证
    if (!req.body) {
      throw new Error("请求体不能为空");
    }

    // 检查会话状态
    if (!req.session) {
      throw new Error("会话无效或已过期");
    }

    const userName = req.session.userName;
    if (!userName) {
      throw new Error("请刷新浏览器重新登录");
    }

    const userDoc = await userMod.findById(req.session.userId).catch((err) => {
      console.error("查询用户失败:", err);
      throw new Error("查询用户信息时出错");
    });

    if (!userDoc) {
      throw new Error("用户不存在");
    }

    // 2. 权限验证
    if (userDoc.userName !== "cjroot" && !userDoc.canAssignDevice) {
      throw new Error("没有设备分配权限");
    }

    // 3. 参数解析和验证
    const body = typeof req.body === "string" ? JSON.parse(req.body) : req.body;
    if (!body.deviceNames && !body.deviceName) {
      throw new Error("缺少设备名称参数");
    }
    if (!body.userNames) {
      throw new Error("缺少用户名称参数");
    }

    const deviceNames = Array.isArray(body.deviceNames)
      ? body.deviceNames
      : body.deviceName
      ? [body.deviceName]
      : [];
    const userNames = Array.isArray(body.userNames)
      ? body.userNames
      : [body.userNames];

    console.log("解析后的参数 - 设备:", deviceNames, "用户:", userNames);

    // 4. 获取用户和设备数据
    const users = await userMod.find({ userName: { $in: userNames } });
    if (users.length !== userNames.length) {
      const missing = userNames.filter(
        (n) => !users.some((u) => u.userName === n)
      );
      throw new Error(`以下账号不存在: ${missing.join(",")}`);
    }

    const mainDeviceMod = mongoose.model("device_cjroot", deviceShe);
    const devices = await mainDeviceMod.find({
      deviceName: { $in: deviceNames },
    });
    if (devices.length !== deviceNames.length) {
      const missing = deviceNames.filter(
        (n) => !devices.some((d) => d.deviceName === n)
      );
      throw new Error(`以下设备不存在: ${missing.join(",")}`);
    }

    // 5. 准备批量操作
    const userIDs = users.map((u) => u._id);
    const bulkOps = devices.map((device) => ({
      updateOne: {
        filter: { deviceName: device.deviceName },
        update: {
          $set: {
            updateTime: new Date(),
            assignedUsers: userIDs,
          },
          $currentDate: {
            lastModified: true, // 添加最后修改时间戳
          },
        },
      },
    }));

    console.log("准备执行主设备集合更新，操作数:", bulkOps.length);
    const mainResult = await mainDeviceMod.bulkWrite(bulkOps);
    console.log("主设备集合更新完成，影响文档数:", mainResult.modifiedCount);

    // 6. 更新子账号设备集合
    const subResults = [];
    for (const user of users) {
      const subDeviceMod = mongoose.model(`device_${user.userName}`, deviceShe);
      const subBulkOps = devices.map((device) => ({
        updateOne: {
          filter: { deviceName: device.deviceName },
          update: {
            $set: {
              deviceName: device.deviceName,
              assignedUser: user._id,
              updateTime: new Date(),
              taskStatus: device.taskStatus, // 同步主集合的设备状态
            },
          },
          upsert: true,
        },
      }));

      console.log(
        `为用户 ${user.userName} 准备设备分配，操作数:`,
        subBulkOps.length
      );
      const result = await subDeviceMod.bulkWrite(subBulkOps);
      subResults.push({
        userName: user.userName,
        modifiedCount: result.modifiedCount,
        upsertedCount: result.upsertedCount,
      });
    }

    // 验证操作结果
    if (
      mainResult.modifiedCount === 0 &&
      subResults.every((r) => r.modifiedCount === 0)
    ) {
      throw new Error("设备分配失败，请检查设备状态");
    }

    console.log("设备分配完成", {
      mainModified: mainResult.modifiedCount,
      subModified: subResults.map((r) => r.modifiedCount),
    });

    // 7. 返回详细结果
    // 获取更新后的设备状态
    // 强制刷新并获取最新设备状态
    const updatedDevices = await mainDeviceMod
      .find({
        deviceName: { $in: devices.map((d) => d.deviceName) },
      })
      .select("deviceName taskStatus assignedUsers")
      .lean();

    console.log("分配后设备状态:", JSON.stringify(updatedDevices, null, 2));

    res.json({
      code: 0,
      msg: `成功分配${mainResult.modifiedCount}个设备给${users.length}个用户`,
      data: {
        deviceNames: devices.map((d) => d.deviceName),
        userNames: users.map((u) => u.userName),
        deviceStatus: updatedDevices.reduce((acc, device) => {
          const status = device.taskStatus || "已分配";
          console.log(`设备 ${device.deviceName} 状态: ${status}`);
          acc[device.deviceName] = status;
          return acc;
        }, {}),
        operationStats: {
          mainCollection: mainResult.modifiedCount,
          subCollections: subResults.map((r) => ({
            userName: r.userName,
            modified: r.modifiedCount,
          })),
        },
      },
    });
  } catch (error) {
    console.error("设备分配失败:", error);
    res.status(500).json({
      code: 1,
      msg: "设备分配失败，请检查参数或联系管理员",
      errorDetails:
        process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
});

//新增分组
router.post("/", async function (req, res) {
  let userName = req.session.userName;
  if (!userName) {
    return res.json({
      msg: "请刷新浏览器重新登录",
    });
  }
  let query = req.body; //{ page, limit }
  // console.log(query);
  let searchDic = { groupId: null }; //{ $exists: false }

  adddGroup(res, searchDic, query, userName);
});

async function adddGroup(res, searchDic, query, userName) {
  let deviceMod = mongoose.model("device_" + userName, deviceShe);
  let groupMod = mongoose.model("group_" + userName, groupShe);
  let checkData = JSON.parse(query.checkData);
  let groupName = query.groupName;
  let groupDoc = await groupMod.findOneAndUpdate(
    { groupName: groupName },
    { groupName: groupName },
    { upsert: true, new: true }
  );
  // console.log("新增或者刷新分组,组名:" + groupDoc.groupName)
  // console.log(checkData)
  for (let i = 0; i < checkData.length; i++) {
    await deviceMod.updateOne(
      { _id: checkData[i]._id },
      { groupId: groupDoc._id }
    );
  }
  resPcData(deviceMod, res, searchDic, query);
}

//从组内删除设备
router.get("/delDevice", async function (req, res) {
  let userName = req.session.userName;
  if (!userName) {
    return res.json({
      msg: "请刷新浏览器重新登录",
    });
  }
  let query = req.query;
  let deviceMod = mongoose.model("device_" + userName, deviceShe);
  await deviceMod.updateOne(
    { _id: query.deviceId },
    { groupId: null },
    { upsert: false }
  );
  asyncResPcData(res, query, deviceMod);
});

async function asyncResPcData(res, query, deviceMod) {
  let groupId = query.groupId;
  let findDic = { groupId: groupId };
  if (groupId == "空闲") {
    findDic = { taskStatus: "空闲" };
  } else if (groupId == "忙碌") {
    findDic = { taskStatus: "忙碌" };
  } else if (groupId == "全部") {
    findDic = {};
  }
  if (query.type == "arr") {
    // console.log(query.deviceName);
    // console.log(typeof (query.deviceName));
    if (query.deviceName && query.deviceName != "") {
      // findDic.deviceName = { $in: query.deviceName }
      findDic.deviceName = { $in: deviceArr(query.deviceName) };
    }
  } else {
    if (query.deviceName && query.deviceName != "") {
      // findDic.deviceName = query.deviceName
      findDic.deviceName = { $regex: query.deviceName };
    }
  }

  try {
    const devices = await deviceMod.find(findDic).sort({ deviceName: 1 });
    if (devices) {
      res.json({
        code: 0,
        msg: "",
        count: devices.length,
        data: devices,
      });
    }
  } catch (error) {
    res.json({
      msg: error.message,
      code: 1,
      count: 0,
      data: [],
    });
  }
}
//从设备列表删除设备
router.post("/delList", function (req, res) {
  //这里出现500报错
  let userName = req.session.userName;
  if (!userName) {
    return res.json({
      msg: "请刷新浏览器重新登录",
    });
  }

  res.locals.username = userName;
  let query = req.body;
  let devicesData = JSON.parse(query.devicesData);

  let daiLi_userName = query.daiLi_userName;
  if (daiLi_userName) {
    userName = daiLi_userName;
  }

  let deviceMod = mongoose.model("device_" + userName, deviceShe);
  //批量删除设备
  devicesData.forEach((devicesDic) => {
    deviceMod.deleteOne({ _id: devicesDic._id }).then();
  });

  resPcData(deviceMod, res, {}, query);
});

router.get("/groupDevice", function (req, res) {
  let userName = req.session.userName;
  if (!userName) {
    return res.json({
      msg: "请刷新浏览器重新登录",
    });
  }

  let deviceMod = mongoose.model("device_" + userName, deviceShe);
  let query = req.query; //{ page, limit }

  asyncResPcData(res, query, deviceMod);
});

async function resPcData(dbMod, res, serchDic, query) {
  try {
    const count = await dbMod.countDocuments(serchDic);
    const docs = await dbMod
      .find(serchDic)
      .sort({ deviceName: 1 })
      .skip((query.page - 1) * query.limit)
      .limit(parseInt(query.limit) || 20)
      .exec();
    //.sort({ 'ciLength': 1 })
    // console.log("docs", docs);
    res.json({
      code: 0,
      msg: "",
      count: count,
      data: docs,
    });
  } catch (error) {
    res.json({
      msg: "错误:" + error.message,
    });
  }
}

function deviceArr(groupRule) {
  let device_arr = [];
  groupRule.split(",").forEach((element) => {
    if (element.indexOf("-") > -1) {
      let index_min_max = element.split("-");
      var qianZui = index_min_max[0].match(/^[^\d]*[0]*/);
      if (qianZui) {
        console.log(qianZui);
        qianZui = qianZui[0];
      } else {
        qianZui = "";
      }
      let index_min = parseInt(index_min_max[0].match(/\d+/)[0]);

      let index_max = parseInt(index_min_max[1].match(/\d+/)[0]);
      //看下去前缀和0后的最小数的位数
      let count_initMin = index_min.toString().length;

      for (let i = index_min; i <= index_max; i++) {
        //每增加一位数,减少一个前缀0
        let len_add = i.toString().length - count_initMin;
        let str0 = "";
        let newQianZui = qianZui;
        for (let j = 0; j < len_add; j++) {
          newQianZui = newQianZui.replace("0", "");
        }
        device_arr.push(newQianZui + i);
      }
    } else {
      device_arr.push(element);
    }
  });
  // console.log(device_arr)
  return device_arr;
}

module.exports = router;
