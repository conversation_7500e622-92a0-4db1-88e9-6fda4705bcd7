#!/bin/bash

# 推广操作数据保存API - cURL使用示例

API_BASE_URL="http://localhost:15001"

echo "🚀 推广操作数据保存API - cURL示例"
echo "=================================="

# 1. 单条数据保存 - 扫码操作
echo ""
echo "1. 保存扫码操作:"
curl -X POST "${API_BASE_URL}/api/promotion-actions/save" \
  -H "Content-Type: application/json" \
  -d '{
    "promotion_user_id": "1001",
    "action_type": "scan",
    "ip_address": "*************",
    "ip_province": "北京"
  }' | jq '.'

# 2. 单条数据保存 - 登录成功
echo ""
echo "2. 保存登录成功操作:"
curl -X POST "${API_BASE_URL}/api/promotion-actions/save" \
  -H "Content-Type: application/json" \
  -d '{
    "promotion_user_id": "1001",
    "action_type": "login_success",
    "ip_address": "*************",
    "ip_province": "北京",
    "douyin_name": "张三",
    "douyin_id": "zhangsan123",
    "ck_data": "sessionid=abc123456",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    "extra_data": {
      "device_type": "mobile",
      "browser": "chrome",
      "timestamp": 1703123456789
    }
  }' | jq '.'

# 3. 单条数据保存 - 登录失败
echo ""
echo "3. 保存登录失败操作:"
curl -X POST "${API_BASE_URL}/api/promotion-actions/save" \
  -H "Content-Type: application/json" \
  -d '{
    "promotion_user_id": "1002",
    "action_type": "login_fail",
    "ip_address": "*************",
    "ip_province": "上海",
    "extra_data": {
      "fail_reason": "密码错误"
    }
  }' | jq '.'

# 4. 单条数据保存 - 请求过期
echo ""
echo "4. 保存请求过期操作:"
curl -X POST "${API_BASE_URL}/api/promotion-actions/save" \
  -H "Content-Type: application/json" \
  -d '{
    "promotion_user_id": "1003",
    "action_type": "request_expire",
    "ip_address": "*************",
    "ip_province": "广东"
  }' | jq '.'

# 5. 批量数据保存
echo ""
echo "5. 批量保存操作:"
curl -X POST "${API_BASE_URL}/api/promotion-actions/batch-save" \
  -H "Content-Type: application/json" \
  -d '{
    "actions": [
      {
        "promotion_user_id": "BATCH001",
        "action_type": "scan",
        "ip_address": "*************",
        "ip_province": "浙江"
      },
      {
        "promotion_user_id": "BATCH001",
        "action_type": "login_success",
        "ip_address": "*************",
        "ip_province": "浙江",
        "douyin_name": "批量用户1",
        "douyin_id": "batch001",
        "ck_data": "sessionid=batch123"
      },
      {
        "promotion_user_id": "BATCH002",
        "action_type": "login_fail",
        "ip_address": "*************",
        "ip_province": "江苏"
      }
    ]
  }' | jq '.'

# 6. 测试参数验证 - 缺少必填字段
echo ""
echo "6. 测试参数验证 - 缺少必填字段:"
curl -X POST "${API_BASE_URL}/api/promotion-actions/save" \
  -H "Content-Type: application/json" \
  -d '{
    "promotion_user_id": "TEST001"
  }' | jq '.'

# 7. 测试参数验证 - 无效操作类型
echo ""
echo "7. 测试参数验证 - 无效操作类型:"
curl -X POST "${API_BASE_URL}/api/promotion-actions/save" \
  -H "Content-Type: application/json" \
  -d '{
    "promotion_user_id": "TEST002",
    "action_type": "invalid_action",
    "ip_address": "*************"
  }' | jq '.'

# 8. 测试参数验证 - 无效IP地址
echo ""
echo "8. 测试参数验证 - 无效IP地址:"
curl -X POST "${API_BASE_URL}/api/promotion-actions/save" \
  -H "Content-Type: application/json" \
  -d '{
    "promotion_user_id": "TEST003",
    "action_type": "scan",
    "ip_address": "999.999.999.999"
  }' | jq '.'

echo ""
echo "✅ 所有示例执行完成！"
