const axios = require('axios');

async function testNoProxy() {
    console.log('🧪 测试无可用账号的API返回\n');

    try {
        // 1. 测试不存在的省份（单个代理）
        console.log('1. 测试获取不存在省份的单个代理:');
        const response1 = await axios.get('http://localhost:15001/api/douyin/get-proxy-by-province?province=不存在的省份');
        console.log('响应:', JSON.stringify(response1.data, null, 2));

        // 2. 测试批量获取不存在的省份
        console.log('\n2. 测试批量获取不存在省份的代理:');
        const response2 = await axios.get('http://localhost:15001/api/douyin/get-proxies-by-province?province=不存在的省份&limit=3');
        console.log('响应:', JSON.stringify(response2.data, null, 2));

        // 3. 测试可能无代理的省份
        console.log('\n3. 测试西藏省份:');
        const response3 = await axios.get('http://localhost:15001/api/douyin/get-proxy-by-province?province=西藏');
        console.log('响应:', JSON.stringify(response3.data, null, 2));

        console.log('\n✅ 测试完成！');

    } catch (error) {
        console.error('❌ 错误:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testNoProxy();
