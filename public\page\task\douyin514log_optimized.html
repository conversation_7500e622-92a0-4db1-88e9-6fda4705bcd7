<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>设备日志(优化版)</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .log-container {
            margin-top: 20px;
        }
        .log-header {
            display: flex;
            padding: 12px 15px;
            background-color: #f2f2f2;
            font-weight: bold;
            border-bottom: 2px solid #ddd;
        }
        .log-header-item {
            flex: 1;
            min-width: 0;
            padding: 0 5px;
        }
        .log-header-item.id {
            flex: 0 0 150px;
        }
        .log-header-item.status {
            flex: 0 0 120px;
        }
        .log-header-item.time {
            flex: 0 0 180px;
        }
        .log-header-item.action {
            flex: 0 0 100px;
            text-align: right;
        }
        .device-list {
            padding: 0;
        }
        .device-item {
            display: flex;
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            align-items: center;
        }
        .device-field {
            flex: 1;
            min-width: 0;
            padding: 0 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .device-field.id {
            flex: 0 0 150px;
            font-weight: bold;
        }
        .device-field.status {
            flex: 0 0 120px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .status-icon {
            margin-right: 5px;
            font-size: 14px;
        }
        .status-running {
            color: #009688;
        }
        .status-pending {
            color: #FFB800;
        }
        .status-error {
            color: #FF5722;
        }
        .status-success {
            color: #1E9FFF;
        }
        .status-completed {
            color: #5FB878;
        }
        .status-failed {
            color: #FF5722;
        }
        .device-field.time {
            flex: 0 0 180px;
            color: #666;
        }
        .device-field.latest {
            flex: 2;
            color: #666;
        }
        .device-field.action {
            flex: 0 0 100px;
            text-align: right;
        }
        .history-modal {
            padding: 20px;
        }
        .history-item {
            padding: 8px 0;
            border-bottom: 1px dashed #eee;
        }
        .history-item:last-child {
            border-bottom: none;
        }
        .history-time {
            color: #999;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h2>设备日志</h2>
                </div>
                <div class="layui-card-body">
                    <div class="log-header">
                        <div class="log-header-item id">设备ID</div>
                        <div class="log-header-item status">状态</div>
                        <div class="log-header-item">最新记录</div>
                        <div class="log-header-item time">更新时间</div>
                        <div class="log-header-item action">操作</div>
                    </div>
                    <ul class="device-list" id="deviceList">
                        <!-- 设备列表将通过JS动态加载 -->
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div id="historyModal" style="display: none; padding: 20px;">
        <div class="layui-card">
            <div class="layui-card-header" id="modalTitle">设备历史记录</div>
            <div class="layui-card-body">
                <div class="history-modal" id="historyContent">
                    <!-- 历史记录内容将通过JS动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script src="/lib/jquery-3.4.1/jquery-3.4.1.min.js"></script>
    <script src="/lib/layui/layui.js"></script>
    <script>
        layui.use(['jquery', 'layer'], function(){
            var $ = layui.$;
            var layer = layui.layer;
            
            // 加载日志数据
            function loadLogs() {
                layer.load(1);
                $.ajax({
                    url: '/wsRouter/mylogs',
                    type: 'GET',
                    dataType: 'json',
                    success: function(res) {
                        layer.closeAll('loading');
                        console.log('接口返回数据:', res);
                        if(res.code === 0 && res.data && res.data.list) {
                            renderDeviceList(res.data.list);
                        } else {
                            layer.msg(res.msg || '数据加载失败', {icon: 2});
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.closeAll('loading');
                        layer.msg('请求失败: ' + error, {icon: 2});
                        console.error('请求失败:', xhr, status, error);
                    }
                });
            }
            
            // 渲染设备列表
            function renderDeviceList(logs) {
                var container = $('#deviceList');
                container.empty();
                
                // 按设备ID分组
                var deviceGroups = {};
                logs.forEach(function(log) {
                    if(!deviceGroups[log.device_id]) {
                        deviceGroups[log.device_id] = [];
                    }
                    deviceGroups[log.device_id].push(log);
                });
                
                // 渲染每个设备
                for(var deviceId in deviceGroups) {
                    var logs = deviceGroups[deviceId];
                    // 按时间降序排序
                    logs.sort(function(a, b) {
                        return new Date(b.created_at) - new Date(a.created_at);
                    });
                    
                    var latestLog = logs[0];
                    var statusClass = getStatusClass(latestLog.status);
                    
                    // 创建设备项
                    var item = $(`
                        <li class="device-item">
                            <div class="device-field id">${deviceId}</div>
                            <div class="device-field status ${statusClass}">
                                <i class="layui-icon status-icon">${getStatusIcon(latestLog.status)}</i>
                                ${getStatusText(latestLog.status)}
                            </div>
                            <div class="device-field latest">${latestLog.execution_data || '无'}</div>
                            <div class="device-field time">${formatTime(latestLog.created_at)}</div>
                            <div class="device-field action">
                                <button class="layui-btn layui-btn-xs view-history" data-device-id="${deviceId}">历史</button>
                            </div>
                        </li>
                        </li>
                    `);
                    
                    // 存储设备日志数据到data属性
                    item.find('.view-history').data('logs', logs);
                    
                    container.append(item);
                }
            }
            
            // 使用事件委托绑定查看历史按钮
            $(document).on('click', '.view-history', function(){
                var $btn = $(this);
                var deviceId = $btn.data('device-id');
                var logs = $btn.data('logs');
                showHistoryModal(deviceId, logs);
            });

            // 显示历史记录模态框
            function showHistoryModal(deviceId, logs) {
                console.log('显示设备历史记录:', deviceId, logs);
                var content = $('#historyContent');
                content.empty();
                
                // 验证日志数据
                if(!Array.isArray(logs)) {
                    console.error('日志数据格式错误:', logs);
                    content.append('<div class="layui-red">日志数据格式错误</div>');
                    return;
                }

                // 设置模态框标题
                $('#modalTitle').text(`设备 ${deviceId} 的历史记录 (共${logs.length}条)`);
                
                // 显示所有历史记录
                logs.forEach(function(log) {
                    try {
                        var statusClass = getStatusClass(log.status);
                        var execData = typeof log.execution_data === 'string' ? 
                            log.execution_data : 
                            JSON.stringify(log.execution_data);
                            
                        content.append(`
                            <div class="history-item">
                                <div>
                                    <span class="device-status ${statusClass}">[${getStatusText(log.status)}]</span>
                                    ${execData || '无'}
                                </div>
                                <div class="history-time">${formatTime(log.created_at)}</div>
                            </div>
                        `);
                    } catch(e) {
                        console.error('渲染日志记录出错:', e);
                        content.append(`
                            <div class="history-item layui-bg-red">
                                记录解析错误: ${e.message}
                            </div>
                        `);
                    }
                });
                
                // 显示模态框
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: 1,
                    area: ['800px', '600px'],
                    shadeClose: true,
                    content: $('#historyModal'),
                    success: function(layero, index){
                        // 自动调整模态框高度
                        var $content = layero.find('.history-modal');
                        var height = $content.height() + 100;
                        layer.style(index, {
                            height: Math.min(height, $(window).height() - 50) + 'px'
                        });
                    }
                });
            }
            
            // 获取状态对应的CSS类
            // 状态映射为中文
            function getStatusText(status) {
                const statusMap = {
                    'running': '运行中',
                    'pending': '待处理', 
                    'error': '错误',
                    'success': '成功',
                    'failed': '失败',
                    'completed': '已完成'
                };
                return statusMap[status] || status;
            }

            function getStatusIcon(status) {
                const iconMap = {
                    'running': '&#xe6cf;',
                    'pending': '&#xe63d;',
                    'error': '&#xe69c;',
                    'success': '&#xe605;',
                    'completed': '&#xe605;',
                    'failed': '&#x1006;'
                };
                return iconMap[status] || '&#xe60b;';
            }

            function getStatusClass(status) {
                if(status === 'running') return 'status-running';
                if(status === 'pending') return 'status-pending';
                if(status === 'error') return 'status-error';
                if(status === 'success') return 'status-success';
                if(status === 'completed') return 'status-completed';
                if(status === 'failed') return 'status-failed';
                return '';
            }
            
            // 格式化时间
            function formatTime(timestamp) {
                if(!timestamp) return '未知';
                var date = new Date(timestamp);
                return date.toLocaleString();
            }
            
            // 初始化
            // 初始加载日志
            loadLogs();
            
            // 设置30秒自动刷新
            var refreshTimer = setInterval(loadLogs, 30000);
            
            // 页面卸载时清除定时器
            $(window).on('beforeunload', function() {
                clearInterval(refreshTimer);
            });
            
            // 错误处理
            $(document).ajaxError(function(event, xhr, settings, error) {
                layer.msg('加载失败: ' + error, {icon: 2});
            });

        });
        
        // 全局变量存储定时器
        var refreshInterval = 10; // 30秒
    </script>
</body>
</html>
