<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }

        .success {
            color: green;
        }

        .error {
            color: red;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        button:hover {
            background: #005a87;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 15px 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007cba;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>

<body>
    <h1>🧪 推广用户功能测试</h1>

    <div class="section">
        <div class="title">1. 登录测试</div>
        <button onclick="testLogin()">登录测试用户</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <div class="title">2. 统计数据展示</div>
        <button onclick="loadStats()">加载统计数据</button>
        <div id="statsResult" class="result"></div>

        <div class="stats-grid" id="statsGrid" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="visitCount">0</div>
                <div class="stat-label">访问次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uniqueIpCount">0</div>
                <div class="stat-label">独立IP数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="scanCount">0</div>
                <div class="stat-label">扫码数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successCount">0</div>
                <div class="stat-label">成功数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failCount">0</div>
                <div class="stat-label">失败数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="expireCount">0</div>
                <div class="stat-label">过期数量</div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="title">3. Session测试</div>
        <button onclick="testSession()">测试Session状态</button>
        <div id="sessionResult" class="result"></div>
    </div>

    <div class="section">
        <div class="title">4. 完整流程测试</div>
        <button onclick="fullTest()">完整流程测试</button>
        <div id="fullTestResult" class="result"></div>
    </div>

    <script>
        // 测试用户
        const testUser = { user_id: '1001', password: '123456' };

        // 通用请求函数
        async function makeRequest(url, options = {}) {
            const defaultOptions = {
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };

            const finalOptions = { ...defaultOptions, ...options };

            try {
                const response = await fetch(url, finalOptions);
                const data = await response.json();

                return { success: true, status: response.status, data: data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 1. 登录测试
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '🔄 正在登录...';

            const result = await makeRequest('/api/promotion/promoter/login', {
                method: 'POST',
                body: JSON.stringify(testUser)
            });

            if (result.success && result.data.code === 0) {
                resultDiv.innerHTML = `<div class="success">✅ 登录成功: ${result.data.data.username}</div>`;
                return true;
            } else {
                resultDiv.innerHTML = `<div class="error">❌ 登录失败: ${result.data?.msg || result.error}</div>`;
                return false;
            }
        }

        // 2. 加载统计数据
        async function loadStats() {
            const resultDiv = document.getElementById('statsResult');
            const statsGrid = document.getElementById('statsGrid');

            resultDiv.innerHTML = '🔄 正在加载统计数据...';

            const result = await makeRequest('/api/promotion/promoter/today-stats');

            if (result.success && result.data.code === 0) {
                const stats = result.data.data;

                // 更新显示
                document.getElementById('visitCount').textContent = stats.visit_count;
                document.getElementById('uniqueIpCount').textContent = stats.unique_ip_count;
                document.getElementById('scanCount').textContent = stats.scan_count;
                document.getElementById('successCount').textContent = stats.success_count;
                document.getElementById('failCount').textContent = stats.fail_count;
                document.getElementById('expireCount').textContent = stats.expire_count;

                statsGrid.style.display = 'grid';

                const hasData = Object.values(stats).some(val => val > 0);
                resultDiv.innerHTML = `<div class="${hasData ? 'success' : 'error'}">
                    ${hasData ? '✅ 统计数据加载成功' : '⚠️ 所有数据都为0'}
                </div>`;

                console.log('统计数据:', stats);

            } else {
                resultDiv.innerHTML = `<div class="error">❌ 加载失败: ${result.data?.msg || result.error}</div>`;
                statsGrid.style.display = 'none';
            }
        }

        // 3. 测试Session状态
        async function testSession() {
            const resultDiv = document.getElementById('sessionResult');
            resultDiv.innerHTML = '🔄 正在测试Session状态...';

            const result = await makeRequest('/api/promotion/promoter/session-test');

            if (result.success && result.data.code === 0) {
                const sessionData = result.data.data;

                resultDiv.innerHTML = `
                    <div class="${sessionData.hasPromotionUser ? 'success' : 'error'}">
                        ${sessionData.hasPromotionUser ? '✅ Session有效' : '❌ Session无效'}
                    </div>
                    <div><strong>Session ID:</strong> ${sessionData.sessionId}</div>
                    <div><strong>有Session:</strong> ${sessionData.hasSession}</div>
                    <div><strong>有推广用户:</strong> ${sessionData.hasPromotionUser}</div>
                    <div><strong>推广用户:</strong> ${JSON.stringify(sessionData.promotionUser)}</div>
                    <div><strong>Cookies:</strong> ${sessionData.cookies || '无'}</div>
                `;

                console.log('Session状态:', sessionData);

            } else {
                resultDiv.innerHTML = `<div class="error">❌ Session测试失败: ${result.data?.msg || result.error}</div>`;
            }
        }

        // 4. 完整流程测试
        async function fullTest() {
            const resultDiv = document.getElementById('fullTestResult');
            let log = '';

            resultDiv.innerHTML = '🔄 开始完整流程测试...<br>';

            // 步骤1: 登录
            log += '1️⃣ 登录...<br>';
            const loginSuccess = await testLogin();

            if (loginSuccess) {
                log += '✅ 登录成功<br>';

                // 等待一下确保session生效
                await new Promise(resolve => setTimeout(resolve, 500));

                // 步骤2: 测试Session
                log += '2️⃣ 测试Session状态...<br>';
                await testSession();

                // 步骤3: 加载统计数据
                log += '3️⃣ 加载统计数据...<br>';
                await loadStats();

                // 检查数据是否正确显示
                const visitCount = document.getElementById('visitCount').textContent;
                const successCount = document.getElementById('successCount').textContent;

                if (visitCount !== '0' || successCount !== '0') {
                    log += `✅ 数据显示正确: 访问=${visitCount}, 成功=${successCount}<br>`;
                } else {
                    log += '❌ 数据仍然显示为0<br>';
                }

                log += '4️⃣ 测试用户信息...<br>';
                const userResult = await makeRequest('/api/promotion/promoter/user-info');
                if (userResult.success && userResult.data.code === 0) {
                    log += `✅ 用户信息: ${userResult.data.data.username}<br>`;
                } else {
                    log += `❌ 用户信息获取失败: ${userResult.data?.msg}<br>`;
                }

            } else {
                log += '❌ 登录失败，无法继续测试<br>';
            }

            resultDiv.innerHTML = log;
        }

        // 页面加载时的提示
        window.onload = function () {
            console.log('🧪 推广用户测试页面已加载');
            console.log('请点击"完整流程测试"按钮开始测试');
        };
    </script>
</body>

</html>