const { mongoose } = require('./db.js');
const { Schema } = mongoose;

let userSchema = new Schema({
    userName: {
        type: String,    //数据类型
        required: true,  //是否必传
        index: { unique: true }    //是否唯一,并且加索引
    },
    userPass: {
        type: Object,
        required: true//,  //是否必传
    },
    maxDeviceNum: Number,//本代理最大控数
    userDeviceNum: Number,//本账号可用控数
    parent: { type: mongoose.Schema.Types.ObjectId, ref: 'userMod' }, // 引用父账号
    userLevel: Number,//0是不开代理,1是开一级代理,2是开二级代理,以此类推
    beiZhu: String,
    checkStatus: Boolean,//是否审核通过
    touPing: Boolean,//1开启投屏,0或其他关闭投屏
    menuData: mongoose.Schema.Types.Mixed, // 存储菜单结构的字段
    creatTime: Date,//加入时间
    canAssignDevice: {  // 新增设备分配权限字段
        type: Boolean,
        default: false
    }
})


let userMod = mongoose.model('userMod', userSchema)

module.exports = userMod;

