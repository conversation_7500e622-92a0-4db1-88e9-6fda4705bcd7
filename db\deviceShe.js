const mongoose = require('./db.js');

//2.创建约束对象Schema(定义结构),每个Schema对应一个collection(集合)
let deviceShe = new mongoose.Schema({
    deviceName: {
        type: String,
        index: { unique: true }    //是否唯一,并且加索引
    },
    socketId: {
        type: String,
        index: true     //是否唯一,并且加索引
    },
    device_width: Number,
    device_height: Number,
    device_UUID: String,
    taskStatus: {
        type: String,
        default: "空闲",
        index: true    //加索引
    },
    deviceMsg: {
        type: String,
        default: "设备上线了"
    },
    taskName: {
        type: String,
        default: "无任务"
    },
    groupId: {
        type: mongoose.SchemaTypes.ObjectId,
        ref: "groupMod"
    },//可以是数组--数组就是可以被多次分组

    // updateTime: {//更新时间
    //     type: Date,
    //     default: Date.now(),
    //     index: { expires: '1d' }//30天后删除设备// expires: 30 * 24 * 60 * 60
    // },

    updateTime: String,
    assignedUsers: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'userMod'
    }],
    assignedUser: { // 保持向后兼容
        type: mongoose.Schema.Types.ObjectId,
        ref: 'userMod'
    }
})



module.exports = deviceShe;

// 展示设备得分组情况
// deviceMod.find({})
//   .populate('groupId') // 填充分组信息
//   .exec(function (err, devices) {
//     // 处理结果
//   });

//查询指定分组得所有设备,并填充分组消息
// Device.find({ groupId: groupId })
//     .populate('groupId') // 填充分组信息
//     .exec(function (err, devices) {
//         // 处理结果
//     });
