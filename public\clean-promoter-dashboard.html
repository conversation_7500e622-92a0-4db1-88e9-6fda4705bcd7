<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户仪表板（干净版）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title {
            font-size: 24px;
            font-weight: bold;
        }
        
        .header-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .date-info {
            background: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .actions-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e6e6e6;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-scan {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }
        
        .error {
            text-align: center;
            padding: 40px;
            color: #dc3545;
        }
        
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .container {
                padding: 15px;
            }
        }
        
        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <div class="header-content">
            <div class="header-title">推广用户仪表板（干净版）</div>
            <div class="header-info">
                <div class="user-info" id="userInfo">加载中...</div>
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
        <!-- 日期信息 -->
        <div class="date-info">
            <strong>今日日期：</strong><span id="todayDate"></span>
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="visitCount">0</div>
                <div class="stat-label">访问次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uniqueIpCount">0</div>
                <div class="stat-label">独立IP数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="scanCount">0</div>
                <div class="stat-label">扫码数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successCount">0</div>
                <div class="stat-label">成功数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failCount">0</div>
                <div class="stat-label">失败数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="expireCount">0</div>
                <div class="stat-label">过期数量</div>
            </div>
        </div>

        <!-- 操作记录 -->
        <div class="actions-section">
            <div class="section-title">今日操作记录</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>用户ID</th>
                        <th>时间</th>
                        <th>IP地址</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody id="actionTableBody">
                    <tr>
                        <td colspan="4" class="loading">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 调试日志 -->
        <div class="debug-log" id="debugLog">调试日志：\n</div>
    </div>

    <script>
        // 完全不依赖任何外部库的纯净JavaScript
        
        const debugLog = document.getElementById('debugLog');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            
            debugLog.textContent += logMessage;
            debugLog.scrollTop = debugLog.scrollHeight;
            
            // 同时输出到控制台
            console.log(`${prefix} ${message}`);
        }
        
        // 监听所有网络请求（检测恶意请求）
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            addLog(`网络请求: ${url}`, 'info');
            
            // 检查是否是外部恶意请求
            if (typeof url === 'string' && (url.includes('summer5188.com') || url.includes('dh.summer5188.com'))) {
                addLog(`🚨 阻止恶意请求: ${url}`, 'warning');
                return Promise.reject(new Error('恶意请求已被阻止'));
            }
            
            return originalFetch.apply(this, args);
        };
        
        // 通用请求函数
        async function makeRequest(url, options = {}) {
            const defaultOptions = {
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            try {
                addLog(`发送请求: ${url}`, 'info');
                const response = await fetch(url, finalOptions);
                const data = await response.json();
                
                addLog(`收到响应: ${url} - 状态${response.status}`, 'success');
                return { success: true, status: response.status, data: data };
            } catch (error) {
                addLog(`请求失败: ${url} - ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }
        
        // 加载用户信息
        async function loadUserInfo() {
            addLog('开始加载用户信息...', 'info');
            const result = await makeRequest('/api/promotion/promoter/user-info');
            
            if (result.success && result.data.code === 0) {
                const userInfo = result.data.data;
                document.getElementById('userInfo').textContent =
                    `推广用户: ${userInfo.username} (ID: ${userInfo.user_id})`;
                addLog('用户信息加载成功', 'success');
            } else if (result.data?.code === -1 && result.data?.msg?.includes('登录')) {
                addLog('用户未登录，跳转到登录页', 'warning');
                window.location.href = '/promoter-login?error=' + encodeURIComponent('请先登录');
            } else {
                addLog('获取用户信息失败', 'error');
                document.getElementById('userInfo').textContent = '获取用户信息失败';
            }
        }
        
        // 加载统计数据
        async function loadStats() {
            addLog('开始加载统计数据...', 'info');
            const result = await makeRequest('/api/promotion/promoter/today-stats');
            
            if (result.success && result.data.code === 0) {
                const stats = result.data.data;
                
                addLog(`统计数据: 访问=${stats.visit_count}, 成功=${stats.success_count}`, 'info');
                
                // 更新显示
                document.getElementById('visitCount').textContent = stats.visit_count || 0;
                document.getElementById('uniqueIpCount').textContent = stats.unique_ip_count || 0;
                document.getElementById('scanCount').textContent = stats.scan_count || 0;
                document.getElementById('successCount').textContent = stats.success_count || 0;
                document.getElementById('failCount').textContent = stats.fail_count || 0;
                document.getElementById('expireCount').textContent = stats.expire_count || 0;
                
                // 检查数据是否为0
                const hasData = Object.values(stats).some(val => val > 0);
                if (hasData) {
                    addLog('统计数据加载成功，有非零数据', 'success');
                } else {
                    addLog('统计数据全部为0', 'warning');
                }
                
            } else if (result.data?.code === -1 && result.data?.msg?.includes('登录')) {
                addLog('用户未登录，跳转到登录页', 'warning');
                window.location.href = '/promoter-login?error=' + encodeURIComponent('请先登录');
            } else {
                addLog('获取统计数据失败', 'error');
            }
        }
        
        // 加载操作记录
        async function loadActions() {
            addLog('开始加载操作记录...', 'info');
            const result = await makeRequest('/api/promotion/promoter/today-actions?page=1&limit=10');
            
            if (result.success && result.data.code === 0) {
                const data = result.data.data;
                renderActionTable(data.list);
                addLog(`操作记录加载成功: ${data.list.length}条`, 'success');
            } else if (result.data?.code === -1 && result.data?.msg?.includes('登录')) {
                addLog('用户未登录，跳转到登录页', 'warning');
                window.location.href = '/promoter-login?error=' + encodeURIComponent('请先登录');
            } else {
                document.getElementById('actionTableBody').innerHTML =
                    '<tr><td colspan="4" class="error">加载失败</td></tr>';
                addLog('操作记录加载失败', 'error');
            }
        }
        
        // 渲染操作记录表格
        function renderActionTable(list) {
            const tbody = document.getElementById('actionTableBody');
            
            if (list.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 40px; color: #999;">暂无数据</td></tr>';
                return;
            }
            
            const html = list.map(item => `
                <tr>
                    <td>${item.user_id || '-'}</td>
                    <td>${item.time}</td>
                    <td>${item.ip}</td>
                    <td>
                        <span class="status-badge ${getStatusClass(item.status)}">${item.status}</span>
                    </td>
                </tr>
            `).join('');
            
            tbody.innerHTML = html;
        }
        
        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case '登录成功': return 'status-success';
                case '登录失败': return 'status-fail';
                case '请求过期': return 'status-fail';
                case '扫码': return 'status-scan';
                default: return '';
            }
        }
        
        // 退出登录
        async function logout() {
            if (!confirm('确定要退出登录吗？')) return;
            
            addLog('执行退出登录...', 'info');
            const result = await makeRequest('/api/promotion/promoter/logout', { method: 'POST' });
            
            addLog('已退出登录，跳转到登录页', 'info');
            window.location.href = '/promoter-login';
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('干净版仪表板页面加载完成', 'success');
            addLog(`当前URL: ${window.location.href}`, 'info');
            addLog(`当前cookies: ${document.cookie || '无'}`, 'info');
            
            // 显示今日日期
            const today = new Date();
            document.getElementById('todayDate').textContent = today.toLocaleDateString('zh-CN');
            
            // 延迟一下确保session已经生效
            setTimeout(() => {
                addLog('开始初始化数据...', 'info');
                loadUserInfo();
                loadStats();
                loadActions();
            }, 500);
            
            // 设置定时刷新（每30秒）
            setInterval(() => {
                addLog('定时刷新数据...', 'info');
                loadStats();
                loadActions();
            }, 30000);
        });
        
        // 监听页面卸载（检测意外跳转）
        window.addEventListener('beforeunload', function(e) {
            addLog('页面即将卸载/跳转', 'warning');
        });
    </script>
</body>
</html>
