<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui优化版</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .image-preview {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
            <div class="layuimini-main">
                <div class="layui-card">
                    <div class="layui-card-header">设备日志</div>
                    <div class="layui-card-body">
                        <div id="groups1" class="layui-btn-container"></div>
                        <table id="deviceLogTable" lay-filter="deviceLogTable"></table>
                    </div>
                </div>
            </div>

            <!-- 历史日志弹窗模板 -->
            <script type="text/html" id="historyTpl">
                <div class="layui-fluid" style="padding: 20px;">
                    <table id="historyTable" lay-filter="historyTable"></table>
                </div>
            </script>
        </div>
    </div>

    <script src="/lib/jquery-3.4.1/jquery-3.4.1.min.js"></script>
    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.1.min.js"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    
    <script>
        layui.use(['jquery', 'table', 'layer', 'util'], function(){
            var $ = layui.$;
            var table = layui.table;
            var layer = layui.layer;
            var util = layui.util;
            
            // 设备日志表格
            // 初始化筛选表单
            var form = layui.form;
            form.on('submit(searchForm)', function(data){
                table.reload('deviceLogTable', {
                    where: data.field
                });
                return false;
            });

            // 初始化表格
            var logTable = table.render({
                elem: '#deviceLogTable',
                url: '/wsRouter/queryLogs',
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                toolbar: '#toolbarDemo',
                cols: [[
                    {field: 'device_id', title: '设备ID', width: 150},
                    {field: 'status', title: '状态', width: 120, templet: function(d){
                                        var statusMap = {
                                            'running': {text: '运行中', color: 'green'},
                                            'completed': {text: '已完成', color: 'blue'},
                                            'failed': {text: '失败', color: 'red'},
                                            'pending': {text: '未执行', color: 'orange'}
                                        };
                                        var statusInfo = statusMap[d.status] || {text: d.status, color: ''};
                                        return '<span class="layui-badge layui-bg-'+statusInfo.color+'">'+statusInfo.text+'</span>';
                    }},
                    {field: 'execution_data', title: '执行内容', minWidth: 200},
                    {field: 'created_at', title: '时间', width: 180, templet: function(d){
                        return util.toDateString(d.created_at, 'yyyy-MM-dd HH:mm:ss');
                    }},
                    {fixed: 'right', title: '操作', width: 120, align:'center', toolbar: '#historyBtn'}
                ]],
                parseData: function(res){
                    if(res.code === 0 && res.data && res.data.list){
                        // 按设备分组，只显示最新日志
                        var deviceMap = {};
                        res.data.list.forEach(function(item){
                            if(!deviceMap[item.device_id] || 
                               new Date(item.created_at) > new Date(deviceMap[item.device_id].created_at)){
                                deviceMap[item.device_id] = item;
                            }
                        });
                        return {
                            "code": 0,
                            "data": Object.values(deviceMap),
                            "count": res.data.total || Object.keys(deviceMap).length
                        };
                    }
                    return res; // 返回原始数据让表格处理错误
                }
            });

            // 20秒自动刷新数据
            // 优化后的无感刷新
            var refreshInterval = setInterval(function() {
                table.reload('deviceLogTable', {
                    url: '/wsRouter/queryLogs',
                    where: {},
                    done: function(res) {
                        // 静默刷新，不显示加载动画
                    }
                });
            }, 5000); // 5秒刷新一次
            
            // 添加表格加载时的遮罩效果
            table.on('loading(deviceLogTable)', function(obj){
                layer.msg('数据更新中...', {icon: 16, shade: 0.01, time: 1000});
            });

            // 页面卸载时清除定时器
            $(window).on('beforeunload', function() {
                clearInterval(refreshInterval);
            });
            
            // 查看历史按钮
            table.on('tool(deviceLogTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'history'){
                    layer.open({
                        type: 1,
                        title: '设备 '+data.device_id+' 的历史日志',
                        area: ['80%', '80%'],
                        content: $('#historyTpl').html(),
                        success: function(){
                            // 历史日志表格
                            table.render({
                                elem: '#historyTable',
                                url: '/wsRouter/queryLogs?device_id='+data.device_id,
                                page: true,
                                parseData: function(res){
                                    if(res.code === 0 && res.data && res.data.list){
                                        return {
                                            "code": 0,
                                            "data": res.data.list,
                                            "count": res.data.total
                                        };
                                    }
                                    return res;
                                },
                                cols: [[
                                    {field: 'status', title: '状态', width: 120, templet: function(d){
                                        var statusMap = {
                                            'running': {text: '运行中', color: 'green'},
                                            'completed': {text: '已完成', color: 'blue'},
                                            'failed': {text: '失败', color: 'red'},
                                            'pending': {text: '待处理', color: 'orange'}
                                        };
                                        var statusInfo = statusMap[d.status] || {text: d.status, color: ''};
                                        return '<span class="layui-badge layui-bg-'+statusInfo.color+'">'+statusInfo.text+'</span>';
                                    }},
                                    {field: 'execution_data', title: '执行内容', minWidth: 200},
                                    {field: 'created_at', title: '时间', width: 180, templet: function(d){
                                        return util.toDateString(d.created_at, 'yyyy-MM-dd HH:mm:ss');
                                    }}
                                ]],
                                done: function(res, curr, count){
                                    if(res.code !== 0){
                                        layer.msg(res.msg || '加载历史记录失败');
                                    }
                                }
                            });
                        }
                    });
                }
            });
        });
    </script>
    
    <!-- 操作按钮模板 -->
    <script type="text/html" id="historyBtn">
        <a class="layui-btn layui-btn-xs" lay-event="history">查看历史</a>
    </script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });

        // 本地文件上传功能（添加元素存在性检查）
        const mediaUpload = document.getElementById('mediaUpload');
        const mediaPreview = document.getElementById('mediaPreview');
        const serverLinkDisplay = document.getElementById('ossLink');
        const serverLinkData = document.getElementById('ossLinkData');
        


    </script>
</body>

</html>