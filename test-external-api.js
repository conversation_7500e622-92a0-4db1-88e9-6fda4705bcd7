const axios = require("axios");

const BASE_URL = "http://localhost:15001/api/douyin";

async function testExternalAPI() {
  console.log("🚀 开始测试外部API...\n");

  try {
    // 1. 先添加一些测试数据
    console.log("1. 添加测试数据...");
    const testProxies = [
      {
        sk5: "************:1080",
        ip: "************",
        province: "北京市",
        usage_count: 0,
        status: "active",
      },
      {
        sk5: "************:1080",
        ip: "************",
        province: "北京市",
        usage_count: 2,
        status: "active",
      },
      {
        sk5: "************:1080",
        ip: "************",
        province: "上海市",
        usage_count: 1,
        status: "active",
      },
      {
        sk5: "************:1080",
        ip: "************",
        province: "广东省",
        usage_count: 0,
        status: "active",
      },
      {
        sk5: "************:1080",
        ip: "************",
        province: "北京市",
        usage_count: 5,
        status: "inactive",
      },
    ];

    for (const proxy of testProxies) {
      try {
        await axios.post(`${BASE_URL}/proxies`, proxy);
        console.log(`✓ 添加代理: ${proxy.sk5} (${proxy.province})`);
      } catch (error) {
        console.log(`- 代理已存在: ${proxy.sk5}`);
      }
    }

    console.log("\n2. 测试获取可用省份列表...");
    const provincesResponse = await axios.get(
      `${BASE_URL}/get-available-provinces`
    );
    console.log("✓ 可用省份列表:");
    console.table(provincesResponse.data.data);

    console.log("\n3. 测试根据省份获取单个代理...");
    const singleProxyResponse = await axios.get(
      `${BASE_URL}/get-proxy-by-province?province=北京市`
    );
    console.log("✓ 获取北京市代理成功:");
    console.log(JSON.stringify(singleProxyResponse.data, null, 2));

    console.log("\n4. 测试批量获取指定省份的代理...");
    const multiProxyResponse = await axios.get(
      `${BASE_URL}/get-proxies-by-province?province=北京市&limit=3`
    );
    console.log("✓ 批量获取北京市代理成功:");
    console.log(JSON.stringify(multiProxyResponse.data, null, 2));

    console.log("\n5. 测试获取不存在省份的代理...");
    const noProxyResponse = await axios.get(
      `${BASE_URL}/get-proxy-by-province?province=西藏自治区`
    );
    console.log("✓ 获取不存在省份代理:");
    console.log(JSON.stringify(noProxyResponse.data, null, 2));

    console.log("\n6. 测试报告代理状态...");
    const reportResponse = await axios.post(`${BASE_URL}/report-proxy-status`, {
      sk5: "************:1080",
      status: "failed",
      error_msg: "连接超时",
    });
    console.log("✓ 报告代理状态成功:");
    console.log(JSON.stringify(reportResponse.data, null, 2));

    console.log("\n7. 验证代理状态是否更新...");
    const verifyResponse = await axios.get(
      `${BASE_URL}/get-proxy-by-province?province=北京市`
    );
    console.log("✓ 验证结果（应该返回不同的代理，因为之前的已标记为失败）:");
    console.log(JSON.stringify(verifyResponse.data, null, 2));

    console.log("\n8. 测试参数验证...");
    try {
      await axios.get(`${BASE_URL}/get-proxy-by-province`);
    } catch (error) {
      console.log("✓ 参数验证正常:", error.response.data.msg);
    }

    console.log("\n🎉 所有测试完成！");
  } catch (error) {
    console.error("❌ 测试失败:", error.response?.data || error.message);
  }
}

// 运行测试
testExternalAPI();
