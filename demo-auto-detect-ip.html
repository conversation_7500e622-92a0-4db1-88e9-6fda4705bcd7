<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOCKS5代理IP自动检测功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .feature-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .feature-section h3 {
            color: #764ba2;
            margin-top: 0;
        }

        .demo-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        input[type="text"]:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .examples {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }

        .examples h4 {
            margin-top: 0;
            color: #856404;
        }

        .example-item {
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
            background: white;
            padding: 8px;
            border-radius: 3px;
            border: 1px solid #e0e0e0;
        }

        .link-button {
            display: inline-block;
            background: #28a745;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            margin: 10px 5px;
            transition: background 0.3s;
        }

        .link-button:hover {
            background: #218838;
            text-decoration: none;
            color: white;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.success {
            background: #28a745;
        }

        .status-indicator.error {
            background: #dc3545;
        }

        .status-indicator.warning {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 SOCKS5代理IP自动检测功能演示</h1>

        <div class="feature-section">
            <h3>📋 功能说明</h3>
            <p>本系统已成功实现SOCKS5代理IP自动检测功能，主要特性包括：</p>
            <ul>
                <li><span class="status-indicator success"></span>自动检测SOCKS5代理的真实IP地址</li>
                <li><span class="status-indicator success"></span>自动获取IP对应的省份信息</li>
                <li><span class="status-indicator success"></span>支持带用户名密码的代理格式</li>
                <li><span class="status-indicator success"></span>智能格式验证和错误处理</li>
                <li><span class="status-indicator success"></span>前端界面集成测试功能</li>
            </ul>
        </div>

        <div class="feature-section">
            <h3>🧪 在线测试</h3>
            <div class="demo-form">
                <div class="form-group">
                    <label for="proxyInput">SOCKS5代理地址:</label>
                    <input type="text" id="proxyInput" placeholder="请输入代理地址，如: 127.0.0.1:1080">
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="autoDetect" checked>
                    <label for="autoDetect">启用自动检测IP地址</label>
                </div>

                <button class="btn" onclick="testDetection()" id="testBtn">
                    🔍 测试检测
                </button>
                <button class="btn btn-secondary" onclick="clearResult()">
                    🗑️ 清除结果
                </button>

                <div id="result"></div>
            </div>

            <div class="examples">
                <h4>📝 示例代理地址格式:</h4>
                <div class="example-item" onclick="fillExample('127.0.0.1:1080')">
                    127.0.0.1:1080 <small>(本地代理)</small>
                </div>
                <div class="example-item" onclick="fillExample('localhost:1080')">
                    localhost:1080 <small>(本地代理)</small>
                </div>
                <div class="example-item" onclick="fillExample('user:<EMAIL>:1080')">
                    user:<EMAIL>:1080 <small>(带认证)</small>
                </div>
                <div class="example-item" onclick="fillExample('*************:1080')">
                    *************:1080 <small>(内网代理)</small>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h3>🚀 快速访问</h3>
            <p>点击下面的链接直接访问相关页面：</p>
            <a href="http://localhost:15001/page/task/douyin514CKIP.html" class="link-button" target="_blank">
                📊 代理管理页面
            </a>
            <a href="http://localhost:15001/page/promotion/admin-promotion-simple.html" class="link-button" target="_blank">
                👥 推广用户管理
            </a>
        </div>

        <div class="feature-section">
            <h3>💡 使用说明</h3>
            <ol>
                <li><strong>在代理管理页面添加代理：</strong>
                    <ul>
                        <li>访问代理管理页面</li>
                        <li>点击"添加代理"按钮</li>
                        <li>输入SOCKS5地址</li>
                        <li>勾选"自动检测IP地址"</li>
                        <li>点击"测试检测"或直接保存</li>
                    </ul>
                </li>
                <li><strong>自动检测过程：</strong>
                    <ul>
                        <li>系统会通过代理连接多个IP检测服务</li>
                        <li>自动获取代理的真实IP地址</li>
                        <li>查询IP对应的省份信息</li>
                        <li>自动填充到表单中</li>
                    </ul>
                </li>
                <li><strong>错误处理：</strong>
                    <ul>
                        <li>如果自动检测失败，可以手动输入</li>
                        <li>系统会显示详细的错误信息</li>
                        <li>支持格式验证和连接测试</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="feature-section">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li>自动检测需要代理服务器可正常连接</li>
                <li>检测过程可能需要15-30秒时间</li>
                <li>本地代理(127.0.0.1:1080)需要先启动SOCKS5服务</li>
                <li>可以使用SSH隧道创建本地代理进行测试</li>
                <li>如果检测失败，系统会提示手动输入IP和省份</li>
            </ul>
        </div>
    </div>

    <script>
        async function testDetection() {
            const proxyInput = document.getElementById('proxyInput');
            const testBtn = document.getElementById('testBtn');
            const resultDiv = document.getElementById('result');
            
            const proxyAddress = proxyInput.value.trim();
            
            if (!proxyAddress) {
                showResult('请输入SOCKS5代理地址', 'error');
                return;
            }
            
            // 显示检测中状态
            testBtn.disabled = true;
            testBtn.innerHTML = '🔄 检测中...';
            showResult('正在检测代理IP，请稍候...', 'info');
            
            try {
                const response = await fetch('/api/douyin/test-proxy-detection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sk5: proxyAddress
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 0) {
                    const data = result.data;
                    showResult(`检测成功！\n\n代理地址: ${data.proxy}\nIP地址: ${data.ip}\n省份: ${data.province}`, 'success');
                } else {
                    showResult(`检测失败: ${result.msg}`, 'error');
                }
                
            } catch (error) {
                showResult(`网络错误: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                testBtn.disabled = false;
                testBtn.innerHTML = '🔍 测试检测';
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        function clearResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '';
            resultDiv.className = '';
        }
        
        function fillExample(example) {
            document.getElementById('proxyInput').value = example;
        }
        
        // 回车键触发测试
        document.getElementById('proxyInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testDetection();
            }
        });
    </script>
</body>
</html>
