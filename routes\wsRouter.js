const express = require('express');
const router = express.Router();
const debug = require('debug')('a-sokio-yun:server');
const axios = require('axios')

const mongoose = require('../db/db.js');
const deviceShe = require('../db/deviceShe')
const messageShe = require('../db/messageShe')
const { pool } = require('../db/mysql'); // 正确导入连接池

let userMod = require('../db/userMod')
// const userLogMod = require('../db/userLogMod')

const clients = new Map();


// qywxsend("jz服务器重启")
//服务器重启,所有设备掉线
userMod.find({}).then(docs => {
    if (docs && docs.length > 0) {
        docs.forEach(doc => {
            let deviceMod = mongoose.model('device_' + doc.userName, deviceShe)
            deviceMod.updateMany({}, { taskStatus: "掉线" }, { upsert: false }).then()
        })
    }
}).catch(err => {
    console.log(err)
})

// 创建 WebSocket 路由
router.ws('/:userName/:deviceName/:device_UUID/:device_height/:device_width/:taskStatus', (ws, req) => {

    const device_dic = req.params

    // debug(device_dic)
    
    const deviceName = device_dic.deviceName;//对应云控编号
    // const device_UUID = device_dic.device_UUID;
    // const device_height = device_dic.device_height;
    // const device_width = device_dic.device_width;
    const userName = device_dic.userName//对应云控账号
    let wsid = ""
    // let userName = req.session.username;//浏览器
    // if (userName) {
    //     wsid = "liuLanQi_" + userName
    //     debug(`浏览器上线: ${wsid}`);
    // }//这个方案不知道怎么回事突然获取不到用户名
    // debug("浏览器:" + req.session.username)
    if (deviceName == "liuLanQi") {
        wsid = "liuLanQi_" + userName
        debug(`浏览器上线: ${wsid}`);
    } else {
        // 获取设备信息 并存储到数据库
        wsid = userName + "_" + deviceName
        console.log(`设备链接: ${wsid}`);
        let d = new Date() //Date.now()
        let dataStr = d.getMonth() + 1 + "月" + d.getDate() + "日" + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds()
        //这里taskName不传

        let upData = { taskStatus: device_dic.taskStatus, socketId: wsid, updateTime: dataStr, device_width: device_dic.device_width, device_height: device_dic.device_height, device_UUID: device_dic.device_UUID }

        let deviceMod = mongoose.model('device_' + userName, deviceShe)
        deviceMod.updateOne({ deviceName }, upData, { upsert: true }).then()
    }

    // 将 WebSocket 实例存储起来
    clients.set(wsid, ws);

    // 初始化isAlive标记为true
    ws.isAlive = true;
    // 监听到信息 表示连接存活
    ws.on('pong', () => {
        debug('收到' + wsid + '的 Pong 消息');
        ws.isAlive = true;
    });

    // 实现每隔30秒发送一次Ping消息 
    const heartbeatTimer = setInterval(() => {
        if (ws.isAlive === false) {
            console.log('服务器,' + wsid + '的心跳异常???')
            clearInterval(heartbeatTimer);
            // qywxsend('服务器,' + wsid + '的心跳异常???')
            return ws.terminate();
        }

        ws.isAlive = false;
        debug('服务器,给' + wsid + '发送心跳包')
        ws.ping('');
    }, 30000);


    // 监听客户端连接,回调函数会传递本次连接的ws
    // 信息展示
    // 监听消息事件

    ws.on("message", msg => {
        // console.log(msg,'1111')
        // debug(`收到${wsid}的消息: ${msg}`);
        onMessage(userName, wsid, ws, msg)
    })

    // 监听关闭事件
    ws.on('close', (code, reason) => {
        // console.log("掉线");
        // 关闭心跳包检测是否存活
        clearInterval(heartbeatTimer);
        if (clients.get(wsid) !== ws) {
            console.log(`旧ws掉线: ${wsid},code:${code},reason:${reason},已重连`);
            return
        }

        clients.delete(wsid);// 从 Map 中删除 WebSocket 实例
        console.log(`ws断开: ${wsid},code:${code},reason:${reason},踢下线`);
        // 如果是投屏断开,则向手机发送取消发送截图的命令
        if (wsid == "liuLanQi_root") {
            // debug("浏览器关闭投屏:" + userName)
            for (const [id, client] of clients) {
                client.send(JSON.stringify({ type: "touPing_stop" }))
            }
        } else if (wsid.includes("liuLanQi_")) {
            // debug("浏览器关闭投屏:" + userName)
            for (const [id, client] of clients) {
                if (id.startsWith(userName + "_")) {
                    client.send(JSON.stringify({ type: "touPing_stop" }))
                }
            }
        } else {//if (wsid.indexOf("_") > -1)
            //  更新设备状态为掉线
            // qywxsend(`ws断开: ${wsid},code:${code},reason:${reason}`)
            let deviceMod = mongoose.model('device_' + userName, deviceShe)
            deviceMod.updateOne({ socketId: wsid }, { taskStatus: "掉线" }, { upsert: false }).then()
        }
    })
});

/**
 * @description: 
 * @param {*} userName 登录用户账号
 * @param {*} wsid 设备信息
 * @param {*} ws WebSocket实例
 * @param {*} msg WebSocket on('message') 监听到的数据
 * @return {*}
 */
async function onMessage(userName, wsid, ws, msg) {
    // debug(wsid, msg);

    // 判断是否二进制数据类型 如果是二进制则是投屏传输图片数据
    if (Buffer.isBuffer(msg)) {
        // debug("图片二进制数据")
        // debug("图片大小:", msg.length)
        // let wsidarr = []
        // if (userName == "root") {
        //     wsidarr = ["liuLanQi_" + userName]
        // } else {
        //     wsidarr = ["liuLanQi_" + userName, "liuLanQi_root"]
        // }
        let wsidarr = ["liuLanQi_" + userName]
        sendToClients(wsidarr, JSON.stringify({ type: "image/jpeg", wsid: wsid, binaryArr: msg }))
        return
    }

    let dic = isJSON(msg)
    if (!dic) {
        debug(wsid, "的消息无须解析", msg)
        return
    }
    // debug(wsid, dic);

    // 浏览器发送命令开启投屏//服务器转发给设备
    if (dic.type == "touPing_start") { // && wsid.includes("liuLanQi_")
        sendToClients(dic.socketIds, JSON.stringify({ type: "touPing_start", touPingDic: dic.touPingDic }))
    } else if (dic.type == "touPing_stop") { // 关闭投屏
        sendToClients(dic.socketIds, JSON.stringify({ type: "touPing_stop" }))
    } else if (dic.type == "startAction") { // 浏览器下发操作屏幕指令
        sendToClients(dic.wsid, JSON.stringify(dic))
    } else if (dic.type == "状态") {
        let data = dic.data
        let d = new Date() //Date.now()
        let updateTime = d.getMonth() + 1 + "月" + d.getDate() + "日" + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds()
        let upData = { taskStatus: data.taskStatus, updateTime: updateTime, taskName: data.taskName }
        debug("用户:" + data.userName + "的设备:" + data.deviceName + "更新状态了")
        let deviceMod = mongoose.model('device_' + data.userName, deviceShe)
        deviceMod.updateOne({ deviceName: data.deviceName }, upData, { upsert: true }).then()
    } else if (dic.type == "信息") {
        // debug("更新信息:" + dic.data);
        debug("用户:" + wsid + "更新信息")
        let deviceMod = mongoose.model('device_' + userName, deviceShe)
        deviceMod.updateOne({ socketId: wsid }, { deviceMsg: dic.data }, { upsert: true }).then()
    } else if (dic.type == "ping") {
        ws.isAlive = true;
        ws.send(JSON.stringify({ type: "send" }))

    } else if (dic.type == "ping_test") {

    } else if (dic.type == "send") {
        try {
            console.log('收到消息数据:', JSON.stringify(dic, null, 2));
            
            // 数据验证
            if (!dic.phoneNumber || !dic.username || !dic.links) {
                const errorMsg = `缺少必要参数: ${JSON.stringify({
                    phoneNumber: dic.phoneNumber,
                    username: dic.username,
                    links: dic.links
                })}`;
                console.error(errorMsg);
                return;
            }

            // 数据格式化
            const messageData = {
                type: dic.type || 'unknown',
                phoneNumber: dic.phoneNumber.toString().trim(),
                username: dic.username.toString().trim(),
                links: dic.links.toString().trim()
            };

            console.log('格式化后的消息数据:', messageData);
            
            // 保存消息到ws_link_data表
            const connection = await pool.getConnection();
            const [result] = await connection.query(
                'INSERT INTO ws_link_data (phoneNumber, username, links) VALUES (?, ?, ?)',
                [messageData.phoneNumber, messageData.username, messageData.links]
            );
            connection.release();
            console.log(`消息保存成功, ID: ${result.insertId}`);
            
            // 返回成功响应
            ws.send(JSON.stringify({
                type: "send_response",
                status: "success",
                messageId: messageId
            }));
            
        } catch (err) {
            console.error('保存消息失败:', {
                error: err.message,
                stack: err.stack,
                originalData: dic
            });
            
            // 返回错误响应
            ws.send(JSON.stringify({
                type: "send_response",
                status: "error",
                message: err.message
            }));
        }
    } else {
        console.log("无效数据");
        console.log(dic);
    }
}


// 发送数据给指定的 WebSocket 实例
function sendToClients(clientIds, data) {
    console.log('准备发送数据给设备:', clientIds);
    console.log('数据内容:', data);
    
    // // 验证clientIds是否为数组
    // if (!Array.isArray(clientIds)) {
    //     console.error('clientIds必须为数组');
    //     return;
    // }

    // // 验证数据是否为字符串
    // if (typeof data !== 'string') {
    //     try {
    //         data = JSON.stringify(data);
    //     } catch (e) {
    //         console.error('数据格式化失败:', e);
    //         return;
    //     }
    // }

    // let successCount = 0;
    // let failCount = 0;
    
    // for (const clientId of clientIds) {
    //     if (!clientId || typeof clientId !== 'string') {
    //         console.error('无效的clientId:', clientId);
    //         failCount++;
    //         continue;
    //     }

    //     const ws = clients.get(clientId);
    //     if (ws && ws.readyState === ws.OPEN) {
    //         try {
    //             ws.send(data);
    //             console.log(`成功发送给设备 ${clientId}`);
    //             successCount++;
    //         } catch (e) {
    //             console.error(`发送给设备 ${clientId} 失败:`, e);
    //             failCount++;
    //             clients.delete(clientId);
    //         }
    //     } else {
    //         console.error(`设备 ${clientId} 未连接或连接未就绪`);
    //         failCount++;
    //     }
    // }
    
    // console.log(`发送结果: 成功 ${successCount} 个, 失败 ${failCount} 个`);
    
    // // 如果全部失败，记录详细错误
    // if (successCount === 0 && failCount > 0) {
    //     console.error('所有消息发送失败，请检查WebSocket连接状态');
    // }
        try {
            const parsedData = JSON.parse(data);
            
            if ("serverData" in parsedData) {
                const taskData = parsedData.serverData.taskData;
                if (data.taskName === "养号") {
                    // 统一分发给所有设备
                    const clientIds = Array.from(clients.keys()).filter(id => 
                        id.startsWith(data.userName + "_") && !id.includes("liuLanQi")
                    );
                    
                    clientIds.forEach(clientId => {
                        const ws = clients.get(clientId);
                        if (ws) {
                            ws.send(JSON.stringify({
                                type: "task_execute",
                                taskName: "养号",
                                params: data.taskData
                            }));
                        }
                    });
                    
                    res.send({code: 0, message: "养号任务已分发"});
                    return;
                }
                if (parsedData.serverData.taskName === "私信") {
                    // 处理私信任务
                    const urls = taskData.url地址 ? taskData.url地址.split("|") : [];
                    const users = taskData.messageUser ? taskData.messageUser.split("|") : [];
                    const messages = taskData.message ? taskData.message.split("|") : [];
                    
                    let i = 0;
                    for (const clientId of clientIds) {
                        const ws = clients.get(clientId);
                        if (ws) {
                            const clientData = JSON.parse(JSON.stringify(parsedData));
                            
                            if (urls.length > 0) {
                                clientData.serverData.taskData.url地址 = urls[i % urls.length];
                            }
                            if (users.length > 0) {
                                clientData.serverData.taskData.messageUser = users[i % users.length];
                            }
                            if (messages.length > 0) {
                                clientData.serverData.taskData.message = messages[i % messages.length];
                            }
                            
                            ws.send(JSON.stringify(clientData));
                            i++;
                        }
                    }
                    return;
                }
                
                if (parsedData.serverData.taskName === "提交评论区操作数据") {
                    // 处理douyin514任务
                    const keywords = taskData.keyword ? taskData.keyword.split("\n").filter(Boolean) : [];
                    const comments = taskData.commentContent ? taskData.commentContent.split("\n") : [];
                    const links = taskData.linkUrl ? taskData.linkUrl.split("\n").filter(Boolean) : [];

                    // 保留原始imageData内容
                    if (taskData.imageData) {
                        console.log('原始imageData:', taskData.imageData);
                    }

                    // 计算每个客户端应该分配的links数量
                    const linksPerClient = Math.max(1, Math.ceil(links.length / clientIds.length));

                    clientIds.forEach((clientId, index) => {
                        const ws = clients.get(clientId);
                        if (ws) {
                            const clientData = JSON.parse(JSON.stringify(parsedData));

                            if (keywords.length > 0) {
                                clientData.serverData.taskData.keyword = keywords; // 直接使用数组
                            }
                            if (comments.length > 0) {
                                clientData.serverData.taskData.commentContent = comments[index % comments.length];
                            }
                            if (links.length > 0) {
                                const start = index * linksPerClient;
                                const end = Math.min(start + linksPerClient, links.length);
                                clientData.serverData.taskData.linkUrl = links.slice(start, end).join('|');
                            }

                            // 保持原有功能开关状态
                            // clientData.serverData.taskData.likeSwitch = taskData.likeSwitch || 'off';
                            // clientData.serverData.taskData.commentSwitch = taskData.commentSwitch || 'on';
                            // clientData.serverData.taskData.collectSwitch = taskData.collectSwitch || 'off';

                            ws.send(JSON.stringify(clientData));
                        }
                    });
                    return;
                }
                if (parsedData.serverData.taskName === "发视频") {
                    // 处理douyin514任务
                    const videoTitles = taskData.videoTitle ? taskData.videoTitle.split("|") : [];
                    const videoDescriptions = taskData.videoDescription ? taskData.videoDescription.split("|") : [];
                    // 处理OSS链接数组，先按逗号分割，再trim每个URL
                    const ossLinks = taskData.ossLink 
                        ? taskData.ossLink.split(',').map(link => link.trim()).filter(link => link)
                        : [];
                    console.log('原始OSS链接:', taskData.ossLink);
                    console.log('分割后OSS链接数组:', ossLinks);
                    
                    let i = 0;
                    for (const clientId of clientIds) {
                        const ws = clients.get(clientId);
                        if (ws) {
                            const clientData = JSON.parse(JSON.stringify(parsedData));
                            
                            if (videoTitles.length > 0) {
                                clientData.serverData.taskData.videoTitle = videoTitles[i % videoTitles.length];
                            }
                            if (videoDescriptions.length > 0) {
                                clientData.serverData.taskData.videoDescription = videoDescriptions[i % videoDescriptions.length];
                            }
                            if (ossLinks.length > 0) {
                                clientData.serverData.taskData.ossLink = ossLinks[i % ossLinks.length];
                            }
                            
                            // 保持原有功能开关状态
                            // clientData.serverData.taskData.likeSwitch = taskData.likeSwitch || 'off';
                            // clientData.serverData.taskData.commentSwitch = taskData.commentSwitch || 'on';
                            // clientData.serverData.taskData.collectSwitch = taskData.collectSwitch || 'off';
                            
                            ws.send(JSON.stringify(clientData));
                            i++;
                        }
                    }
                    return;
                }
                                
                if (parsedData.serverData.taskName === "提交个人主页操作数据") {
                    // 处理个人主页修改任务
                    const keywords = taskData.keyword ? taskData.keyword.split("\n").filter(Boolean) : [];
                    const comments = taskData.commentContent ? taskData.commentContent.split("|").filter(Boolean) : [];
                    const links = taskData.linkUrl ? [taskData.linkUrl].filter(Boolean) : [];
                    
                    // 优化imageData处理
                    let base64Images = [];
                    if (taskData.imageData) {
                        try {
                            // 尝试解析JSON格式的imageData
                            const parsedImages = JSON.parse(taskData.imageData);
                            if (Array.isArray(parsedImages)) {
                                // 处理数组格式
                                base64Images = parsedImages.map(item => {
                                    if (typeof item === 'string') {
                                        const match = item.match(/[A-Za-z0-9+/]+={0,2}$/);
                                        return match ? match[0] : '';
                                    } else if (item && typeof item.base64 === 'string') {
                                        const match = item.base64.match(/[A-Za-z0-9+/]+={0,2}$/);
                                        return match ? match[0] : '';
                                    }
                                    return '';
                                }).filter(Boolean);
                            } else if (typeof taskData.imageData === 'string') {
                                // 处理纯字符串格式
                                const matches = taskData.imageData.match(/[A-Za-z0-9+/]+={0,2}$/g) || [];
                                base64Images = matches.filter(Boolean);
                            }
                        } catch (e) {
                            // 如果不是JSON，尝试直接提取base64
                            const matches = taskData.imageData.match(/[A-Za-z0-9+/]+={0,2}$/g) || [];
                            base64Images = matches.filter(Boolean);
                        }
                    }

                    // 记录处理结果
                    console.log('处理后的imageData:', base64Images.length > 0 ? `${base64Images[0].substring(0, 20)}...` : '无');

                    // 计算每个客户端应该分配的任务数量
                    const clientCount = clientIds.length;
                    const keywordPerClient = Math.max(1, Math.ceil(keywords.length / clientCount));
                    const commentPerClient = Math.max(1, Math.ceil(comments.length / clientCount));
                    const imagePerClient = Math.max(1, Math.ceil(base64Images.length / clientCount));

                    // 分配任务给每个客户端
                    clientIds.forEach((clientId, index) => {
                        const ws = clients.get(clientId);
                        if (ws) {
                            const clientData = JSON.parse(JSON.stringify(parsedData));
                            
                            // 分配关键词
                            if (keywords.length > 0) {
                                const start = index * keywordPerClient;
                                const end = Math.min(start + keywordPerClient, keywords.length);
                                clientData.serverData.taskData.keyword = keywords.slice(start, end).join('|');
                            }
                            
                            // 分配评论内容
                            if (comments.length > 0) {
                                const start = index * commentPerClient;
                                const end = Math.min(start + commentPerClient, comments.length);
                                clientData.serverData.taskData.commentContent = comments.slice(start, end).join('|');
                            }
                            
                            // 分配图片
                            if (base64Images.length > 0) {
                                const start = index * imagePerClient;
                                const end = Math.min(start + imagePerClient, base64Images.length);
                                clientData.serverData.taskData.imageData = base64Images.slice(start, end).join(',');
                            }
                            
                            // 保持原有功能开关状态
                            clientData.serverData.taskData.likeSwitch = taskData.likeSwitch || 'off';
                            clientData.serverData.taskData.commentSwitch = taskData.commentSwitch || 'on';
                            clientData.serverData.taskData.collectSwitch = taskData.collectSwitch || 'off';
                            
                            ws.send(JSON.stringify(clientData));
                        }
                    });
                    return;
                }
            }
            
            // 默认处理：原样发送数据
            for (const clientId of clientIds) {
                const ws = clients.get(clientId);
                if (ws) {
                    ws.send(data);
                }
            }
        } catch (err) {
            console.error('处理消息失败:', err);
        }
    


    // //第二种方案
    // for (const [id, ws] of clients) {
    //     if (clientIds.includes(id)) {
    //         ws.send(data);
    //     }
    // }
}


// // 发送数据给所有连接的 WebSocket 实例
// function sendToAll(data) {
//     //第一种方案
//     for (const ws of clients.values()) {
//         ws.send(data);
//     }

// }
// 判断是否为json格式 如果解析后数据为obj且不为空 则正常返回数据
function isJSON(str) {
    if (typeof str == 'string') {
        try {
            var obj = JSON.parse(str);
            if (typeof obj == 'object' && obj) {
                return obj;
            } else {
                return false;
            }
        } catch (e) {
            debug(e)
            return false;
        }
    } else {
        console.log(typeof str + "????????????????????????");
    }
}


// // 解析请求体
// router.use(bodyParser.json());
// router.use(bodyParser.urlencoded({ extended: true }));

// 处理 HTTP POST 请求

// 判断用户是否在线 在线得话 开始使用WebScoket向脚本发送任务
router.post('/faTast', (req, res) => {
    let { data } = req.body
    // debug("data", req.body)
    // let userName = data.userName
    if (data.userName) {
        faTask(res, data, data.userName)
    } else {
        res.json({
            code: -1,
            msg: "失败:用户登录失效"
        })
    }
})

async function faTask(res, data, userName) {
    //向后台发送给提交成功
    console.log("服务器收到任务,开始分发")
    //给设备arr发任务,并记录日志
    try {
        // 处理养号任务

        
        // 其他任务处理

        // debug("data.taskData.taskID", data.taskData.taskID)
        // debug(data.taskData.taskID ? { taskID: data.taskData.taskID } : data.taskData)
        // debug(data.taskData.taskID || data.taskData)

        let taskTask = JSON.stringify({ type: "deviceTask", serverData: { taskName: data.taskName, taskData: data.taskData?.taskID || data.taskData, timeStamp: data.timeStamp } })
        let wsIds = [] // 定义一个数组，用于保存要发送到的WebSocket客户端的ID
        if (data.socketIdArr && data.socketIdArr.length > 0) {
            debug('有附加设备')
            wsIds = data.socketIdArr
        }

        if (data.groupDocArr && data.groupDocArr.length > 0) {
            // debug('有分组全选')
            // debug(data.groupDocArr)
            let deviceMod = mongoose.model('device_' + userName, deviceShe)
            for (let groupDoc of data.groupDocArr) {
                // debug(groupDoc)
                let devices = await deviceMod.find({ groupId: groupDoc.groupId })//.exec()
                for (let device of devices) {
                    //给指定得客户端发送消息1
                    // debug("添加到任务数组:用户的" + userName + "[" + groupDoc.groupName + "]组的:" + device.deviceName + ",wsid:" + device.socketId)
                    if (!wsIds.includes(device.socketId)) {
                        await wsIds.push(device.socketId)
                    }
                };
            }
        }

        //记录日志
        // let d = new Date()
        // debug(userObj)
        // new userLogMod({
        //     userName: userName,
        //     logStr: "所选设备[" + wsIds + "],任务名[" + data.taskName + "],任务参数[" + JSON.stringify(data.taskData) + "]",
        //     logTime: d.getFullYear() + "年" + (d.getMonth() + 1) + "月" + d.getDate() + "日" + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds()
        // }).save()

        //发送任务
        debug("下发任务给wsIds", wsIds)
        sendToClients(wsIds, taskTask)
        res.json({
            code: 1,
            msg: "成功"
        })
    } catch (err10) {
        res.json({
            code: -1,
            msg: "下发任务出错:" + err10.toString()
        })
    }
}

router.post('/send/:clientId', (req, res) => {
    const clientId = req.params.clientId; // 获取URL路径中的clientId参数
    const data = req.body.data; // 获取请求体中的data字段

    debug(`Sending data to: ${clientId}`); // 打印日志，表示正在向指定的clientId发送数据
    debug(`Data: ${data}`); // 打印日志，表示要发送的数据内容

    // 发送数据
    sendToClients([clientId], data); // 调用sendToClients()方法，将数据发送给指定的clientId
    res.sendStatus(200);// 返回状态码200表示成功处理请求
});

router.get('/wss', function (req, res) {
    let zhangHao = req.query.z // 获取查询参数中的账号（用户名）
    if (zhangHao) { // 如果账号存在
        let deviceNum = 0 // 已上线设备数量
        for (const id of clients.keys()) { // 判断连接ID是否以当前账号开头
            if (id.startsWith(zhangHao + "_")) { // 判断连接ID是否以当前账号开头
                deviceNum = deviceNum + 1 // 判断连接ID是否以当前账号开头
            }
        }
        res.send(`ws(${zhangHao}):${deviceNum}个`) // 返回格式化的已上线设备数量信息
    } else {
        res.send(`ws(总):${clients.size}个`) // 如果账号不存在，返回格式化的总连接数量信息
    }
});

//上线前先检测
router.get('/wsid', async function (req, res) {
    let deviceName = req.query.id
    let userName = req.query.userName
    let wsid = `${userName}_${deviceName}`
    try {
        //先判断有没有此账户,再判断上线数量,最后判断有没有重复
        const doc = await userMod.findOne({ userName })
        if (!doc) {
            // debug(userName + "用户不存在");
            return res.json({
                code: -1,
                msg: "用户不存在"
            })
        }

        //用户存在
        //判断上线数量两种方案,(暂用第二种)
        //第一种通过数据库判断,缺点1占用数据库线程.缺点2需要把没用的设备删除
        //第二种通过遍历ws,例如root,通过di是否indexOf("root_")然后+1来计算.缺点遍历完才知道个数.占用内存?

        let deviceNum = 0 // 已上线设备数量
        const maxNum = doc.userDeviceNum // 设定的此用户占控数量
        debug(`本次上线id(${wsid}),用户(${userName})的最大占控数量${maxNum}个`)
        const wwwsss = clients.get(wsid) // 根据连接ID获取已存在的连接对象
        if (wwwsss) { // 如果连接ID已存在的连接对象
            console.log(`连接ID${wsid}已存在的连接对象,关闭先前的连接`)
            wwwsss.terminate(); // 关闭先前的连接
            // throw new Error(wsid + "有重复")
            return res.json({
                code: -1,
                msg: "疑似重复,已剔除老设备"
            })
        }

        for (const id of clients.keys()) {
            console.log("id", id);
            if (id.startsWith(userName + "_")) { // 判断连接ID是否以当前用户名开头
                deviceNum = deviceNum + 1 // 计算已上线设备数量
            }
        }


        debug(userName + ':已有设备数量:' + deviceNum)

        if (deviceNum >= maxNum) {  // 如果已上线设备数量超过设定的上限
            return res.json({
                code: -1,
                msg: `设备超过设定数量:${maxNum}`
            })
        } else {
            return res.json({
                code: 1,
                msg: "可以上线"
            })
        }
    } catch (error) {
        return res.json({
            code: -1,
            msg: "服务器:" + error.message
        })
    }

})
//手机端ws验证代码
// function yanZheng() {
//     // for (let i = 3; i > 0; i--) {
//     // while (true) {
//     logd("验证中..",)
//     try {
//         let resJson = http.get("http://" + server_api + "/wsRouter/wsid?userName=" + jz_yun.云控账号 + "&id=" + jz_yun.云控编号).body.json()
//         if (resJson) {
//             // logd(JSON.stringify(resJson))
//             if (resJson.code == 1) {
//                 // logd("验证成功.连接云控后台中..")
//                 return true
//             } else {
//                 // loge(resJson.msg, "20秒后重试")
//                 loge(resJson.msg)
//                 alert(resJson.msg + ",请重试")
//                 exit()
//             }
//         } else {
//             logi("登录错误,重试中")
//             // exit()
//         }
//     } catch (error) {
//         loge("验证错误:", error)
//         // alert("验证错误:【" + error.toString() + "】行:" + error.lineNumber)
//         // exit()
//     }
//     sleep(20000)
//     // }

// }

async function qywxsend(str) {
    try {
        let response = await axios.post("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d817ae9d-ed16-46d4-bf2a-cdcfc8ff50d0", {
            msgtype: 'text',
            text: { content: str }
        })
        if (response.data.errmsg == "ok") {
            // log.info("企微发信成功")
            return true
        }
    } catch (error) {
        log.error("企微发信错误:", error)
    }
}

// 添加查询messages数据的接口
router.get('/getMessages', async (req, res) => {
    try {
        const connection = await pool.getConnection();
        const [rows] = await connection.query('SELECT * FROM messages ORDER BY create_time DESC');
        connection.release();
        res.json({
            code: 1,
            data: rows
        });
    } catch (err) {
        console.error('查询消息失败:', err);
        res.status(500).json({
            code: -1,
            msg: '查询消息失败'
        });
    }
});

// 查询所有链接数据
router.get('/queryAllLinkData', async (req, res) => {
    console.log('queryAllLinkData接口被调用');
    try {
        console.log('获取数据库连接');
        const connection = await pool.getConnection();
        console.log('执行SQL查询');
        const [rows] = await connection.query('SELECT * FROM ws_link_data ORDER BY created_at DESC');
        console.log('查询结果:', rows);
        connection.release();
        console.log('返回数据');
        res.json({
            code: 1,
            data: rows
        });
    } catch (err) {
        console.error('查询链接数据失败:', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({
            code: -1,
            msg: '查询链接数据失败'
        });
    }
});

// WebSocket连接状态检查
function checkWebSocketConnections() {
    console.log('当前活跃连接:', clients.size);
    clients.forEach((ws, wsid) => {
        console.log(`连接ID: ${wsid}, 状态: ${ws.readyState}`);
    });
}

// 定时检查连接状态
setInterval(checkWebSocketConnections, 60000);

// 新增日志查询接口(带分页和筛选)
router.get('/queryLogs', async (req, res) => {
    console.log('queryLogs接口被调用，参数:', req.query);
    try {
        const { page = 1, limit = 20, device_id } = req.query;
        const offset = (page - 1) * limit;
        
        console.log('获取数据库连接');
        const connection = await pool.getConnection();
        
        let query = 'SELECT * FROM device_logs';
        let countQuery = 'SELECT COUNT(*) as total FROM device_logs';
        const params = [];
        
        if (device_id) {
            query += ' WHERE device_id = ?';
            countQuery += ' WHERE device_id = ?';
            params.push(device_id);
        }
        
        query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), offset);
        
        console.log('执行SQL查询:', query);
        const [rows] = await connection.query(query, params);
        const [[{total}]] = await connection.query(countQuery, params.slice(0, -2));
        
        connection.release();
        
        console.log('返回分页数据');
        res.json({
            code: 1,
            data: {
                list: rows,
                total,
                page: parseInt(page),
                limit: parseInt(limit)
            }
        });
    } catch (err) {
        console.error('查询日志失败:', {
            error: err.message,
            stack: err.stack
        });
        res.status(500).json({
            code: -1,
            msg: '查询日志失败'
        });
    }
});

router.post('/mylogs', async (req, res) => {
    console.log('设备日志插入接口被调用，数据:', req.body);
    const { device_id, status, execution_data } = req.body;
    
    // 验证必填字段
    if (!device_id || !status) {
        console.log('缺少必填字段');
        return res.status(400).json({ 
            code: -1,
            msg: 'device_id和status是必填字段'
        });
    }
    
    // 验证status枚举值
    const validStatuses = ['pending', 'running', 'completed', 'failed'];
    if (!validStatuses.includes(status)) {
        console.log('无效的status值:', status);
        return res.status(400).json({
            code: -1,
            msg: `status必须是以下值之一: ${validStatuses.join(', ')}`
        });
    }

    try {
        const connection = await pool.getConnection();
        console.log('获取数据库连接成功');
        
        try {
            const [result] = await connection.query(
                'INSERT INTO device_logs (device_id, status, execution_data) VALUES (?, ?, ?)',
                [device_id, status, execution_data || null]
            );
            
            console.log(`成功插入日志，ID: ${result.insertId}`);
            
            res.json({
                code: 0,
                msg: '日志插入成功',
                data: { id: result.insertId }
            });
        } finally {
            connection.release();
            console.log('释放数据库连接');
        }
    } catch (err) {
        console.error('插入日志失败:', err);
        res.status(500).json({
            code: -1,
            msg: err.message
        });
    }
});

router.get('/mylogs', async (req, res) => {
    console.log('设备日志查询接口被调用，参数:', req.query);
    let { device_id, status, start_time, end_time } = req.query;

    try {
        // 构建查询条件
        let whereClause = 'WHERE 1=1';
        let params = [];
        
        if (device_id) {
            whereClause += ' AND device_id = ?';
            params.push(device_id);
        }
        if (status) {
            whereClause += ' AND status = ?';
            params.push(status);
        }
        if (start_time) {
            whereClause += ' AND created_at >= ?';
            params.push(start_time);
        }
        if (end_time) {
            whereClause += ' AND created_at <= ?';
            params.push(end_time);
        }

        // 执行查询（移除 LIMIT 和 OFFSET）
        const [results] = await pool.query(`
            SELECT 
                id, device_id, status, execution_data,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') AS created_at
            FROM device_logs
            ${whereClause}
            ORDER BY created_at DESC
        `, params);

        const [count] = await pool.query(`
            SELECT COUNT(*) AS total FROM device_logs ${whereClause}
        `, params);

        res.json({
            code: 0,
            data: {
                list: results,
                total: count[0].total
            }
        });
    } catch (err) {
        console.error('查询设备日志失败:', err);
        res.status(500).json({
            code: -1,
            msg: '查询设备日志失败'
        });
    }
});

module.exports = router;
