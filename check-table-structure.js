const mysql = require('mysql2/promise');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4"
};

async function checkTableStructure() {
  console.log('🔍 检查推广表结构...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查promotion_actions表结构
    console.log('\n📋 promotion_actions表结构:');
    try {
      const [columns] = await connection.execute('DESCRIBE promotion_actions');
      columns.forEach(col => {
        console.log(`  ${col.Field} (${col.Type}) - ${col.Comment || '无注释'}`);
      });
    } catch (error) {
      console.error('❌ promotion_actions表不存在:', error.message);
    }
    
    // 检查promotion_visits表结构
    console.log('\n📋 promotion_visits表结构:');
    try {
      const [columns2] = await connection.execute('DESCRIBE promotion_visits');
      columns2.forEach(col => {
        console.log(`  ${col.Field} (${col.Type}) - ${col.Comment || '无注释'}`);
      });
    } catch (error) {
      console.error('❌ promotion_visits表不存在:', error.message);
    }
    
    // 重新创建promotion_actions表
    console.log('\n🔧 重新创建promotion_actions表...');
    
    try {
      await connection.execute('DROP TABLE IF EXISTS promotion_actions');
      console.log('✅ 删除旧表成功');
      
      await connection.execute(`
        CREATE TABLE promotion_actions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          promotion_user_id VARCHAR(50) NOT NULL COMMENT '推广用户ID',
          target_user_id VARCHAR(100) COMMENT '目标用户ID',
          action_type ENUM('scan', 'login_success', 'login_fail', 'request_expire') NOT NULL COMMENT '操作类型',
          ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
          douyin_name VARCHAR(200) COMMENT '抖音名',
          douyin_id VARCHAR(100) COMMENT '抖音号',
          ck_data TEXT COMMENT 'CK数据',
          user_agent TEXT COMMENT '用户代理',
          action_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
          extra_data JSON COMMENT '额外数据',
          INDEX idx_promotion_user_id (promotion_user_id),
          INDEX idx_action_type (action_type),
          INDEX idx_ip_address (ip_address),
          INDEX idx_action_time (action_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广操作记录表'
      `);
      console.log('✅ 创建新表成功');
      
      // 验证新表结构
      const [newColumns] = await connection.execute('DESCRIBE promotion_actions');
      console.log('\n📋 新表结构:');
      newColumns.forEach(col => {
        console.log(`  ${col.Field} (${col.Type}) - ${col.Comment || '无注释'}`);
      });
      
      // 插入示例数据
      console.log('\n📊 插入示例数据...');
      
      const sampleData = [
        ['1001', 'user001', 'scan', '*************', null, null, null],
        ['1001', 'user001', 'login_success', '*************', '测试用户1', 'dy123456', 'sessionid=abc123'],
        ['1001', 'user002', 'scan', '*************', null, null, null],
        ['1001', 'user002', 'login_fail', '*************', null, null, null],
        ['1002', 'user003', 'scan', '*************', null, null, null],
        ['1002', 'user003', 'login_success', '*************', '测试用户2', 'dy789012', 'sessionid=ghi789'],
        ['1001', 'user004', 'request_expire', '*************', null, null, null],
        ['1003', 'user005', 'scan', '*************', null, null, null],
        ['1003', 'user005', 'login_success', '*************', '测试用户3', 'dy345678', 'sessionid=mno345']
      ];
      
      for (const data of sampleData) {
        await connection.execute(
          `INSERT INTO promotion_actions (promotion_user_id, target_user_id, action_type, ip_address, douyin_name, douyin_id, ck_data) 
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          data
        );
      }
      
      console.log(`✅ 插入 ${sampleData.length} 条示例数据`);
      
      // 更新统计数据
      await connection.execute(`
        UPDATE user SET 
          visit_count = (
            SELECT COUNT(*) FROM promotion_visits 
            WHERE promotion_user_id = user.promoter_id
          ),
          unique_ip_count = (
            SELECT COUNT(DISTINCT visitor_ip) FROM promotion_visits 
            WHERE promotion_user_id = user.promoter_id
          ),
          scan_count = (
            SELECT COUNT(*) FROM promotion_actions 
            WHERE promotion_user_id = user.promoter_id AND action_type = 'scan'
          ),
          success_count = (
            SELECT COUNT(*) FROM promotion_actions 
            WHERE promotion_user_id = user.promoter_id AND action_type = 'login_success'
          ),
          fail_count = (
            SELECT COUNT(*) FROM promotion_actions 
            WHERE promotion_user_id = user.promoter_id AND action_type = 'login_fail'
          ),
          expire_count = (
            SELECT COUNT(*) FROM promotion_actions 
            WHERE promotion_user_id = user.promoter_id AND action_type = 'request_expire'
          ),
          last_stat_update = NOW()
        WHERE user_type = 'promoter'
      `);
      
      console.log('✅ 统计数据更新完成');
      
    } catch (error) {
      console.error('❌ 重新创建表失败:', error.message);
    }
    
    await connection.end();
    
    console.log('\n🎉 表结构检查和修复完成！');
    
  } catch (error) {
    console.error('💥 检查失败:', error.message);
    process.exit(1);
  }
}

checkTableStructure();
