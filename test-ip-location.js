const axios = require('axios');

// 测试IP地理位置查询服务
async function testIPLocation() {
  console.log('🧪 测试IP地理位置查询服务...');
  
  // 测试的IP地址
  const testIPs = [
    '**************',  // 从代理检测到的真实IP
    '*******',         // Google DNS
    '***************', // 中国DNS
    '*******',         // Cloudflare DNS
    '*********'        // 阿里DNS
  ];
  
  // 不同的IP地理位置查询服务
  const locationServices = [
    {
      name: 'ip-api.com (中文)',
      url: (ip) => `https://ip-api.com/json/${ip}?lang=zh-CN`,
      parseResponse: (data) => ({
        country: data.country,
        region: data.regionName,
        city: data.city,
        isp: data.isp,
        status: data.status
      })
    },
    {
      name: 'ip-api.com (英文)',
      url: (ip) => `https://ip-api.com/json/${ip}`,
      parseResponse: (data) => ({
        country: data.country,
        region: data.regionName,
        city: data.city,
        isp: data.isp,
        status: data.status
      })
    },
    {
      name: 'ipapi.co',
      url: (ip) => `https://ipapi.co/${ip}/json/`,
      parseResponse: (data) => ({
        country: data.country_name,
        region: data.region,
        city: data.city,
        isp: data.org
      })
    },
    {
      name: 'ipinfo.io',
      url: (ip) => `https://ipinfo.io/${ip}/json`,
      parseResponse: (data) => ({
        country: data.country,
        region: data.region,
        city: data.city,
        isp: data.org
      })
    },
    {
      name: 'geoip-db.com',
      url: (ip) => `https://geoip-db.com/json/${ip}`,
      parseResponse: (data) => ({
        country: data.country_name,
        region: data.state,
        city: data.city
      })
    }
  ];
  
  console.log('\n📋 开始测试各个IP地理位置查询服务...\n');
  
  for (const ip of testIPs) {
    console.log(`🔍 测试IP: ${ip}`);
    console.log('─'.repeat(50));
    
    for (const service of locationServices) {
      try {
        console.log(`🌐 查询服务: ${service.name}`);
        
        const response = await axios.get(service.url(ip), {
          timeout: 10000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });
        
        const locationInfo = service.parseResponse(response.data);
        
        console.log(`   ✅ 查询成功:`);
        console.log(`      国家: ${locationInfo.country || '未知'}`);
        console.log(`      省份/地区: ${locationInfo.region || '未知'}`);
        console.log(`      城市: ${locationInfo.city || '未知'}`);
        console.log(`      ISP: ${locationInfo.isp || '未知'}`);
        if (locationInfo.status) {
          console.log(`      状态: ${locationInfo.status}`);
        }
        
      } catch (error) {
        console.log(`   ❌ 查询失败: ${error.message}`);
      }
      
      console.log(''); // 空行
    }
    
    console.log('═'.repeat(50));
    console.log(''); // 空行
  }
  
  console.log('🎉 IP地理位置查询测试完成！');
  
  console.log('\n💡 建议的改进方案:');
  console.log('1. 使用多个地理位置查询服务作为备选');
  console.log('2. 优先使用中文服务获取中文省份名称');
  console.log('3. 添加省份名称标准化处理');
  console.log('4. 增加缓存机制减少重复查询');
  console.log('5. 添加更详细的错误处理和重试机制');
}

// 运行测试
if (require.main === module) {
  testIPLocation().catch(console.error);
}

module.exports = testIPLocation;
