<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>


<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input
                            id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>

                <br>
                <fieldset style="border-color: rgb(51, 255, 0);border-width: 3px;">
                    <legend>【抢福袋任务】功能</legend>

                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width: 150px;">全局随机延迟</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" name="云随机上限值" value="3" autocomplete="off" class="layui-input" />
                            </div>
                            <div class="layui-form-mid">到</div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" name="云随机下限值" value="5" autocomplete="off" class="layui-input" />
                            </div>
                            <div class="layui-form-mid layui-word-aux">分,输入范围0-9(范围内随机)</div>
                        </div>
                    </div>
                    <fieldset>
                        <legend>【排行榜直播间抢福袋任务】功能</legend>


                        <!-- <div class="layui-form-item">
                        <label class="layui-form-label">榜单关键词</label>
                        <div class="layui-input-block">
                            <textarea name="云榜单关键词" placeholder="例如:你好|在吗|干嘛呢" class="layui-textarea"></textarea>
                            <tip>多句用|分割</tip>
                        </div>
                    </div> -->

                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 150px;">榜单关键词</label>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <input type="text" name="云榜单关键词" value="" autocomplete="off" class="layui-input" />
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                        </div>


                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 150px;">榜单用户名</label>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <input type="text" name="云设置榜单用户名" value="" autocomplete="off"
                                        class="layui-input" />
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                        </div>



                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="排行榜直播间抢福袋任务"
                            lay-filter="tijiao">排行榜直播间抢福袋任务</button>
                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止</button>
                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="一键清后台"
                            lay-filter="tijiao">一键清后台</button>
                    </fieldset>

                    <br>

                    <fieldset>
                        <legend>【关注列表直播间抢福袋任务】功能</legend>

                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 150px;">关注列表关键词</label>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <input type="text" name="云关注列表关键词" value="" autocomplete="off"
                                        class="layui-input" />
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                        </div>



                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="关注列表直播间抢福袋任务"
                            lay-filter="tijiao">关注列表直播间抢福袋任务</button>
                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止</button>
                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="一键清后台"
                            lay-filter="tijiao">一键清后台</button>
                    </fieldset>


                    <br>

                    <fieldset>
                        <legend>【链接跳转直播间抢福袋】功能</legend>

                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 150px;">直播间链接</label>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <input type="text" name="云链接跳转接口" value="" autocomplete="off" class="layui-input" />
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                        </div>



                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="链接跳转直播间抢福袋"
                            lay-filter="tijiao">链接跳转直播间抢福袋</button>
                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止</button>
                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="一键清后台"
                            lay-filter="tijiao">一键清后台</button>
                    </fieldset>

                    <br>


                    <fieldset>
                        <legend>【搜索直播间抢福袋任务】功能</legend>

                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 150px;">搜索关键词</label>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <input type="text" name="云搜索关键词" value="" autocomplete="off" class="layui-input" />
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                        </div>



                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="搜索直播间抢福袋任务"
                            lay-filter="tijiao">搜索直播间抢福袋任务</button>
                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止</button>
                        <button class="layui-btn  layui-btn-sm" lay-submit="" value="一键清后台"
                            lay-filter="tijiao">一键清后台</button>
                    </fieldset>

                </fieldset>

            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong
            tongYong.tongYong1()
        });
    </script>
</body>

</html>