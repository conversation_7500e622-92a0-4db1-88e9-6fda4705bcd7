<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户管理</title>
    <!-- SheetJS库用于Excel导出 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
            font-size: 16px;
        }

        .section-content {
            padding: 20px;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background: #f9f9f9;
        }

        tr:hover {
            background: #f5f5f5;
        }

        .status-active {
            color: #28a745;
            font-weight: bold;
        }

        .status-inactive {
            color: #6c757d;
            font-weight: bold;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .modal-footer {
            border-top: 1px solid #eee;
            padding-top: 15px;
            margin-top: 20px;
            text-align: right;
        }

        .close {
            float: right;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .toolbar {
            margin-bottom: 15px;
        }

        .link-cell {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* CK数据相关样式 */
        .ck-cell {
            max-width: 200px;
            position: relative;
        }

        .ck-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .ck-preview:hover {
            background: #e9ecef;
            border-color: #007bff;
        }

        .ck-preview.empty {
            color: #6c757d;
            font-style: italic;
        }

        .ck-copy-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 10px;
            cursor: pointer;
            margin-left: 8px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .ck-preview:hover .ck-copy-btn {
            opacity: 1;
        }

        .ck-copy-btn:hover {
            background: #0056b3;
        }

        .ck-copy-btn:active {
            background: #28a745;
        }

        /* 复制成功提示 */
        .copy-success {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 9999;
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* CK详情模态框样式 */
        .ck-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
        }

        .ck-modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .ck-modal-header {
            background: #007bff;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .ck-modal-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .ck-modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .ck-modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .ck-modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .ck-data-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
        }

        .ck-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }

        .ck-action-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .ck-action-btn:hover {
            background: #0056b3;
        }

        .ck-action-btn.success {
            background: #28a745;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>推广用户管理系统</h1>
            <p>管理推广用户，查看推广数据统计</p>
        </div>

        <!-- 推广用户管理 -->
        <div class="section">
            <div class="section-header">推广用户管理 (promotion_users表)</div>
            <div class="section-content">
                <div class="toolbar">
                    <button class="btn btn-success" onclick="showAddUserModal()">
                        ➕ 添加推广用户
                    </button>
                    <button class="btn" onclick="loadPromotionUsers()">
                        🔄 刷新数据
                    </button>
                </div>

                <div id="userTableContainer">
                    <div class="loading">正在加载推广用户数据...</div>
                </div>
            </div>
        </div>

        <!-- 推广访问统计数据 -->
        <div class="section">
            <div class="section-header">推广访问统计数据 (promotion_visits表)</div>
            <div class="section-content">
                <div class="toolbar">
                    <label>开始日期:</label>
                    <input type="date" id="startDate" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <label>结束日期:</label>
                    <input type="date" id="endDate" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <label>推广用户ID:</label>
                    <input type="text" id="filterUserId" placeholder="输入用户ID筛选"
                        style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <label>访问IP:</label>
                    <input type="text" id="filterIP" placeholder="输入IP筛选"
                        style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <button class="btn" onclick="loadPromotionVisits()">📊 查看访问数据</button>
                    <button class="btn" onclick="loadVisitsSummary()">📈 查看汇总统计</button>
                    <button class="btn" onclick="toggleAutoRefresh()">🔄 自动刷新</button>
                </div>

                <div id="visitsStatsContainer">
                    <div class="stats-cards" style="display: flex; gap: 20px; margin: 20px 0;">
                        <div class="stat-card"
                            style="flex: 1; text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                            <div class="stat-number" id="totalVisits"
                                style="font-size: 24px; font-weight: bold; color: #007bff;">-</div>
                            <div class="stat-label" style="color: #666;">总访问次数</div>
                        </div>
                        <div class="stat-card"
                            style="flex: 1; text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                            <div class="stat-number" id="uniqueIPs"
                                style="font-size: 24px; font-weight: bold; color: #28a745;">-</div>
                            <div class="stat-label" style="color: #666;">独立IP数</div>
                        </div>
                        <div class="stat-card"
                            style="flex: 1; text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                            <div class="stat-number" id="activePromoters"
                                style="font-size: 24px; font-weight: bold; color: #ffc107;">-</div>
                            <div class="stat-label" style="color: #666;">活跃推广用户</div>
                        </div>
                    </div>
                </div>

                <div id="visitsTableContainer">
                    <p>点击"查看访问数据"加载推广访问记录</p>
                </div>

                <div id="summaryTableContainer" style="display: none;">
                    <h3>推广用户访问汇总</h3>
                    <div class="loading">点击"查看汇总统计"加载汇总数据</div>
                </div>
            </div>
        </div>

        <!-- 操作记录 -->
        <div class="section">
            <div class="section-header">推广操作记录</div>
            <div class="section-content">
                <div class="toolbar">
                    <input type="date" id="actionDate"
                        style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <select id="promotionUserFilter"
                        style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-left: 10px;">
                        <option value="">全部推广用户</option>
                    </select>
                    <select id="actionTypeFilter"
                        style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-left: 10px;">
                        <option value="">全部操作类型</option>
                        <option value="scan">扫码</option>
                        <option value="login_success">登录成功</option>
                        <option value="login_fail">登录失败</option>
                        <option value="request_expire">请求过期</option>
                    </select>
                    <button class="btn" onclick="loadActions()">🔍 查询记录</button>
                    <button class="btn btn-success" onclick="exportActionsToExcel()" style="margin-left: 10px;">📊
                        导出Excel</button>
                </div>

                <div id="actionsContainer">
                    <p>请选择日期查询操作记录</p>
                </div>

                <div id="actionsPagination" style="margin-top: 20px; text-align: center;">
                    <!-- 分页控件 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 添加用户模态框 -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="closeAddUserModal()">&times;</span>
                <h3>添加推广用户</h3>
            </div>

            <form id="addUserForm">
                <div class="form-group">
                    <label>用户ID</label>
                    <input type="text" id="userId" placeholder="如：1003" required>
                </div>
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" id="username" placeholder="推广用户名" required>
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" id="password" placeholder="登录密码" required>
                </div>
                <div class="form-group">
                    <label>推广链接</label>
                    <input type="text" id="promotionLink" placeholder="留空自动生成">
                </div>
            </form>

            <div class="modal-footer">
                <button class="btn btn-success" onclick="addPromotionUser()">确定添加</button>
                <button class="btn" onclick="closeAddUserModal()">取消</button>
            </div>
        </div>
    </div>

    <!-- CK详情模态框 -->
    <div id="ckModal" class="ck-modal">
        <div class="ck-modal-content">
            <div class="ck-modal-header">
                <h3>CK数据详情</h3>
                <button class="ck-modal-close" onclick="closeCKModal()">&times;</button>
            </div>
            <div class="ck-modal-body">
                <div class="ck-actions">
                    <button class="ck-action-btn" onclick="copyCKData()">📋 复制全部CK</button>
                    <button class="ck-action-btn" onclick="formatCKData()">🔧 格式化显示</button>
                    <button class="ck-action-btn" onclick="downloadCKData()">💾 下载CK文件</button>
                </div>
                <div id="ckDataContainer" class="ck-data-container">
                    <!-- CK数据将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let promotionUsers = [];
        let currentPage = 1;
        const pageSize = 20;
        let currentActionsData = []; // 存储当前查询的操作记录数据

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🚀 推广用户管理页面已加载');

            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('statDate').value = today;
            document.getElementById('actionDate').value = today;

            // 加载推广用户数据
            loadPromotionUsers();
        });

        // 显示消息
        function showMessage(message, type = 'info') {
            const className = type === 'error' ? 'error' : 'success';
            const messageDiv = document.createElement('div');
            messageDiv.className = className;
            messageDiv.textContent = message;

            // 插入到第一个section之前
            const container = document.querySelector('.container');
            const firstSection = container.querySelector('.section');
            container.insertBefore(messageDiv, firstSection);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

        // 加载推广用户列表
        function loadPromotionUsers() {
            console.log('🔄 开始加载推广用户列表...');

            const container = document.getElementById('userTableContainer');
            container.innerHTML = '<div class="loading">正在加载推广用户数据...</div>';

            fetch('/api/promotion/admin/promotion-users')
                .then(response => {
                    console.log('📡 API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(result => {
                    console.log('📊 API响应数据:', result);

                    if (result.code === 0) {
                        promotionUsers = result.data.list;
                        console.log('✅ 推广用户数据加载成功，共', promotionUsers.length, '条记录');
                        renderUserTable(promotionUsers);
                    } else {
                        console.error('❌ API返回错误:', result.msg);
                        container.innerHTML = `<div class="error">加载失败: ${result.msg || '未知错误'}</div>`;
                    }
                })
                .catch(error => {
                    console.error('❌ 加载推广用户失败:', error);
                    container.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
                });
        }

        // 渲染用户表格
        function renderUserTable(users) {
            console.log('🎨 开始渲染用户表格，用户数量:', users.length);

            const container = document.getElementById('userTableContainer');

            if (users.length === 0) {
                container.innerHTML = '<p>暂无推广用户数据</p>';
                return;
            }

            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>用户ID</th>
                            <th>用户名</th>
                            <th>推广链接</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            users.forEach(user => {
                const statusText = user.status == 1 ? '启用' : '禁用';
                const statusClass = user.status == 1 ? 'status-active' : 'status-inactive';
                const actionText = user.status == 1 ? '禁用' : '启用';
                const actionClass = user.status == 1 ? 'btn-danger' : 'btn-success';
                const createTime = user.created_at ? new Date(user.created_at).toLocaleString('zh-CN') : '-';

                tableHTML += `
                    <tr>
                        <td>${user.user_id || '-'}</td>
                        <td>${user.username || '-'}</td>
                        <td class="link-cell" title="${user.promotion_link || ''}">${user.promotion_link || '-'}</td>
                        <td><span class="${statusClass}">${statusText}</span></td>
                        <td>${createTime}</td>
                        <td>
                            <button class="btn btn-sm ${actionClass}" onclick="toggleUserStatus('${user.user_id}', ${user.status})">
                                ${actionText}
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deletePromotionUser('${user.user_id}')" style="margin-left: 5px;">
                                删除
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            container.innerHTML = tableHTML;

            // 更新推广用户筛选器
            updatePromotionUserFilter(users);

            console.log('✅ 用户表格渲染完成');
        }

        // 显示添加用户模态框
        function showAddUserModal() {
            console.log('📝 显示添加用户模态框');
            document.getElementById('addUserModal').style.display = 'block';

            // 清空表单
            document.getElementById('addUserForm').reset();
        }

        // 关闭添加用户模态框
        function closeAddUserModal() {
            document.getElementById('addUserModal').style.display = 'none';
        }

        // 添加推广用户
        function addPromotionUser() {
            const userId = document.getElementById('userId').value.trim();
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const promotionLink = document.getElementById('promotionLink').value.trim();

            if (!userId || !username || !password) {
                showMessage('请填写完整的用户信息', 'error');
                return;
            }

            console.log('➕ 开始添加推广用户:', userId);

            const requestData = {
                user_id: userId,
                username: username,
                password: password
            };

            if (promotionLink) {
                requestData.promotion_link = promotionLink;
            }

            fetch('/api/promotion/admin/add-promotion-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
                .then(response => response.json())
                .then(result => {
                    console.log('📊 添加用户响应:', result);

                    if (result.code === 0) {
                        showMessage('推广用户添加成功', 'success');
                        closeAddUserModal();
                        loadPromotionUsers(); // 重新加载用户列表
                    } else {
                        showMessage('添加失败: ' + (result.msg || '未知错误'), 'error');
                    }
                })
                .catch(error => {
                    console.error('❌ 添加用户失败:', error);
                    showMessage('网络错误: ' + error.message, 'error');
                });
        }

        // 切换用户状态
        function toggleUserStatus(userId, currentStatus) {
            const newStatus = currentStatus == 1 ? 0 : 1;
            const action = newStatus == 1 ? '启用' : '禁用';

            if (!confirm(`确定要${action}用户 ${userId} 吗？`)) {
                return;
            }

            console.log(`🔄 ${action}用户 ${userId}`);

            fetch('/api/promotion/admin/update-promotion-user-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId,
                    status: newStatus
                })
            })
                .then(response => response.json())
                .then(result => {
                    console.log(`📊 ${action}用户响应:`, result);

                    if (result.code === 0) {
                        showMessage(`用户${userId}${action}成功`, 'success');
                        loadPromotionUsers(); // 重新加载用户列表
                    } else {
                        showMessage(`${action}失败: ` + (result.msg || '未知错误'), 'error');
                    }
                })
                .catch(error => {
                    console.error(`❌ ${action}用户失败:`, error);
                    showMessage(`网络错误: ${error.message}`, 'error');
                });
        }

        // 删除推广用户
        function deletePromotionUser(userId) {
            if (!confirm(`⚠️ 确定要删除推广用户 ${userId} 吗？\n\n此操作不可恢复，将永久删除该用户的所有数据！`)) {
                return;
            }

            console.log(`🗑️ 删除推广用户 ${userId}`);

            fetch('/api/promotion/admin/delete-promotion-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId
                })
            })
                .then(response => response.json())
                .then(result => {
                    console.log(`📊 删除用户响应:`, result);

                    if (result.code === 0) {
                        showMessage(`推广用户${userId}删除成功`, 'success');
                        loadPromotionUsers(); // 重新加载用户列表
                    } else {
                        showMessage(`删除失败: ` + (result.msg || '未知错误'), 'error');
                    }
                })
                .catch(error => {
                    console.error(`❌ 删除用户失败:`, error);
                    showMessage(`网络错误: ${error.message}`, 'error');
                });
        }

        // 加载统计数据
        function loadStats() {
            const date = document.getElementById('statDate').value;
            if (!date) {
                showMessage('请选择统计日期', 'error');
                return;
            }

            console.log('📊 加载统计数据，日期:', date);

            const container = document.getElementById('statsContainer');
            container.innerHTML = '<div class="loading">正在加载统计数据...</div>';

            fetch(`/api/promotion/admin/all-users-stats?date=${date}`)
                .then(response => response.json())
                .then(result => {
                    console.log('📊 统计数据响应:', result);

                    if (result.code === 0) {
                        renderStats(result.data.stats);
                    } else {
                        container.innerHTML = `<div class="error">加载统计失败: ${result.msg || '未知错误'}</div>`;
                    }
                })
                .catch(error => {
                    console.error('❌ 加载统计数据失败:', error);
                    container.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
                });
        }

        // 渲染统计数据
        function renderStats(stats) {
            const container = document.getElementById('statsContainer');

            if (stats.length === 0) {
                container.innerHTML = '<p>暂无统计数据</p>';
                return;
            }

            let html = '<table><thead><tr><th>推广用户</th><th>访问次数</th><th>独立IP</th><th>扫码数</th><th>成功数</th><th>失败数</th><th>过期数</th></tr></thead><tbody>';

            stats.forEach(stat => {
                html += `
                    <tr>
                        <td>${stat.user_id} - ${stat.username}</td>
                        <td>${stat.visit_count}</td>
                        <td>${stat.unique_ip_count}</td>
                        <td>${stat.scan_count}</td>
                        <td>${stat.success_count}</td>
                        <td>${stat.fail_count}</td>
                        <td>${stat.expire_count}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 加载操作记录
        function loadActions(page = 1) {
            const date = document.getElementById('actionDate').value;
            if (!date) {
                showMessage('请选择查询日期', 'error');
                return;
            }

            const promotionUserId = document.getElementById('promotionUserFilter').value;
            const actionType = document.getElementById('actionTypeFilter').value;

            console.log('📋 加载操作记录，日期:', date, '页码:', page);

            const container = document.getElementById('actionsContainer');
            container.innerHTML = '<div class="loading">正在加载操作记录...</div>';

            let url = `/api/promotion/admin/all-users-actions?page=${page}&limit=${pageSize}&date=${date}`;
            if (promotionUserId) url += `&promotion_user_id=${promotionUserId}`;
            if (actionType) url += `&action_type=${actionType}`;

            fetch(url)
                .then(response => response.json())
                .then(result => {
                    console.log('📊 操作记录响应:', result);

                    if (result.code === 0) {
                        currentActionsData = result.data.list; // 保存数据供导出使用
                        renderActions(result.data.list);
                        renderPagination(result.data.total, page);
                        currentPage = page;
                    } else {
                        container.innerHTML = `<div class="error">加载操作记录失败: ${result.msg || '未知错误'}</div>`;
                    }
                })
                .catch(error => {
                    console.error('❌ 加载操作记录失败:', error);
                    container.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
                });
        }

        // 渲染操作记录
        function renderActions(actions) {
            const container = document.getElementById('actionsContainer');

            if (actions.length === 0) {
                container.innerHTML = '<p>暂无操作记录</p>';
                return;
            }

            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>推广用户</th>
                            <th>用户ID</th>
                            <th>时间</th>
                            <th>IP</th>
                            <th>省份</th>
                            <th>状态</th>
                            <th>抖音名</th>
                            <th>抖音号</th>
                            <th>CK</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            actions.forEach((action, index) => {
                const statusClass = getActionStatusClass(action.status);
                html += `
                    <tr>
                        <td>${action.promotion_user_id}</td>
                        <td>${action.user_id || '-'}</td>
                        <td>${action.time}</td>
                        <td>${action.ip}</td>
                        <td>${action.ip_province || '-'}</td>
                        <td><span style="color: ${statusClass}">${action.status}</span></td>
                        <td>${action.douyin_name || '-'}</td>
                        <td>${action.douyin_id || '-'}</td>
                        <td class="ck-cell">${renderCKCell(action.ck, index)}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 获取操作状态颜色
        function getActionStatusClass(status) {
            switch (status) {
                case '登录成功': return '#28a745';
                case '登录失败': return '#dc3545';
                case '请求过期': return '#fd7e14';
                case '扫码': return '#007bff';
                default: return '#6c757d';
            }
        }

        // 渲染分页
        function renderPagination(total, current) {
            const container = document.getElementById('actionsPagination');

            if (total <= pageSize) {
                container.innerHTML = '';
                return;
            }

            const totalPages = Math.ceil(total / pageSize);
            let html = '<div style="display: inline-block;">';

            // 上一页
            if (current > 1) {
                html += `<button class="btn btn-sm" onclick="loadActions(${current - 1})">上一页</button>`;
            }

            // 页码
            const startPage = Math.max(1, current - 2);
            const endPage = Math.min(totalPages, current + 2);

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === current ? 'btn-success' : '';
                html += `<button class="btn btn-sm ${activeClass}" onclick="loadActions(${i})">${i}</button>`;
            }

            // 下一页
            if (current < totalPages) {
                html += `<button class="btn btn-sm" onclick="loadActions(${current + 1})">下一页</button>`;
            }

            html += `</div><div style="margin-left: 20px; display: inline-block;">共 ${total} 条记录，第 ${current}/${totalPages} 页</div>`;
            container.innerHTML = html;
        }

        // 更新推广用户筛选器
        function updatePromotionUserFilter(users) {
            const select = document.getElementById('promotionUserFilter');
            const currentValue = select.value;

            select.innerHTML = '<option value="">全部推广用户</option>';
            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.user_id;
                option.textContent = `${user.user_id} - ${user.username}`;
                select.appendChild(option);
            });

            select.value = currentValue;
        }

        // 点击模态框外部关闭
        window.onclick = function (event) {
            const modal = document.getElementById('addUserModal');
            if (event.target === modal) {
                closeAddUserModal();
            }
        }

        // 自动刷新相关变量
        let autoRefreshInterval = null;
        let isAutoRefreshing = false;

        // 加载推广访问数据 - 从promotion_visits表
        function loadPromotionVisits() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const userId = document.getElementById('filterUserId').value;
            const ip = document.getElementById('filterIP').value;

            console.log('📊 加载推广访问数据，筛选条件:', { startDate, endDate, userId, ip });

            const container = document.getElementById('visitsTableContainer');
            container.innerHTML = '<div class="loading">正在加载推广访问数据...</div>';

            let url = '/api/promotion/admin/promotion-visits?limit=50';
            if (startDate) url += `&start_date=${startDate}`;
            if (endDate) url += `&end_date=${endDate}`;
            if (userId) url += `&promotion_user_id=${userId}`;
            if (ip) url += `&visitor_ip=${ip}`;

            fetch(url)
                .then(response => response.json())
                .then(result => {
                    console.log('📊 推广访问数据响应:', result);

                    if (result.code === 0) {
                        renderVisitsTable(result.data.list);
                        updateVisitsStats(result.data.stats);
                        console.log('✅ 从promotion_visits表加载了', result.data.list.length, '条访问记录');
                    } else {
                        container.innerHTML = `<div class="error">加载失败: ${result.msg}</div>`;
                    }
                })
                .catch(error => {
                    console.error('❌ 加载推广访问数据失败:', error);
                    container.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
                });
        }

        // 渲染访问数据表格
        function renderVisitsTable(visits) {
            const container = document.getElementById('visitsTableContainer');

            if (visits.length === 0) {
                container.innerHTML = '<p>暂无访问记录</p>';
                return;
            }

            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>推广用户ID</th>
                            <th>访问IP</th>
                            <th>用户代理</th>
                            <th>来源页面</th>
                            <th>访问时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            visits.forEach(visit => {
                html += `
                    <tr>
                        <td>${visit.id}</td>
                        <td>${visit.promotion_user_id}</td>
                        <td>${visit.visitor_ip}</td>
                        <td class="link-cell" title="${visit.user_agent || ''}">${visit.user_agent || '-'}</td>
                        <td class="link-cell" title="${visit.referer || ''}">${visit.referer || '-'}</td>
                        <td>${visit.visit_time}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 更新访问统计数据
        function updateVisitsStats(stats) {
            document.getElementById('totalVisits').textContent = stats.total_visits || 0;
            document.getElementById('uniqueIPs').textContent = stats.unique_ips || 0;
            document.getElementById('activePromoters').textContent = stats.active_promoters || 0;
        }

        // 加载访问汇总统计
        function loadVisitsSummary() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            console.log('📈 加载访问汇总统计，日期范围:', { startDate, endDate });

            const container = document.getElementById('summaryTableContainer');
            container.style.display = 'block';
            container.innerHTML = '<h3>推广用户访问汇总</h3><div class="loading">正在加载汇总数据...</div>';

            let url = '/api/promotion/admin/promotion-visits-summary';
            const params = [];
            if (startDate) params.push(`start_date=${startDate}`);
            if (endDate) params.push(`end_date=${endDate}`);
            if (params.length > 0) url += '?' + params.join('&');

            fetch(url)
                .then(response => response.json())
                .then(result => {
                    console.log('📈 访问汇总数据响应:', result);

                    if (result.code === 0) {
                        renderSummaryTable(result.data.summary, result.data.date_range);
                    } else {
                        container.innerHTML = `<h3>推广用户访问汇总</h3><div class="error">加载失败: ${result.msg}</div>`;
                    }
                })
                .catch(error => {
                    console.error('❌ 加载访问汇总失败:', error);
                    container.innerHTML = `<h3>推广用户访问汇总</h3><div class="error">网络错误: ${error.message}</div>`;
                });
        }

        // 渲染汇总表格
        function renderSummaryTable(summary, dateRange) {
            const container = document.getElementById('summaryTableContainer');

            if (summary.length === 0) {
                container.innerHTML = `
                    <h3>推广用户访问汇总</h3>
                    <p>日期范围: ${dateRange.start_date} 至 ${dateRange.end_date}</p>
                    <p>暂无汇总数据</p>
                `;
                return;
            }

            let html = `
                <h3>推广用户访问汇总</h3>
                <p>日期范围: ${dateRange.start_date} 至 ${dateRange.end_date}</p>
                <table>
                    <thead>
                        <tr>
                            <th>推广用户ID</th>
                            <th>用户名</th>
                            <th>访问次数</th>
                            <th>独立IP数</th>
                            <th>首次访问</th>
                            <th>最后访问</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            summary.forEach(item => {
                html += `
                    <tr>
                        <td>${item.promotion_user_id}</td>
                        <td>${item.username || '-'}</td>
                        <td>${item.visit_count}</td>
                        <td>${item.unique_ip_count}</td>
                        <td>${item.first_visit_formatted}</td>
                        <td>${item.last_visit_formatted}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            if (isAutoRefreshing) {
                // 停止自动刷新
                clearInterval(autoRefreshInterval);
                isAutoRefreshing = false;
                showMessage('自动刷新已停止', 'info');
                console.log('🔄 自动刷新已停止');
            } else {
                // 开始自动刷新
                autoRefreshInterval = setInterval(() => {
                    console.log('🔄 自动刷新推广访问数据...');
                    loadPromotionVisits();
                }, 30000); // 每30秒刷新一次
                isAutoRefreshing = true;
                showMessage('自动刷新已开启（每30秒）', 'success');
                console.log('🔄 自动刷新已开启，每30秒刷新一次');
            }
        }

        // 导出访问数据
        function exportVisitsData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const userId = document.getElementById('filterUserId').value;
            const ip = document.getElementById('filterIP').value;

            let url = '/api/promotion/admin/export-visits-data?format=csv';
            if (startDate) url += `&start_date=${startDate}`;
            if (endDate) url += `&end_date=${endDate}`;
            if (userId) url += `&promotion_user_id=${userId}`;
            if (ip) url += `&visitor_ip=${ip}`;

            console.log('📥 导出访问数据:', url);

            // 创建下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = `promotion_visits_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showMessage('访问数据导出已开始', 'success');
        }

        // 页面加载时设置默认日期
        document.addEventListener('DOMContentLoaded', function () {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('startDate').value = today;
            document.getElementById('endDate').value = today;

            console.log('📅 默认日期已设置为今天:', today);
        });

        // CK数据相关变量
        let currentCKData = '';

        // 渲染CK单元格
        function renderCKCell(ckData, index) {
            if (!ckData || ckData === '-' || ckData.trim() === '') {
                return '<div class="ck-preview empty">暂无CK数据</div>';
            }

            // 生成预览文本（显示前30个字符）
            let previewText = ckData.length > 30 ? ckData.substring(0, 30) + '...' : ckData;

            // 尝试解析JSON格式的CK数据
            let isJSON = false;
            try {
                JSON.parse(ckData);
                isJSON = true;
                previewText = `JSON格式 (${ckData.length}字符)`;
            } catch (e) {
                // 不是JSON格式，使用原始预览
            }

            return `
                <div class="ck-preview" onclick="showCKModal('${escapeHtml(ckData)}', ${index})" title="点击查看完整CK数据">
                    <span>${previewText}</span>
                    <button class="ck-copy-btn" onclick="event.stopPropagation(); quickCopyCK('${escapeHtml(ckData)}', this)" title="快速复制">
                        📋
                    </button>
                </div>
            `;
        }

        // HTML转义函数
        function escapeHtml(text) {
            if (!text) return '';
            return text
                .replace(/\\/g, '\\\\')  // 先处理反斜杠
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;')
                .replace(/\n/g, '\\n')   // 处理换行符
                .replace(/\r/g, '\\r')   // 处理回车符
                .replace(/\t/g, '\\t');  // 处理制表符
        }

        // HTML反转义函数
        function unescapeHtml(text) {
            if (!text) return '';
            return text
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'")
                .replace(/\\n/g, '\n')   // 处理换行符
                .replace(/\\r/g, '\r')   // 处理回车符
                .replace(/\\t/g, '\t')   // 处理制表符
                .replace(/\\\\/g, '\\'); // 最后处理反斜杠
        }

        // 快速复制CK数据
        async function quickCopyCK(ckData, button) {
            const originalCKData = unescapeHtml(ckData);

            console.log('📋 尝试复制CK数据，长度:', originalCKData.length);
            console.log('📋 CK数据预览:', originalCKData.substring(0, 100) + (originalCKData.length > 100 ? '...' : ''));

            try {
                const success = await copyToClipboard(originalCKData);

                if (success) {
                    // 显示复制成功效果
                    const originalText = button.innerHTML;
                    const originalBg = button.style.background;

                    button.innerHTML = '✅';
                    button.style.background = '#28a745';

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.style.background = originalBg || '#007bff';
                    }, 1500);

                    showCopySuccess(`CK数据已复制到剪贴板 (${originalCKData.length}字符)`);
                } else {
                    showCopySuccess('复制失败，请手动复制', 'error');
                }
            } catch (error) {
                console.error('❌ 复制CK数据失败:', error);
                showCopySuccess('复制失败: ' + error.message, 'error');
            }
        }

        // 显示CK详情模态框
        function showCKModal(ckData, index) {
            currentCKData = unescapeHtml(ckData);

            const modal = document.getElementById('ckModal');
            const container = document.getElementById('ckDataContainer');

            // 尝试格式化JSON数据
            let displayData = currentCKData;
            try {
                const parsed = JSON.parse(currentCKData);
                displayData = JSON.stringify(parsed, null, 2);
            } catch (e) {
                // 不是JSON格式，保持原样
            }

            container.textContent = displayData;
            modal.style.display = 'block';

            console.log('📋 显示CK详情模态框，数据长度:', currentCKData.length);
        }

        // 关闭CK详情模态框
        function closeCKModal() {
            const modal = document.getElementById('ckModal');
            modal.style.display = 'none';
            currentCKData = '';
        }

        // 复制完整CK数据
        async function copyCKData() {
            if (!currentCKData) {
                showCopySuccess('没有可复制的CK数据', 'error');
                return;
            }

            try {
                const success = await copyToClipboard(currentCKData);

                if (success) {
                    showCopySuccess(`CK数据已复制到剪贴板 (${currentCKData.length}字符)`);

                    // 更新按钮状态
                    const button = event.target;
                    const originalText = button.textContent;
                    button.textContent = '✅ 已复制';
                    button.classList.add('success');

                    setTimeout(() => {
                        button.textContent = originalText;
                        button.classList.remove('success');
                    }, 2000);
                } else {
                    showCopySuccess('复制失败，请手动复制', 'error');
                }
            } catch (error) {
                console.error('❌ 复制完整CK数据失败:', error);
                showCopySuccess('复制失败: ' + error.message, 'error');
            }
        }

        // 格式化CK数据显示
        function formatCKData() {
            if (!currentCKData) {
                showCopySuccess('没有可格式化的CK数据', 'error');
                return;
            }

            const container = document.getElementById('ckDataContainer');

            try {
                // 尝试解析为JSON并格式化
                const parsed = JSON.parse(currentCKData);
                container.textContent = JSON.stringify(parsed, null, 2);
                showCopySuccess('CK数据已格式化显示');
            } catch (e) {
                // 不是JSON格式，尝试其他格式化方式
                if (currentCKData.includes('=') && currentCKData.includes(';')) {
                    // 可能是cookie格式，按分号分行
                    const formatted = currentCKData.split(';').join(';\n');
                    container.textContent = formatted;
                    showCopySuccess('CK数据已按Cookie格式化显示');
                } else {
                    container.textContent = currentCKData;
                    showCopySuccess('CK数据格式无法识别，显示原始数据');
                }
            }
        }

        // 下载CK数据文件
        function downloadCKData() {
            if (!currentCKData) {
                showCopySuccess('没有可下载的CK数据', 'error');
                return;
            }

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `ck_data_${timestamp}.txt`;

            const blob = new Blob([currentCKData], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            URL.revokeObjectURL(url);
            showCopySuccess(`CK数据已下载为 ${filename}`);
        }

        // 复制到剪贴板函数
        async function copyToClipboard(text) {
            try {
                // 现代浏览器使用Clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    console.log('✅ 使用Clipboard API复制成功');
                    return true;
                }

                // 兼容旧浏览器
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                textArea.style.opacity = '0';
                textArea.style.pointerEvents = 'none';
                document.body.appendChild(textArea);

                textArea.focus();
                textArea.select();
                textArea.setSelectionRange(0, text.length);

                const result = document.execCommand('copy');
                document.body.removeChild(textArea);

                console.log('✅ 使用execCommand复制:', result ? '成功' : '失败');
                return result;
            } catch (error) {
                console.error('❌ 复制失败:', error);
                return false;
            }
        }

        // 显示复制成功提示
        function showCopySuccess(message, type = 'success') {
            // 移除现有的提示
            const existing = document.querySelector('.copy-success');
            if (existing) {
                existing.remove();
            }

            const toast = document.createElement('div');
            toast.className = 'copy-success';
            toast.textContent = message;

            if (type === 'error') {
                toast.style.background = '#dc3545';
            }

            document.body.appendChild(toast);

            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function (event) {
            const ckModal = document.getElementById('ckModal');
            if (event.target === ckModal) {
                closeCKModal();
            }
        });

        // 导出推广操作记录为Excel
        function exportActionsToExcel() {
            console.log('📊 开始导出推广操作记录为Excel...');

            // 检查是否有数据
            if (!currentActionsData || currentActionsData.length === 0) {
                showMessage('没有可导出的数据，请先查询操作记录', 'error');
                return;
            }

            try {
                // 准备Excel数据
                const excelData = prepareExcelData(currentActionsData);

                // 创建工作簿
                const workbook = createWorkbook(excelData);

                // 生成文件名
                const fileName = generateExcelFileName();

                // 导出Excel文件
                exportWorkbook(workbook, fileName);

                showMessage(`Excel文件导出成功：${fileName}`, 'success');
                console.log('✅ Excel导出完成');

            } catch (error) {
                console.error('❌ Excel导出失败:', error);
                showMessage('Excel导出失败: ' + error.message, 'error');
            }
        }

        // 准备Excel数据
        function prepareExcelData(actions) {
            console.log('📋 准备Excel数据，记录数量:', actions.length);

            // Excel表头
            const headers = [
                '推广用户ID',
                '用户ID',
                '操作时间',
                'IP地址',
                'IP省份',
                '操作状态',
                '抖音名称',
                '抖音号',
                'CK数据长度',
                'CK完整数据'
            ];

            // 转换数据
            const rows = actions.map(action => {
                // 处理CK数据 - 保存完整数据
                let ckLength = 0;
                let ckFullData = '';

                if (action.ck && action.ck !== '-') {
                    ckLength = action.ck.length;
                    ckFullData = action.ck; // 保存完整的CK数据
                }

                return [
                    action.promotion_user_id || '',
                    action.user_id || '',
                    action.time || '',
                    action.ip || '',
                    action.ip_province || '',
                    action.status || '',
                    action.douyin_name || '',
                    action.douyin_id || '',
                    ckLength,
                    ckFullData
                ];
            });

            return {
                headers: headers,
                rows: rows
            };
        }

        // 创建工作簿
        function createWorkbook(data) {
            console.log('📊 创建Excel工作簿...');

            // 创建工作表数据
            const wsData = [data.headers, ...data.rows];

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(wsData);

            // 设置列宽
            const colWidths = [
                { wch: 12 }, // 推广用户ID
                { wch: 10 }, // 用户ID
                { wch: 20 }, // 操作时间
                { wch: 15 }, // IP地址
                { wch: 10 }, // IP省份
                { wch: 12 }, // 操作状态
                { wch: 15 }, // 抖音名称
                { wch: 15 }, // 抖音号
                { wch: 12 }, // CK数据长度
                { wch: 80 }  // CK完整数据（增加列宽以容纳完整数据）
            ];
            ws['!cols'] = colWidths;

            // 设置表头样式
            const headerStyle = {
                font: { bold: true, color: { rgb: "FFFFFF" } },
                fill: { fgColor: { rgb: "366092" } },
                alignment: { horizontal: "center", vertical: "center" }
            };

            // 应用表头样式
            for (let i = 0; i < data.headers.length; i++) {
                const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
                if (!ws[cellRef]) ws[cellRef] = {};
                ws[cellRef].s = headerStyle;
            }

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "推广操作记录");

            return wb;
        }

        // 生成Excel文件名
        function generateExcelFileName() {
            const now = new Date();
            const dateStr = now.getFullYear() +
                String(now.getMonth() + 1).padStart(2, '0') +
                String(now.getDate()).padStart(2, '0');
            const timeStr = String(now.getHours()).padStart(2, '0') +
                String(now.getMinutes()).padStart(2, '0') +
                String(now.getSeconds()).padStart(2, '0');

            return `推广操作记录_${dateStr}_${timeStr}.xlsx`;
        }

        // 导出工作簿
        function exportWorkbook(workbook, fileName) {
            console.log('💾 导出Excel文件:', fileName);

            // 生成Excel文件
            const excelBuffer = XLSX.write(workbook, {
                bookType: 'xlsx',
                type: 'array',
                compression: true
            });

            // 创建Blob对象
            const blob = new Blob([excelBuffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;

            // 触发下载
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // 释放URL对象
            window.URL.revokeObjectURL(url);
        }

        console.log('📋 推广用户管理脚本已加载完成');
        console.log('✅ 推广访问数据管理功能已启用');
        console.log('🔧 CK数据复制功能已启用');
        console.log('📊 Excel导出功能已启用');
    </script>
</body>

</html>