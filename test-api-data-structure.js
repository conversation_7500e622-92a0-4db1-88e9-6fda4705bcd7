const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

async function testAPIDataStructure() {
    console.log(colors.bold('🧪 API返回数据结构测试'));
    console.log('='.repeat(70));
    console.log(colors.cyan('目的: 验证API返回的数据结构和省份匹配功能\n'));

    try {
        // 1. 测试获取省份列表
        console.log(colors.blue('📋 1. 获取所有可用省份列表'));
        console.log(colors.cyan('API: GET /api/douyin/get-available-provinces'));
        
        const provincesResponse = await axios.get(`${BASE_URL}/get-available-provinces`);
        console.log(colors.green('✅ 响应数据:'));
        console.log(JSON.stringify(provincesResponse.data, null, 2));
        
        // 分析数据结构
        console.log(colors.yellow('\n📊 数据结构分析:'));
        console.log(`   - code: ${provincesResponse.data.code} (${typeof provincesResponse.data.code})`);
        console.log(`   - msg: "${provincesResponse.data.msg}" (${typeof provincesResponse.data.msg})`);
        console.log(`   - data: Array[${provincesResponse.data.data.length}] (${typeof provincesResponse.data.data})`);
        
        if (provincesResponse.data.data.length > 0) {
            const firstProvince = provincesResponse.data.data[0];
            console.log(`   - data[0].province: "${firstProvince.province}" (${typeof firstProvince.province})`);
            console.log(`   - data[0].available_count: ${firstProvince.available_count} (${typeof firstProvince.available_count})`);
        }

        // 2. 测试获取单个代理（汉字输入）
        console.log(colors.blue('\n🎯 2. 获取单个代理（汉字输入）'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxy-by-province?province=山东省'));
        
        const singleProxyResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=山东省`);
        console.log(colors.green('✅ 响应数据:'));
        console.log(JSON.stringify(singleProxyResponse.data, null, 2));
        
        // 分析数据结构
        console.log(colors.yellow('\n📊 数据结构分析:'));
        console.log(`   - code: ${singleProxyResponse.data.code} (${typeof singleProxyResponse.data.code})`);
        console.log(`   - msg: "${singleProxyResponse.data.msg}" (${typeof singleProxyResponse.data.msg})`);
        
        if (singleProxyResponse.data.data) {
            const proxy = singleProxyResponse.data.data;
            console.log(`   - data: Object (${typeof singleProxyResponse.data.data})`);
            console.log(`   - data.id: ${proxy.id} (${typeof proxy.id})`);
            console.log(`   - data.sk5: "${proxy.sk5}" (${typeof proxy.sk5})`);
            console.log(`   - data.ip: "${proxy.ip}" (${typeof proxy.ip})`);
            console.log(`   - data.province: "${proxy.province}" (${typeof proxy.province})`);
            console.log(`   - data.usage_count: ${proxy.usage_count} (${typeof proxy.usage_count})`);
        } else {
            console.log(`   - data: ${singleProxyResponse.data.data} (${typeof singleProxyResponse.data.data})`);
        }

        // 3. 测试获取单个代理（拼音输入）
        console.log(colors.blue('\n🎯 3. 获取单个代理（拼音输入）'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxy-by-province?province=shandong'));
        
        const pinyinProxyResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=shandong`);
        console.log(colors.green('✅ 响应数据:'));
        console.log(JSON.stringify(pinyinProxyResponse.data, null, 2));

        // 4. 测试批量获取代理
        console.log(colors.blue('\n📦 4. 批量获取代理'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxies-by-province?province=beijing&limit=3'));
        
        const batchResponse = await axios.get(`${BASE_URL}/get-proxies-by-province?province=beijing&limit=3`);
        console.log(colors.green('✅ 响应数据:'));
        console.log(JSON.stringify(batchResponse.data, null, 2));
        
        // 分析数据结构
        console.log(colors.yellow('\n📊 数据结构分析:'));
        console.log(`   - code: ${batchResponse.data.code} (${typeof batchResponse.data.code})`);
        console.log(`   - msg: "${batchResponse.data.msg}" (${typeof batchResponse.data.msg})`);
        console.log(`   - data: Array[${batchResponse.data.data.length}] (${typeof batchResponse.data.data})`);
        
        if (batchResponse.data.data.length > 0) {
            const firstProxy = batchResponse.data.data[0];
            console.log(`   - data[0]: Object (${typeof firstProxy})`);
            console.log(`   - data[0].id: ${firstProxy.id} (${typeof firstProxy.id})`);
            console.log(`   - data[0].sk5: "${firstProxy.sk5}" (${typeof firstProxy.sk5})`);
            console.log(`   - data[0].ip: "${firstProxy.ip}" (${typeof firstProxy.ip})`);
            console.log(`   - data[0].province: "${firstProxy.province}" (${typeof firstProxy.province})`);
            console.log(`   - data[0].usage_count: ${firstProxy.usage_count} (${typeof firstProxy.usage_count})`);
        }

        // 5. 测试不存在的省份
        console.log(colors.blue('\n❌ 5. 测试不存在的省份'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxy-by-province?province=不存在的省份'));
        
        const notFoundResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=不存在的省份`);
        console.log(colors.yellow('⚠️  响应数据:'));
        console.log(JSON.stringify(notFoundResponse.data, null, 2));

        // 6. 测试报告代理状态
        console.log(colors.blue('\n📊 6. 测试报告代理状态'));
        
        if (singleProxyResponse.data.data) {
            const testProxy = singleProxyResponse.data.data;
            console.log(colors.cyan('API: POST /api/douyin/report-proxy-status'));
            
            const reportData = {
                sk5: testProxy.sk5,
                status: 'success'
            };
            
            console.log(colors.yellow('📤 请求数据:'));
            console.log(JSON.stringify(reportData, null, 2));
            
            const reportResponse = await axios.post(`${BASE_URL}/report-proxy-status`, reportData);
            console.log(colors.green('✅ 响应数据:'));
            console.log(JSON.stringify(reportResponse.data, null, 2));
            
            // 分析数据结构
            console.log(colors.yellow('\n📊 数据结构分析:'));
            console.log(`   - code: ${reportResponse.data.code} (${typeof reportResponse.data.code})`);
            console.log(`   - msg: "${reportResponse.data.msg}" (${typeof reportResponse.data.msg})`);
            
            if (reportResponse.data.data) {
                const reportData = reportResponse.data.data;
                console.log(`   - data: Object (${typeof reportResponse.data.data})`);
                console.log(`   - data.proxy_id: ${reportData.proxy_id} (${typeof reportData.proxy_id})`);
                console.log(`   - data.sk5: "${reportData.sk5}" (${typeof reportData.sk5})`);
                console.log(`   - data.status: "${reportData.status}" (${typeof reportData.status})`);
                console.log(`   - data.error_msg: ${reportData.error_msg} (${typeof reportData.error_msg})`);
                console.log(`   - data.usage_count: ${reportData.usage_count} (${typeof reportData.usage_count})`);
                console.log(`   - data.updated_status: ${reportData.updated_status} (${typeof reportData.updated_status})`);
            }
        }

        console.log(colors.bold('\n🎉 API数据结构测试完成!'));
        console.log('='.repeat(70));
        
        // 总结数据结构
        console.log(colors.bold('\n📋 API返回数据结构总结:'));
        console.log(colors.green('\n✅ 通用响应格式:'));
        console.log('   {');
        console.log('     "code": 0,           // Number: 0=成功, -1=失败');
        console.log('     "msg": "响应消息",    // String: 描述信息');
        console.log('     "data": {}          // Object/Array/null: 具体数据');
        console.log('   }');
        
        console.log(colors.green('\n✅ 单个代理对象结构:'));
        console.log('   {');
        console.log('     "id": 1,                    // Number: 代理ID');
        console.log('     "sk5": "host:port|user|pass", // String: SOCKS5地址');
        console.log('     "ip": "***********",       // String: IP地址');
        console.log('     "province": "山东省",       // String: 省份名称');
        console.log('     "usage_count": 5            // Number: 使用次数');
        console.log('   }');
        
        console.log(colors.green('\n✅ 省份统计对象结构:'));
        console.log('   {');
        console.log('     "province": "山东省",       // String: 省份名称');
        console.log('     "available_count": 3        // Number: 可用代理数量');
        console.log('   }');

    } catch (error) {
        console.error(colors.red('❌ 测试过程中发生错误:'));
        console.error('错误信息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
console.log(colors.bold('🚀 API数据结构测试脚本'));
console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
console.log('\n' + colors.yellow('⏳ 3秒后开始测试...'));

setTimeout(() => {
    testAPIDataStructure().catch(error => {
        console.error(colors.red('💥 测试脚本执行失败:'), error.message);
        process.exit(1);
    });
}, 3000);
