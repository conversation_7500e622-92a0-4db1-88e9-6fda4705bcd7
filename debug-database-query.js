const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 调试数据库查询
async function debugDatabaseQuery() {
  console.log('🔍 调试数据库查询...');
  
  const testUserId = '1001';
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查当前日期
    console.log('\n1️⃣ 检查数据库当前日期...');
    const [dateInfo] = await connection.execute(`SELECT NOW() as current_time, CURDATE() as current_date`);
    console.log('数据库当前时间:', dateInfo[0]);
    
    // 2. 检查promotion_daily_stats表中的所有数据
    console.log('\n2️⃣ 检查promotion_daily_stats表中的所有数据...');
    const [allStats] = await connection.execute(
      `SELECT id, promotion_user_id, stat_date, DATE(stat_date) as date_only,
              visit_count, unique_ip_count, scan_count, success_count, fail_count, expire_count
       FROM promotion_daily_stats 
       WHERE promotion_user_id = ?
       ORDER BY stat_date DESC`,
      [testUserId]
    );
    
    console.log(`找到 ${allStats.length} 条记录:`);
    allStats.forEach((stat, index) => {
      console.log(`   ${index + 1}. ID=${stat.id}, 日期=${stat.date_only}, 访问=${stat.visit_count}, 成功=${stat.success_count}`);
    });
    
    // 3. 使用API中相同的查询
    console.log('\n3️⃣ 使用API中相同的查询...');
    const [apiQuery] = await connection.execute(
      `SELECT
        visit_count,
        unique_ip_count,
        scan_count,
        success_count,
        fail_count,
        expire_count
       FROM promotion_daily_stats
       WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    console.log(`API查询结果 (${apiQuery.length} 条记录):`);
    if (apiQuery.length > 0) {
      console.log('   数据:', apiQuery[0]);
    } else {
      console.log('   ❌ 没有找到匹配的记录');
    }
    
    // 4. 检查日期匹配问题
    console.log('\n4️⃣ 检查日期匹配问题...');
    const [dateCheck] = await connection.execute(
      `SELECT 
        stat_date,
        DATE(stat_date) as date_only,
        CURDATE() as current_date,
        DATE(stat_date) = CURDATE() as is_today
       FROM promotion_daily_stats 
       WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    console.log('日期匹配检查:');
    dateCheck.forEach((row, index) => {
      console.log(`   ${index + 1}. 存储日期=${row.date_only}, 当前日期=${row.current_date}, 是否匹配=${row.is_today}`);
    });
    
    // 5. 如果没有今日数据，创建一条
    if (apiQuery.length === 0) {
      console.log('\n5️⃣ 没有今日数据，创建测试数据...');
      
      // 删除可能存在的今日数据
      await connection.execute(
        `DELETE FROM promotion_daily_stats 
         WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
        [testUserId]
      );
      
      // 插入新的今日数据
      const [insertResult] = await connection.execute(
        `INSERT INTO promotion_daily_stats 
         (promotion_user_id, stat_date, visit_count, unique_ip_count, 
          scan_count, success_count, fail_count, expire_count, created_at, updated_at)
         VALUES (?, CURDATE(), 8, 2, 1, 6, 1, 0, NOW(), NOW())`,
        [testUserId]
      );
      
      console.log(`✅ 插入新数据，ID: ${insertResult.insertId}`);
      
      // 验证插入的数据
      const [verifyQuery] = await connection.execute(
        `SELECT
          visit_count,
          unique_ip_count,
          scan_count,
          success_count,
          fail_count,
          expire_count
         FROM promotion_daily_stats
         WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
        [testUserId]
      );
      
      console.log('验证插入的数据:');
      if (verifyQuery.length > 0) {
        console.log('   ✅ 数据:', verifyQuery[0]);
      } else {
        console.log('   ❌ 验证失败，仍然没有数据');
      }
    }
    
    // 6. 检查promotion_visits和promotion_actions表
    console.log('\n6️⃣ 检查原始数据表...');
    
    const [visitCount] = await connection.execute(
      `SELECT COUNT(*) as total, COUNT(DISTINCT visitor_ip) as unique_ips
       FROM promotion_visits WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    const [actionCount] = await connection.execute(
      `SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count
       FROM promotion_actions WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    console.log('原始数据统计:');
    console.log(`   访问记录: 总数=${visitCount[0].total}, 独立IP=${visitCount[0].unique_ips}`);
    console.log(`   操作记录: 总数=${actionCount[0].total}, 成功=${actionCount[0].success_count}`);
    
  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
  
  console.log('\n🎉 数据库调试完成！');
  console.log('\n💡 现在请重新测试API，应该能看到正确的数据了。');
}

// 运行调试
debugDatabaseQuery().catch(console.error);
