require("./db/initdb-disabled"); // 使用禁用MongoDB的初始化文件
const fs = require("fs");
const path = require("path");
const multer = require("multer");
const { publicPath } = require("./serverOpt"); // 移除mongoUrl
const createError = require("http-errors");
const express = require("express"); //ws相关

// 确保statuc目录存在
const statucDir = path.join(publicPath, "statuc");
if (!fs.existsSync(statucDir)) {
  fs.mkdirSync(statucDir, { recursive: true });
}

// 配置multer文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, statucDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, "file-" + uniqueSuffix + path.extname(file.originalname));
  },
});
const upload = multer({ storage: storage });
const expressWs = require("express-ws");
const logger = require("morgan");
const app = express();
expressWs(app); //ws相关

const session = require("express-session");
// const MongoStore = require("connect-mongo"); // 禁用MongoDB session store
// 静态文件服务配置
app.get("/favicon.ico", (req, res) => res.status(204).end());

// 推广用户登录页面路由重定向（必须在静态文件之前）
app.get("/promoter-login", (req, res) => {
  // 保留URL参数
  const queryString = req.url.includes("?") ? req.url.split("?")[1] : "";
  const redirectUrl = queryString
    ? `/promoter-login.html?${queryString}`
    : "/promoter-login.html";
  res.redirect(redirectUrl);
});

// 推广用户仪表板路由重定向（重定向到干净版）
app.get("/promoter-dashboard", (req, res) => {
  const queryString = req.url.includes("?") ? req.url.split("?")[1] : "";
  const redirectUrl = queryString
    ? `/clean-promoter-dashboard.html?${queryString}`
    : "/clean-promoter-dashboard.html";
  res.redirect(redirectUrl);
});

// 主静态目录
app.use(
  express.static(publicPath, {
    setHeaders: (res, path) => {
      console.log(`静态文件访问: ${path}`);
    },
  })
);

// 媒体资源目录 - statuc
app.use(
  "/statuc",
  express.static(path.join(publicPath, "statuc"), {
    setHeaders: (res, path) => {
      console.log(`媒体资源访问: ${path}`);
      // 设置媒体资源缓存
      if (path.match(/\.(jpg|jpeg|png|gif|mp4|webm|ogg)$/)) {
        res.setHeader("Cache-Control", "public, max-age=86400");
      }
    },
  })
);

// 本地文件上传路由
app.post("/localUpload", upload.array("files"), (req, res) => {
  const files = req.files;
  if (!files || files.length === 0) {
    return res.status(400).json({ error: "未上传文件" });
  }

  const paths = files.map((file) => {
    return `/statuc/${file.filename}`;
  });

  // 返回包含baseUrl、paths和fileTypes的完整响应
  res.json({
    success: true,
    baseUrl: `http://${req.get("host")}`,
    paths: paths,
    fileTypes: files.map((file) =>
      path.extname(file.originalname).replace(".", "")
    ),
  });
});

// 媒体资源路由
app.use(
  "/media",
  express.static(path.join(publicPath, "media"), {
    setHeaders: (res, path) => {
      console.log(`媒体资源访问: ${path}`);
      // 设置媒体资源缓存
      if (path.match(/\.(jpg|jpeg|png|gif)$/)) {
        res.setHeader("Cache-Control", "public, max-age=86400"); // 图片缓存1天
      } else if (path.match(/\.(mp4|webm|ogg)$/)) {
        res.setHeader("Cache-Control", "public, max-age=86400"); // 视频缓存1天
      }
    },
  })
);

// 媒体资源访问示例路由
app.get("/media-examples", (req, res) => {
  const examples = {
    images: [
      { name: "示例图片1", path: "/media/images/example1.jpg" },
      { name: "示例图片2", path: "/media/images/example2.jpg" },
    ],
    videos: [
      { name: "示例视频1", path: "/media/videos/demo.mp4" },
      { name: "示例视频2", path: "/media/videos/sample.mp4" },
    ],
  };

  // 生成完整URL
  examples.images.forEach((img) => {
    img.url = `http://${req.get("host")}${img.path}`;
  });
  examples.videos.forEach((video) => {
    video.url = `http://${req.get("host")}${video.path}`;
  });

  res.json({
    message: "媒体资源访问示例",
    baseUrl: `http://${req.get("host")}/media`,
    tips: "请将媒体文件存放在public/media目录下",
    examples: examples,
  });
});

// 静态文件测试路由
app.get("/test-static", (req, res) => {
  const testFiles = ["/images/logo.png", "/static/version.txt"];

  res.json({
    message: "静态文件服务测试",
    baseUrl: `http://${req.get("host")}`,
    testFiles: testFiles.map((file) => ({
      path: file,
      url: `http://${req.get("host")}${file}`,
      exists: fs.existsSync(path.join(publicPath, file)),
    })),
  });
});

// 配置中间件
app.use(logger("dev"));
app.use(express.json({ limit: "50 mb" }));
app.use(express.urlencoded({ extended: false, limit: "50mb" }));

//脚本更新相关
const jbGengXin = require("./routes/jbGengXin");
const jbUpFile = require("./routes/jbUpFile");
app.use("/", jbGengXin);
app.use("/", jbUpFile);

//云控ws相关
var wsRouter = require("./routes/wsRouter");
app.use("/wsRouter", wsRouter);

// app.use(cookieParser());
app.use(
  session({
    resave: true, //resave是指每次请求都重新设置session cookie，假设你的cookie是10分钟过期，每次请求都会再设置10分钟
    secret: "jinZhu",
    saveUninitialized: true, // 改为true，确保session被创建
    name: "connect.sid", // 明确指定session名称
    cookie: {
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7天过期
      httpOnly: false, // 改为false，允许JavaScript访问cookie
      secure: false, // 开发环境设为false
      sameSite: "lax", // 添加sameSite设置
    },
    // 使用内存存储替代MongoDB存储（注意：重启服务器会丢失session）
    // store: new MongoStore({
    //   mongoUrl: mongoUrl,
    //   ttl: 7 * 24 * 60 * 60,
    // }),
  })
);

// 配置路由
const indexRouter = require("./routes/index-disabled"); // 使用禁用MongoDB的index路由
const user = require("./routes/user-disabled"); // 使用禁用MongoDB的用户路由
//云控分组设备相关
var indexDevice = require("./routes/indexDevice");
var indexGroup = require("./routes/indexGroup");
var dyTasks = require("./routes/dyTask");
app.use("/indexDevice", indexDevice);
app.use("/indexGroup", indexGroup);

//用户登录相关
app.use("/", indexRouter);
app.use("/user", user);
app.use("/dyTask", dyTasks);

// 抖音账号管理路由 - 必须在404错误处理之前
const douyinAccountRouter = require("./routes/douyinAccount");
app.use("/api/douyin", douyinAccountRouter);

// 抖音IP代理管理路由
const douyinIPRouter = require("./routes/douyinIP");
app.use("/api/douyin", douyinIPRouter);

// 推广用户管理路由
const promotionManagementRouter = require("./routes/promotionManagement");
app.use("/api/promotion", promotionManagementRouter);

// 推广管理员路由
const promotionAdminRouter = require("./routes/promotionAdmin");
app.use("/api/promotion", promotionAdminRouter);

// 推广操作数据保存路由
const promotionActionsRouter = require("./routes/promotionActions");
app.use("/api/promotion-actions", promotionActionsRouter);

// 验证码管理路由
const captchaRouter = require("./routes/captcha");
app.use("/api/captcha", captchaRouter);

// 推广链接访问跟踪中间件
const {
  promotionTrackingMiddleware,
} = require("./middleware/promotionMiddleware");
app.use(promotionTrackingMiddleware);

// API路由
const apiRouter = require("./routes/apiRouter");
app.use("/api", apiRouter);

// 其他路由
const { router: mysqlRouter } = require("./db/mysql");
app.use("/device", mysqlRouter);

// 捕捉 404 错误，并转发到错误处理器
app.use(function (req, res, next) {
  next(createError(404, "未找到页面或内容"));
});

// 错误处理
app.use(function (err, req, res, next) {
  const errMsg = err.message;
  if (errMsg == "未找到页面或内容") {
    console.error(`${errMsg}:${req.url}`);
  } else {
    console.error(`node未处理的错误:${req.url}`, err);
  }
  res.status(err.status || 500).json({
    code: err.status || 500,
    message: errMsg,
  });
});

module.exports = app;
