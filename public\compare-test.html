<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对比测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .test-section { border: 1px solid #ddd; border-radius: 5px; padding: 20px; }
        .test-section h3 { margin-top: 0; color: #333; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin: 15px 0; padding: 15px; border-radius: 5px; white-space: pre-wrap; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 登录跳转对比测试</h1>
        
        <div class="test-grid">
            <!-- 测试1: 直接API调用 -->
            <div class="test-section">
                <h3>测试1: 直接API调用（正常工作）</h3>
                <button onclick="testDirectAPI()">测试直接API</button>
                <div id="directResult" class="result info">点击按钮开始测试...</div>
            </div>
            
            <!-- 测试2: 模拟原始登录页面 -->
            <div class="test-section">
                <h3>测试2: 模拟原始登录页面</h3>
                <button onclick="testOriginalLogin()">测试原始登录逻辑</button>
                <div id="originalResult" class="result info">点击按钮开始测试...</div>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>🔧 跳转测试</h3>
            <button onclick="testDirectJump()">测试直接跳转</button>
            <button onclick="testDelayedJump()">测试延迟跳转</button>
            <button onclick="testLocationReplace()">测试location.replace</button>
            <div id="jumpResult" class="result info">跳转测试结果...</div>
        </div>
    </div>

    <script>
        function addLog(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
            element.className = `result ${type}`;
            element.scrollTop = element.scrollHeight;
            console.log(message);
        }
        
        // 测试1: 直接API调用（这个应该正常工作）
        async function testDirectAPI() {
            const elementId = 'directResult';
            document.getElementById(elementId).textContent = '';
            
            addLog(elementId, '🚀 开始直接API测试');
            
            try {
                // 登录
                addLog(elementId, '1️⃣ 发送登录请求...');
                const loginResponse = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ user_id: '1001', password: '123456' })
                });
                
                const loginResult = await loginResponse.json();
                addLog(elementId, `登录响应: ${JSON.stringify(loginResult)}`);
                
                if (loginResult.code === 0) {
                    addLog(elementId, '✅ 登录成功');
                    
                    // 获取数据
                    addLog(elementId, '2️⃣ 获取统计数据...');
                    const statsResponse = await fetch('/api/promotion/promoter/today-stats', {
                        credentials: 'include'
                    });
                    
                    const statsResult = await statsResponse.json();
                    addLog(elementId, `统计数据: ${JSON.stringify(statsResult)}`);
                    
                    if (statsResult.code === 0) {
                        addLog(elementId, '✅ 数据获取成功', 'success');
                        addLog(elementId, '🎉 直接API测试完全正常！');
                    } else {
                        addLog(elementId, '❌ 数据获取失败', 'error');
                    }
                } else {
                    addLog(elementId, '❌ 登录失败', 'error');
                }
                
            } catch (error) {
                addLog(elementId, `❌ 测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试2: 模拟原始登录页面的逻辑
        async function testOriginalLogin() {
            const elementId = 'originalResult';
            document.getElementById(elementId).textContent = '';
            
            addLog(elementId, '🚀 开始模拟原始登录页面测试');
            
            try {
                addLog(elementId, '1️⃣ 模拟表单提交...');
                
                // 模拟原始页面的登录逻辑
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ user_id: '1001', password: '123456' })
                });
                
                const result = await response.json();
                addLog(elementId, `登录响应: ${JSON.stringify(result)}`);
                
                if (result.code === 0) {
                    addLog(elementId, '✅ 登录成功，准备跳转');
                    
                    // 模拟原始页面的跳转逻辑
                    addLog(elementId, '2️⃣ 执行跳转逻辑...');
                    addLog(elementId, '🔄 window.location.href = "/promoter-dashboard"');
                    
                    // 不实际跳转，只是测试逻辑
                    addLog(elementId, '⚠️ 跳转被阻止（测试模式）');
                    addLog(elementId, '🤔 如果这里正常，说明问题在原始页面的其他地方', 'success');
                    
                } else {
                    addLog(elementId, `❌ 登录失败: ${result.msg}`, 'error');
                }
                
            } catch (error) {
                addLog(elementId, `❌ 测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试直接跳转
        function testDirectJump() {
            addLog('jumpResult', '🚀 测试直接跳转');
            addLog('jumpResult', '执行: window.location.href = "/promoter-dashboard"');
            
            setTimeout(() => {
                addLog('jumpResult', '⏰ 3秒后执行跳转...');
                setTimeout(() => {
                    window.location.href = '/promoter-dashboard';
                }, 3000);
            }, 1000);
        }
        
        // 测试延迟跳转
        function testDelayedJump() {
            addLog('jumpResult', '🚀 测试延迟跳转');
            addLog('jumpResult', '延迟2秒后跳转...');
            
            setTimeout(() => {
                addLog('jumpResult', '⏰ 执行延迟跳转');
                window.location.href = '/promoter-dashboard';
            }, 2000);
        }
        
        // 测试location.replace
        function testLocationReplace() {
            addLog('jumpResult', '🚀 测试location.replace');
            addLog('jumpResult', '执行: window.location.replace("/promoter-dashboard")');
            
            setTimeout(() => {
                addLog('jumpResult', '⏰ 3秒后执行replace...');
                setTimeout(() => {
                    window.location.replace('/promoter-dashboard');
                }, 3000);
            }, 1000);
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            console.log('📋 对比测试页面已加载');
            addLog('directResult', '准备进行直接API测试');
            addLog('originalResult', '准备进行原始登录逻辑测试');
            addLog('jumpResult', '准备进行跳转测试');
        };
    </script>
</body>
</html>
