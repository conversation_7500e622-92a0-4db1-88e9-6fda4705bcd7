const axios = require('axios');
const mysql = require('mysql2/promise');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
};

// 测试多表保存功能
async function testMultiTableSave() {
  console.log('🧪 测试多表保存功能...');
  
  const baseURL = 'http://localhost:15001';
  
  try {
    // 1. 测试您提供的实际数据
    console.log('\n📋 1. 测试实际数据格式（会话session_1750401732380）:');
    
    const realData = {
      promotion_user_id: '1001',
      action_type: ['scan', 'login_success'], // 数组格式
      ip_address: '**************',
      ip_province: '浙江省',
      douyin_name: '恢复恢复',
      douyin_id: '78184614511',
      ck_data: '[{"name":"bd_ticket_guard_client_data","value":"eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRmdwL05PZ0VIV1VFemdrN1Z4aXZXY21SS0Ura3E4bCtpaENXMlJacXFCcTU1ZDAzVVRaQ3pUNFlpZlRSOG1UUks2aktTVEhpUzZqSklVcC9HekNuOUk9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D","domain":".douyin.com"}]',
      user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      extra_data: {
        session_id: "session_1750401732380",
        login_count: 1,
        ip_source: "API_PROXY",
        timestamp: new Date().toISOString(),
        login_time: new Date().toISOString(),
        login_method: "qr_code_scan",
        user_info_extracted: true,
        referer: "http://localhost:15001/promo/1001"
      }
    };
    
    console.log('📤 发送数据到API...');
    const response = await axios.post(`${baseURL}/api/promotion-actions/save`, realData);
    
    if (response.data.code === 0) {
      console.log('✅ 多表保存成功:');
      console.log('   访问记录ID:', response.data.data.visit_id);
      console.log('   操作记录ID:', response.data.data.action_id);
      console.log('   推广用户ID:', response.data.data.promotion_user_id);
      console.log('   操作类型:', response.data.data.action_type);
      console.log('   会话ID:', response.data.data.session_id);
      console.log('   更新的表:', response.data.data.tables_updated);
      
      // 验证数据库中的数据
      await verifyDatabaseData(response.data.data);
      
    } else {
      console.log('❌ 多表保存失败:', response.data.msg);
    }
    
    // 2. 测试不同操作类型
    console.log('\n📋 2. 测试不同操作类型:');
    
    const actionTypes = ['scan', 'login_success', 'login_fail', 'request_expire'];
    
    for (const actionType of actionTypes) {
      console.log(`\n🔄 测试操作类型: ${actionType}`);
      
      const testData = {
        promotion_user_id: '1002',
        action_type: actionType,
        ip_address: '*************',
        ip_province: '北京市',
        douyin_name: `测试用户-${actionType}`,
        douyin_id: `test_${actionType}_123`,
        ck_data: `sessionid=test_${actionType}_456`,
        user_agent: 'Mozilla/5.0 (Test Browser)',
        extra_data: {
          session_id: `session_test_${actionType}_${Date.now()}`,
          test_action: actionType,
          timestamp: new Date().toISOString()
        }
      };
      
      try {
        const testResponse = await axios.post(`${baseURL}/api/promotion-actions/save`, testData);
        
        if (testResponse.data.code === 0) {
          console.log(`  ✅ ${actionType} 保存成功，访问ID: ${testResponse.data.data.visit_id}, 操作ID: ${testResponse.data.data.action_id}`);
        } else {
          console.log(`  ❌ ${actionType} 保存失败: ${testResponse.data.msg}`);
        }
      } catch (error) {
        console.log(`  ❌ ${actionType} 请求失败: ${error.message}`);
      }
      
      // 短暂延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 3. 验证每日统计数据
    console.log('\n📋 3. 验证每日统计数据:');
    await verifyDailyStats();
    
    console.log('\n🎉 多表保存功能测试完成！');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 验证数据库中的数据
async function verifyDatabaseData(responseData) {
  console.log('\n🔍 验证数据库中的数据...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    // 验证promotion_visits表
    const [visitRecord] = await connection.execute(
      'SELECT * FROM promotion_visits WHERE id = ?',
      [responseData.visit_id]
    );
    
    if (visitRecord.length > 0) {
      console.log('✅ promotion_visits表记录存在:');
      console.log(`   ID: ${visitRecord[0].id}, 推广用户: ${visitRecord[0].promotion_user_id}, IP: ${visitRecord[0].visitor_ip}`);
    } else {
      console.log('❌ promotion_visits表记录不存在');
    }
    
    // 验证promotion_actions表
    const [actionRecord] = await connection.execute(
      'SELECT * FROM promotion_actions WHERE id = ?',
      [responseData.action_id]
    );
    
    if (actionRecord.length > 0) {
      console.log('✅ promotion_actions表记录存在:');
      console.log(`   ID: ${actionRecord[0].id}, 推广用户: ${actionRecord[0].promotion_user_id}, 操作类型: ${actionRecord[0].action_type}`);
    } else {
      console.log('❌ promotion_actions表记录不存在');
    }
    
    // 验证promotion_daily_stats表
    const today = new Date().toISOString().split('T')[0];
    const [statsRecord] = await connection.execute(
      'SELECT * FROM promotion_daily_stats WHERE promotion_user_id = ? AND stat_date = ?',
      [responseData.promotion_user_id, today]
    );
    
    if (statsRecord.length > 0) {
      console.log('✅ promotion_daily_stats表记录存在:');
      console.log(`   推广用户: ${statsRecord[0].promotion_user_id}, 日期: ${statsRecord[0].stat_date}`);
      console.log(`   访问次数: ${statsRecord[0].visit_count}, 独立IP: ${statsRecord[0].unique_ip_count}`);
      console.log(`   扫码: ${statsRecord[0].scan_count}, 成功: ${statsRecord[0].success_count}, 失败: ${statsRecord[0].fail_count}`);
    } else {
      console.log('❌ promotion_daily_stats表记录不存在');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 验证数据库数据失败:', error.message);
  }
}

// 验证每日统计数据
async function verifyDailyStats() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    const today = new Date().toISOString().split('T')[0];
    
    // 获取今日所有推广用户的统计
    const [dailyStats] = await connection.execute(
      `SELECT 
        promotion_user_id,
        visit_count,
        unique_ip_count,
        scan_count,
        success_count,
        fail_count,
        expire_count,
        updated_at
       FROM promotion_daily_stats 
       WHERE stat_date = ?
       ORDER BY promotion_user_id`,
      [today]
    );
    
    console.log(`📊 今日(${today})推广统计数据:`);
    
    if (dailyStats.length === 0) {
      console.log('   暂无统计数据');
    } else {
      dailyStats.forEach((stat, index) => {
        console.log(`   ${index + 1}. 推广用户${stat.promotion_user_id}:`);
        console.log(`      访问: ${stat.visit_count}次, 独立IP: ${stat.unique_ip_count}个`);
        console.log(`      扫码: ${stat.scan_count}次, 成功: ${stat.success_count}次, 失败: ${stat.fail_count}次, 过期: ${stat.expire_count}次`);
        console.log(`      更新时间: ${stat.updated_at}`);
      });
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 验证每日统计失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testMultiTableSave();
}

module.exports = testMultiTableSave;
