const mysql = require('mysql2/promise');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4"
};

async function modifyUserTable() {
  console.log('🔧 开始修改user表结构以适应推广用户管理...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查现有字段
    const [existingColumns] = await connection.execute('DESCRIBE user');
    const existingFieldNames = existingColumns.map(col => col.Field);
    console.log('📋 当前字段:', existingFieldNames.join(', '));
    
    // 要添加的字段
    const fieldsToAdd = [
      {
        name: 'user_type',
        sql: `ALTER TABLE user ADD COLUMN user_type ENUM('normal', 'promoter') DEFAULT 'normal' COMMENT '用户类型'`
      },
      {
        name: 'promotion_link',
        sql: `ALTER TABLE user ADD COLUMN promotion_link VARCHAR(500) NULL COMMENT '推广链接'`
      },
      {
        name: 'promoter_id',
        sql: `ALTER TABLE user ADD COLUMN promoter_id VARCHAR(50) NULL COMMENT '推广用户ID'`
      },
      {
        name: 'visit_count',
        sql: `ALTER TABLE user ADD COLUMN visit_count INT DEFAULT 0 COMMENT '访问次数'`
      },
      {
        name: 'unique_ip_count',
        sql: `ALTER TABLE user ADD COLUMN unique_ip_count INT DEFAULT 0 COMMENT '独立IP数'`
      },
      {
        name: 'scan_count',
        sql: `ALTER TABLE user ADD COLUMN scan_count INT DEFAULT 0 COMMENT '扫码数量'`
      },
      {
        name: 'success_count',
        sql: `ALTER TABLE user ADD COLUMN success_count INT DEFAULT 0 COMMENT '成功数量'`
      },
      {
        name: 'fail_count',
        sql: `ALTER TABLE user ADD COLUMN fail_count INT DEFAULT 0 COMMENT '失败数量'`
      },
      {
        name: 'expire_count',
        sql: `ALTER TABLE user ADD COLUMN expire_count INT DEFAULT 0 COMMENT '过期数量'`
      },
      {
        name: 'last_stat_update',
        sql: `ALTER TABLE user ADD COLUMN last_stat_update TIMESTAMP NULL COMMENT '最后统计更新时间'`
      }
    ];
    
    // 添加字段
    for (const field of fieldsToAdd) {
      if (!existingFieldNames.includes(field.name)) {
        try {
          await connection.execute(field.sql);
          console.log(`✅ 添加字段成功: ${field.name}`);
        } catch (error) {
          console.error(`❌ 添加字段失败 ${field.name}:`, error.message);
        }
      } else {
        console.log(`⚠️  字段已存在: ${field.name}`);
      }
    }
    
    // 添加索引
    try {
      await connection.execute('ALTER TABLE user ADD INDEX idx_promoter_id (promoter_id)');
      console.log('✅ 添加索引成功: idx_promoter_id');
    } catch (error) {
      if (error.code === 'ER_DUP_KEYNAME') {
        console.log('⚠️  索引已存在: idx_promoter_id');
      } else {
        console.error('❌ 添加索引失败 idx_promoter_id:', error.message);
      }
    }
    
    try {
      await connection.execute('ALTER TABLE user ADD INDEX idx_user_type (user_type)');
      console.log('✅ 添加索引成功: idx_user_type');
    } catch (error) {
      if (error.code === 'ER_DUP_KEYNAME') {
        console.log('⚠️  索引已存在: idx_user_type');
      } else {
        console.error('❌ 添加索引失败 idx_user_type:', error.message);
      }
    }
    
    // 插入示例推广用户数据
    console.log('\n📊 插入示例推广用户数据...');
    
    const promotionUsers = [
      {
        username: 'promoter1001',
        password: '123456',
        status: 1,
        user_type: 'promoter',
        promoter_id: '1001',
        promotion_link: 'http://localhost:15001?id=1001',
        ckcount: 0
      },
      {
        username: 'promoter1002',
        password: '123456',
        status: 1,
        user_type: 'promoter',
        promoter_id: '1002',
        promotion_link: 'http://localhost:15001?id=1002',
        ckcount: 0
      }
    ];
    
    for (const userData of promotionUsers) {
      try {
        await connection.execute(
          `INSERT INTO user (username, password, status, user_type, promoter_id, promotion_link, ckcount)
           VALUES (?, ?, ?, ?, ?, ?, ?)
           ON DUPLICATE KEY UPDATE
           user_type = VALUES(user_type),
           promoter_id = VALUES(promoter_id),
           promotion_link = VALUES(promotion_link)`,
          [
            userData.username,
            userData.password,
            userData.status,
            userData.user_type,
            userData.promoter_id,
            userData.promotion_link,
            userData.ckcount
          ]
        );
        console.log(`✅ 插入/更新推广用户: ${userData.username} (ID: ${userData.promoter_id})`);
      } catch (error) {
        console.error(`❌ 插入推广用户失败 ${userData.username}:`, error.message);
      }
    }
    
    // 验证结果
    console.log('\n🔍 验证修改结果...');
    
    const [newColumns] = await connection.execute('DESCRIBE user');
    console.log('\n📋 修改后的user表结构:');
    newColumns.forEach(col => {
      console.log(`- ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? 'DEFAULT ' + col.Default : ''}`);
    });
    
    const [promoters] = await connection.execute(
      "SELECT username, promoter_id, promotion_link, user_type, status FROM user WHERE user_type = 'promoter'"
    );
    console.log('\n📊 推广用户数据:');
    promoters.forEach(user => {
      console.log(`✅ ${user.username} (ID: ${user.promoter_id}) - ${user.promotion_link} - 状态: ${user.status ? '启用' : '禁用'}`);
    });
    
    await connection.end();
    
    console.log('\n🎉 user表结构修改完成！');
    console.log('\n📋 现在可以使用user表进行推广用户管理了');
    
  } catch (error) {
    console.error('💥 修改失败:', error.message);
    process.exit(1);
  }
}

modifyUserTable();
