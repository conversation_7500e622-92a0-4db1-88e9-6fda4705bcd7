const mongoose = require('mongoose');
const { mongoUrl } = require('../serverOpt');
const mysql = require('./mysql.js');

// MongoDB连接配置
const connectionOptions = {
    connectTimeoutMS: 30000, // 连接超时时间(毫秒)
    socketTimeoutMS: 30000, // socket超时时间(毫秒)
    serverSelectionTimeoutMS: 5000, // 服务器选择超时时间(毫秒)
    retryWrites: true, // 启用重试写入
    retryReads: true // 启用重试读取
};

// 连接数据库
mongoose.connect(mongoUrl, connectionOptions);

// 全局配置
mongoose.set('strictPopulate', false);
const db = mongoose.connection;

// 增强错误处理
db.on('error', (error) => {
    console.error('数据库连接错误:', error.message);
    console.error('错误堆栈:', error.stack);
});

db.on('connected', () => {
    console.log('数据库已连接');
    console.log('连接状态:', mongoose.connection.readyState); // 1 = connected
});

db.on('disconnected', () => {
    console.warn('数据库连接断开');
});

db.on('reconnected', () => {
    console.log('数据库重新连接成功');
});

// 添加连接测试方法
const testConnection = async () => {
    try {
        await mongoose.connection.db.admin().ping();
        console.log('数据库连接测试成功');
        return true;
    } catch (error) {
        console.error('数据库连接测试失败:', error);
        return false;
    }
};

// 初始连接检查
db.once('open', async function () {
    console.log("数据库初始连接成功");
    await testConnection();
});

// 保持原有导出方式确保兼容性
module.exports = mongoose;

// 同时提供额外功能
module.exports.testConnection = testConnection;
module.exports.getConnectionStatus = () => mongoose.connection.readyState;


// mongoose.connect('mongodb://127.0.0.1/ceshinew', {
//     // useNewUrlParser: true,//确保链接地址被正确解析  //从 MongoDB Node.js 驱动的 4.0.0 版本开始 弃用
//     // useUnifiedTopology: true,//处理连接、故障转移等更可靠 ////从 MongoDB Node.js 驱动的 4.0.0 版本开始 弃用
//     // useFindAndModify: false,//使用标准的 findOneAndUpdate() 方法，而不是 原生的findAndModify()//新版mongoose移除此参数
//     // useCreateIndex: true//定义索引时使用 createIndex() 而不是过时的 ensureIndex(),提升性能////新版mongoose移除此参数
// });





/*
以下是验证密码 多次输错 锁定
const bcrypt = require('bcryptjs');

// 存储用户登录尝试的次数和锁定状态
let userAttempts = {
    // userName: { attempts: 0, lockUntil: null }
};

async function attemptLogin(userName, inputPassword) {
    // 检查用户是否已被锁定
    if (userAttempts[userName] && userAttempts[userName].lockUntil && userAttempts[userName].lockUntil > Date.now()) {
        throw new Error('您的账户已被锁定，请稍后再试');
    }

    // 假设我们从数据库获取用户信息
    const user = getUserFromDatabase(userName);

    // 验证密码
    const isMatch = await bcrypt.compare(inputPassword, user.userPass);
    if (isMatch) {
        // 重置尝试次数
        userAttempts[userName] = { attempts: 0, lockUntil: null };
        return true; // 登录成功
    } else {
        // 增加尝试次数
        let attempts = (userAttempts[userName] && userAttempts[userName].attempts) || 0;
        attempts++;

        if (attempts >= 3) {
            // 锁定账户，例如30分钟
            userAttempts[userName] = { attempts: attempts, lockUntil: Date.now() + 30 * 60 * 1000 };
        } else {
            userAttempts[userName] = { attempts: attempts, lockUntil: null };
        }

        throw new Error('密码错误');
    }
}

async function run() {
    try {
        await attemptLogin('rootadmin', 'admin123..');
        console.log('登录成功！');
    } catch (e) {
        console.error(e.message);
    }
}

run();

*/


