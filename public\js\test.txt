   if (taskName == "提交评论区操作数据") {
                    let aim = taskData.keyword;
                    // layer.msg(aim);
                    // if (!aim) {
                    //   layer.msg("关键词");
                    //   return false;
                    // }
                    let commentContent = taskData.commentContent;
                    // if (!commentContent) {
                    //   layer.msg("请输入评论内容");
                    //   return false;
                    // }
                    // let imageData = taskData.imageData;
                    // if (!imageData) {
                    //   layer.msg("请上传图片");
                    //   return false;
                    // }
                    let linkUrl = taskData.linkUrl;
                    // if (!linkUrl) {
                    //     layer.msg("请输入链接地址");
                    //   return false;
                    // }
                    // let index = taskData.话题顺序;
                    // if (!index) {
                    //   layer.msg("请输入话题顺序");
                    //   return false;
                    // }
                    // 高德点赞确保参数正确
                } else if (taskName === "提交个人主页操作数据") {  
                    let myName = taskData.myName;
                    let PersonalizedSignature = taskData.PersonalizedSignature;
                    let headImageData = taskData.headImageData;

                }else if (taskName === "获取链接") {  

                }           