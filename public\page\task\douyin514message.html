<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui优化版</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .image-preview {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                
                <fieldset>
                    <legend>【发消息】功能</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">私信用户</label>
                        <div class="layui-input-block">
                            <textarea id="messageUser" name="messageUser" placeholder="用户名称，多个用|分割" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">私信内容</label>
                        <div class="layui-input-block">
                            <textarea id="message" name="message" placeholder="私信内容，多个用|分割" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    <button class="layui-btn layui-btn-sm" value="私信" lay-submit="" lay-filter="tijiao">执行任务</button>
                    <button class="layui-btn layui-btn-sm" lay-submit="" lay-filter="stopTask">停止任务</button>
                </fieldset>
            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });

        // 任务执行按钮
        document.querySelector(".layui-btn[lay-filter='tijiao']").addEventListener("click", function (event) {
            event.preventDefault();

            const messageUser = document.getElementById("messageUser").value.trim();
            const message = document.getElementById("message").value.trim();

            if (!messageUser || !message) {
                layui.layer.msg('请填写完整的用户和消息内容');
                return;
            }

            // 获取选中的设备
            const selectedDevices = [];
            document.querySelectorAll('#groups1 input[type="checkbox"]:checked').forEach(checkbox => {
                if (checkbox.value && !checkbox.hasAttribute('lay-filter')) {
                    selectedDevices.push(checkbox.value);
                }
            });

            if (selectedDevices.length === 0) {
                layui.layer.msg('请至少选择一个设备');
                return;
            }

            // 分割用户和消息
            const users = messageUser.split('|').filter(u => u.trim());
            const messages = message.split('|').filter(m => m.trim());

            if (users.length !== messages.length) {
                layui.layer.msg('用户和消息数量不匹配');
                return;
            }

            // 将任务分配给设备
            const tasks = [];
            for (let i = 0; i < users.length; i++) {
                const deviceIndex = i % selectedDevices.length;
                tasks.push({
                    device: selectedDevices[deviceIndex],
                    user: users[i].trim(),
                    message: messages[i].trim()
                });
            }

            // 发送任务到后端
            axios.post('/wsRouter/sendMessage', {
                tasks: tasks
            }).then(response => {
                layui.layer.msg('任务提交成功');
            }).catch(error => {
                layui.layer.msg('提交失败: ' + error.message);
            });
        });

        // 停止任务按钮
        document.querySelector(".layui-btn[lay-filter='stopTask']").addEventListener("click", function () {
            layui.layer.confirm('确定要停止当前任务吗？', { icon: 3, title: '提示' }, function (index) {
                axios.post('/wsRouter/stopMessage').then(response => {
                    layui.layer.msg('任务已停止', { icon: 5 });
                }).catch(error => {
                    layui.layer.msg('停止失败: ' + error.message);
                });
                layer.close(index);
            });
        });
    </script>
</body>

</html>
