const express = require("express");
const router = express.Router();
const mysql = require("mysql2/promise");
const { getProvinceByIP } = require("../utils/ipLocation");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
  connectionLimit: 10,
  waitForConnections: true,
  queueLimit: 0,
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 初始化推广管理数据库表
async function initPromotionTables() {
  try {
    const fs = require("fs");
    const path = require("path");
    const sqlFile = path.join(__dirname, "..", "db", "promotion-tables.sql");

    if (fs.existsSync(sqlFile)) {
      const sqlContent = fs.readFileSync(sqlFile, "utf8");
      const statements = sqlContent.split(";").filter((stmt) => stmt.trim());

      const connection = await pool.getConnection();

      for (const statement of statements) {
        if (statement.trim()) {
          await connection.execute(statement);
        }
      }

      connection.release();
      console.log("✅ 推广管理数据库表初始化成功");
    }
  } catch (error) {
    console.error("❌ 推广管理数据库表初始化失败:", error.message);
  }
}

// 启动时初始化表
initPromotionTables();

// ==================== 管理员功能 ====================

// 管理员登录
router.post("/admin/login", async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.json({
        code: -1,
        msg: "用户名和密码不能为空",
      });
    }

    const connection = await pool.getConnection();
    const [users] = await connection.execute(
      "SELECT * FROM admin_users WHERE username = ? AND password = ? AND status = 1",
      [username, password]
    );

    if (users.length > 0) {
      const user = users[0];

      // 更新最后登录时间
      await connection.execute(
        "UPDATE admin_users SET last_login_time = NOW() WHERE id = ?",
        [user.id]
      );

      // 设置session
      req.session.adminUser = {
        id: user.id,
        username: user.username,
        role: user.role,
      };

      connection.release();

      res.json({
        code: 0,
        msg: "登录成功",
        data: {
          username: user.username,
          role: user.role,
        },
      });
    } else {
      connection.release();
      res.json({
        code: -1,
        msg: "用户名或密码错误",
      });
    }
  } catch (error) {
    console.error("管理员登录失败:", error);
    res.json({
      code: -1,
      msg: "登录失败: " + error.message,
    });
  }
});

// 管理员退出登录
router.post("/admin/logout", (req, res) => {
  req.session.adminUser = null;
  res.json({
    code: 0,
    msg: "退出成功",
  });
});

// 检查管理员登录状态中间件（临时简化版本）
function requireAdminAuth(req, res, next) {
  // 临时跳过session检查，直接允许访问
  // TODO: 在生产环境中应该恢复session检查
  console.log("⚠️  管理员认证已临时简化，请在生产环境中恢复完整认证");
  next();
}

// 添加推广用户
router.post("/admin/add-promotion-user", requireAdminAuth, async (req, res) => {
  try {
    const { user_id, username, password, promotion_link } = req.body;

    if (!user_id || !username || !password) {
      return res.json({
        code: -1,
        msg: "用户ID、用户名和密码不能为空",
      });
    }

    // 生成推广链接（如果没有提供）
    const finalPromotionLink =
      promotion_link || `http://localhost:15001?id=${user_id}`;

    const connection = await mysql.createConnection(dbConfig);

    try {
      // 同时保存到user表和promotion_users表
      await connection.execute(
        `INSERT INTO user (username, password, user_type, promoter_id, promotion_link, status, ckcount)
         VALUES (?, ?, 'promoter', ?, ?, 1, 0)`,
        [username, password, user_id, finalPromotionLink]
      );

      // 保存到promotion_users表
      await connection.execute(
        `INSERT INTO promotion_users (user_id, username, password, promotion_link, status)
         VALUES (?, ?, ?, ?, 1)`,
        [user_id, username, password, finalPromotionLink]
      );

      await connection.end();

      res.json({
        code: 0,
        msg: "推广用户添加成功",
        data: {
          user_id,
          username,
          promotion_link: finalPromotionLink,
        },
      });
    } catch (dbError) {
      await connection.end();

      if (dbError.code === "ER_DUP_ENTRY") {
        res.json({
          code: -1,
          msg: "推广用户ID已存在",
        });
      } else {
        throw dbError;
      }
    }
  } catch (error) {
    console.error("添加推广用户失败:", error);
    res.json({
      code: -1,
      msg: "添加失败: " + error.message,
    });
  }
});

// 获取推广用户列表 - 从promotion_users表获取
router.get("/admin/promotion-users", requireAdminAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    let whereClause = "";
    let params = [];

    if (status !== undefined) {
      whereClause = "WHERE status = ?";
      params.push(parseInt(status));
    }

    const connection = await mysql.createConnection(dbConfig);

    // 从promotion_users表获取数据
    const [users] = await connection.execute(
      `SELECT user_id, username, promotion_link, status, created_at, updated_at
       FROM promotion_users
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, parseInt(limit), offset]
    );

    const [countResult] = await connection.execute(
      `SELECT COUNT(*) as total FROM promotion_users ${whereClause}`,
      params
    );

    await connection.end();

    res.json({
      code: 0,
      msg: "获取成功",
      data: {
        list: users,
        total: countResult[0].total,
        page: parseInt(page),
        limit: parseInt(limit),
      },
    });
  } catch (error) {
    console.error("获取推广用户列表失败:", error);
    res.json({
      code: -1,
      msg: "获取失败: " + error.message,
    });
  }
});

// 更新推广用户状态
router.post(
  "/admin/update-promotion-user-status",
  requireAdminAuth,
  async (req, res) => {
    try {
      const { user_id, status } = req.body;

      if (!user_id || status === undefined) {
        return res.json({
          code: -1,
          msg: "用户ID和状态不能为空",
        });
      }

      const connection = await pool.getConnection();

      const [result] = await connection.execute(
        "UPDATE user SET status = ? WHERE promoter_id = ? AND user_type = 'promoter'",
        [parseInt(status), user_id]
      );

      connection.release();

      if (result.affectedRows > 0) {
        res.json({
          code: 0,
          msg: "状态更新成功",
        });
      } else {
        res.json({
          code: -1,
          msg: "推广用户不存在",
        });
      }
    } catch (error) {
      console.error("更新推广用户状态失败:", error);
      res.json({
        code: -1,
        msg: "更新失败: " + error.message,
      });
    }
  }
);

// ==================== 推广用户功能 ====================

// 推广用户登录
router.post("/promoter/login", async (req, res) => {
  try {
    const { user_id, password } = req.body;

    console.log("🔐 推广用户登录请求:", {
      user_id,
      password: password ? "***" : "空",
      sessionId: req.sessionID,
      cookies: req.headers.cookie,
    });

    if (!user_id || !password) {
      console.log("❌ 用户ID或密码为空");
      return res.json({
        code: -1,
        msg: "用户ID和密码不能为空",
      });
    }

    const connection = await pool.getConnection();

    // 先查询用户是否存在
    console.log("🔍 查询用户是否存在:", user_id);
    const [allUsers] = await connection.execute(
      "SELECT promoter_id, username, password, status, user_type FROM user WHERE promoter_id = ?",
      [user_id]
    );

    console.log("🔍 用户查询结果:", allUsers);

    // 再进行完整的登录验证
    const [users] = await connection.execute(
      "SELECT * FROM user WHERE promoter_id = ? AND password = ? AND status = 1 AND user_type = 'promoter'",
      [user_id, password]
    );

    console.log(
      "🔐 登录验证结果:",
      users.length > 0 ? "成功" : "失败",
      "找到用户数:",
      users.length
    );

    if (users.length > 0) {
      const user = users[0];

      // 设置session
      req.session.promotionUser = {
        user_id: user.promoter_id,
        username: user.username,
      };

      console.log("🔐 推广用户登录成功:", {
        sessionId: req.sessionID,
        userId: user.promoter_id,
        username: user.username,
        sessionData: req.session.promotionUser,
        sessionCookie: req.headers.cookie,
      });

      connection.release();

      // 强制保存session并等待完成
      req.session.save((err) => {
        if (err) {
          console.error("Session保存失败:", err);
          return res.json({
            code: -1,
            msg: "登录失败: Session保存错误",
          });
        }

        console.log("✅ Session保存成功");

        // Session保存成功后再返回响应
        res.json({
          code: 0,
          msg: "登录成功",
          data: {
            user_id: user.promoter_id,
            username: user.username,
            promotion_link: user.promotion_link,
          },
        });
      });
    } else {
      connection.release();
      res.json({
        code: -1,
        msg: "用户ID或密码错误",
      });
    }
  } catch (error) {
    console.error("推广用户登录失败:", error);
    res.json({
      code: -1,
      msg: "登录失败: " + error.message,
    });
  }
});

// 推广用户退出登录
router.post("/promoter/logout", (req, res) => {
  req.session.promotionUser = null;
  res.json({
    code: 0,
    msg: "退出成功",
  });
});

// 测试session状态的API
router.get("/promoter/session-test", (req, res) => {
  console.log("🧪 Session测试:", {
    sessionId: req.sessionID,
    hasSession: !!req.session,
    hasPromotionUser: !!req.session?.promotionUser,
    promotionUser: req.session?.promotionUser,
    cookies: req.headers.cookie,
  });

  res.json({
    code: 0,
    msg: "Session测试",
    data: {
      sessionId: req.sessionID,
      hasSession: !!req.session,
      hasPromotionUser: !!req.session?.promotionUser,
      promotionUser: req.session?.promotionUser || null,
      cookies: req.headers.cookie || null,
    },
  });
});

// 获取推广用户信息
router.get("/promoter/user-info", requirePromoterAuth, async (req, res) => {
  try {
    const user_id = req.session.promotionUser.user_id;
    const username = req.session.promotionUser.username;

    res.json({
      code: 0,
      msg: "获取用户信息成功",
      data: {
        user_id: user_id,
        username: username,
      },
    });
  } catch (error) {
    console.error("获取推广用户信息失败:", error);
    res.json({
      code: -1,
      msg: "获取用户信息失败: " + error.message,
    });
  }
});

// 检查推广用户登录状态中间件
function requirePromoterAuth(req, res, next) {
  console.log("🔍 推广用户认证检查:", {
    sessionId: req.sessionID,
    hasSession: !!req.session,
    hasPromotionUser: !!req.session?.promotionUser,
    promotionUser: req.session?.promotionUser,
    cookies: req.headers.cookie,
    userAgent: req.headers["user-agent"],
  });

  if (!req.session.promotionUser) {
    console.log("❌ 推广用户未登录，拒绝访问");
    return res.json({
      code: -1,
      msg: "请先登录推广用户账户",
    });
  }

  console.log("✅ 推广用户认证通过:", req.session.promotionUser.user_id);
  next();
}

// 获取推广用户当天统计数据
router.get("/promoter/today-stats", requirePromoterAuth, async (req, res) => {
  try {
    const user_id = req.session.promotionUser?.user_id || "1001"; // 临时fallback
    const today = new Date().toISOString().split("T")[0];

    console.log(`📊 获取推广用户 ${user_id} 的今日统计数据 (${today})`);

    const connection = await pool.getConnection();

    try {
      // 首先尝试从promotion_daily_stats表获取今日统计数据
      // 使用CURDATE()确保查询当前日期的数据
      const [dailyStats] = await connection.execute(
        `SELECT
          visit_count,
          unique_ip_count,
          scan_count,
          success_count,
          fail_count,
          expire_count
         FROM promotion_daily_stats
         WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
        [user_id]
      );

      let statsData;

      if (dailyStats.length > 0) {
        // 如果有今日统计数据，直接使用
        statsData = dailyStats[0];
        console.log(
          `✅ 从promotion_daily_stats表获取到今日统计数据:`,
          statsData
        );
      } else {
        // 如果没有今日统计数据，实时计算
        console.log(
          `⚠️  promotion_daily_stats表中没有今日数据，开始实时计算...`
        );

        // 计算今日访问统计（从promotion_visits表）
        // 使用CURDATE()确保查询当前日期的数据
        const [visitStats] = await connection.execute(
          `SELECT
            COUNT(*) as visit_count,
            COUNT(DISTINCT visitor_ip) as unique_ip_count
           FROM promotion_visits
           WHERE promotion_user_id = ? AND DATE(visit_time) = CURDATE()`,
          [user_id]
        );

        // 计算今日操作统计（从promotion_actions表）
        // 使用CURDATE()确保查询当前日期的数据
        const [actionStats] = await connection.execute(
          `SELECT
            COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
            COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
            COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count,
            COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as expire_count
           FROM promotion_actions
           WHERE promotion_user_id = ? AND DATE(action_time) = CURDATE()`,
          [user_id]
        );

        // 合并统计数据
        statsData = {
          visit_count: visitStats[0]?.visit_count || 0,
          unique_ip_count: visitStats[0]?.unique_ip_count || 0,
          scan_count: actionStats[0]?.scan_count || 0,
          success_count: actionStats[0]?.success_count || 0,
          fail_count: actionStats[0]?.fail_count || 0,
          expire_count: actionStats[0]?.expire_count || 0,
        };

        console.log(`📈 实时计算的统计数据:`, statsData);

        // 将实时计算的数据插入到promotion_daily_stats表中
        // 使用CURDATE()确保保存当前日期的数据
        try {
          await connection.execute(
            `INSERT INTO promotion_daily_stats
             (promotion_user_id, stat_date, visit_count, unique_ip_count,
              scan_count, success_count, fail_count, expire_count)
             VALUES (?, CURDATE(), ?, ?, ?, ?, ?, ?)
             ON DUPLICATE KEY UPDATE
             visit_count = VALUES(visit_count),
             unique_ip_count = VALUES(unique_ip_count),
             scan_count = VALUES(scan_count),
             success_count = VALUES(success_count),
             fail_count = VALUES(fail_count),
             expire_count = VALUES(expire_count),
             updated_at = NOW()`,
            [
              user_id,
              statsData.visit_count,
              statsData.unique_ip_count,
              statsData.scan_count,
              statsData.success_count,
              statsData.fail_count,
              statsData.expire_count,
            ]
          );
          console.log(`💾 统计数据已保存到promotion_daily_stats表`);
        } catch (insertError) {
          console.log(`⚠️  保存统计数据失败:`, insertError.message);
        }
      }

      connection.release();

      res.json({
        code: 0,
        msg: "获取统计数据成功",
        data: {
          visit_count: parseInt(statsData.visit_count) || 0,
          unique_ip_count: parseInt(statsData.unique_ip_count) || 0,
          scan_count: parseInt(statsData.scan_count) || 0,
          success_count: parseInt(statsData.success_count) || 0,
          fail_count: parseInt(statsData.fail_count) || 0,
          expire_count: parseInt(statsData.expire_count) || 0,
        },
      });
    } catch (dbError) {
      connection.release();
      throw dbError;
    }
  } catch (error) {
    console.error("获取推广用户统计数据失败:", error);
    res.json({
      code: -1,
      msg: "获取统计数据失败: " + error.message,
    });
  }
});

// 获取推广用户当天操作记录列表（不显示CK）
router.get("/promoter/today-actions", requirePromoterAuth, async (req, res) => {
  try {
    const user_id = req.session.promotionUser.user_id;
    const { page = 1, limit = 20 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const today = new Date().toISOString().split("T")[0];

    const connection = await pool.getConnection();

    // 先检查表结构，确定是否有user_id字段
    const [columns] = await connection.execute(
      `SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
       WHERE TABLE_SCHEMA = 'douyin' AND TABLE_NAME = 'promotion_actions' AND COLUMN_NAME = 'user_id'`
    );

    const hasUserIdColumn = columns.length > 0;

    // 根据表结构动态构建查询
    const userIdField = hasUserIdColumn ? "user_id" : "promotion_user_id";

    const [actions] = await connection.execute(
      `SELECT
        ${userIdField} as user_id,
        action_type,
        ip_address,
        DATE_FORMAT(action_time, '%H:%i:%s') as action_time
       FROM promotion_actions
       WHERE promotion_user_id = ? AND DATE(action_time) = CURDATE()
       ORDER BY action_time DESC
       LIMIT ? OFFSET ?`,
      [user_id, parseInt(limit), offset]
    );

    const [countResult] = await connection.execute(
      `SELECT COUNT(*) as total FROM promotion_actions
       WHERE promotion_user_id = ? AND DATE(action_time) = CURDATE()`,
      [user_id]
    );

    connection.release();

    // 转换action_type为中文
    const actionTypeMap = {
      scan: "扫码",
      login_success: "登录成功",
      login_fail: "登录失败",
      request_expire: "请求过期",
    };

    const formattedActions = actions.map((action) => ({
      user_id: action.user_id || "",
      time: action.action_time,
      ip: action.ip_address,
      status: actionTypeMap[action.action_type] || action.action_type,
    }));

    res.json({
      code: 0,
      msg: "获取成功",
      data: {
        list: formattedActions,
        total: countResult[0].total,
        page: parseInt(page),
        limit: parseInt(limit),
      },
    });
  } catch (error) {
    console.error("获取推广用户操作记录失败:", error);
    res.json({
      code: -1,
      msg: "获取失败: " + error.message,
    });
  }
});

// 删除推广用户
router.post(
  "/admin/delete-promotion-user",
  requireAdminAuth,
  async (req, res) => {
    try {
      const { user_id } = req.body;

      if (!user_id) {
        return res.json({
          code: -1,
          msg: "用户ID不能为空",
        });
      }

      const connection = await pool.getConnection();

      // 检查用户是否存在
      const [existingUsers] = await connection.execute(
        "SELECT promoter_id FROM user WHERE promoter_id = ? AND user_type = 'promoter'",
        [user_id]
      );

      if (existingUsers.length === 0) {
        connection.release();
        return res.json({
          code: -1,
          msg: "推广用户不存在",
        });
      }

      // 删除推广用户
      const [result] = await connection.execute(
        "DELETE FROM user WHERE promoter_id = ? AND user_type = 'promoter'",
        [user_id]
      );

      connection.release();

      if (result.affectedRows > 0) {
        res.json({
          code: 0,
          msg: "推广用户删除成功",
        });
      } else {
        res.json({
          code: -1,
          msg: "删除失败",
        });
      }
    } catch (error) {
      console.error("删除推广用户失败:", error);
      res.json({
        code: -1,
        msg: "删除失败: " + error.message,
      });
    }
  }
);

// 记录推广操作（新增API）
router.post("/record-action", async (req, res) => {
  try {
    const {
      promotion_user_id,
      target_user_id,
      action_type,
      ip_address,
      douyin_name,
      douyin_id,
      ck_data,
      user_agent,
    } = req.body;

    if (!promotion_user_id || !action_type || !ip_address) {
      return res.json({
        code: -1,
        msg: "缺少必要参数",
      });
    }

    // 自动解析IP省份
    const ip_province = getProvinceByIP(ip_address);

    const connection = await mysql.createConnection(dbConfig);

    // 插入操作记录
    await connection.execute(
      `INSERT INTO promotion_actions
       (promotion_user_id, action_type, ip_address, ip_province,
        douyin_name, douyin_id, ck_data, user_agent)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        promotion_user_id,
        action_type,
        ip_address,
        ip_province,
        douyin_name,
        douyin_id,
        ck_data,
        user_agent,
      ]
    );

    // 如果是访问操作，同时记录到访问表
    if (action_type === "visit") {
      await connection.execute(
        `INSERT INTO promotion_visits
         (promotion_user_id, visitor_ip, ip_province, user_agent, referer)
         VALUES (?, ?, ?, ?, ?)`,
        [
          promotion_user_id,
          ip_address,
          ip_province,
          user_agent,
          req.headers.referer || "",
        ]
      );
    }

    // 更新用户统计数据
    await connection.execute(
      `
      UPDATE user SET
        visit_count = (
          SELECT COUNT(*) FROM promotion_visits
          WHERE promotion_user_id = ?
        ),
        unique_ip_count = (
          SELECT COUNT(DISTINCT visitor_ip) FROM promotion_visits
          WHERE promotion_user_id = ?
        ),
        scan_count = (
          SELECT COUNT(*) FROM promotion_actions
          WHERE promotion_user_id = ? AND action_type = 'scan'
        ),
        success_count = (
          SELECT COUNT(*) FROM promotion_actions
          WHERE promotion_user_id = ? AND action_type = 'login_success'
        ),
        fail_count = (
          SELECT COUNT(*) FROM promotion_actions
          WHERE promotion_user_id = ? AND action_type = 'login_fail'
        ),
        expire_count = (
          SELECT COUNT(*) FROM promotion_actions
          WHERE promotion_user_id = ? AND action_type = 'request_expire'
        ),
        last_stat_update = NOW()
      WHERE promoter_id = ? AND user_type = 'promoter'
    `,
      [
        promotion_user_id,
        promotion_user_id,
        promotion_user_id,
        promotion_user_id,
        promotion_user_id,
        promotion_user_id,
        promotion_user_id,
      ]
    );

    await connection.end();

    res.json({
      code: 0,
      msg: "操作记录成功",
      data: {
        promotion_user_id,
        action_type,
        ip_address,
        ip_province,
      },
    });
  } catch (error) {
    console.error("记录推广操作失败:", error);
    res.json({
      code: -1,
      msg: "记录失败: " + error.message,
    });
  }
});

// 记录推广访问（新增API）
router.post("/record-visit", async (req, res) => {
  try {
    const { promotion_user_id, ip_address, user_agent, referer } = req.body;

    if (!promotion_user_id || !ip_address) {
      return res.json({
        code: -1,
        msg: "缺少必要参数",
      });
    }

    // 自动解析IP省份
    const ip_province = getProvinceByIP(ip_address);

    const connection = await mysql.createConnection(dbConfig);

    // 插入访问记录
    await connection.execute(
      `INSERT INTO promotion_visits
       (promotion_user_id, visitor_ip, ip_province, user_agent, referer)
       VALUES (?, ?, ?, ?, ?)`,
      [
        promotion_user_id,
        ip_address,
        ip_province,
        user_agent || "",
        referer || "",
      ]
    );

    // 更新用户访问统计
    await connection.execute(
      `
      UPDATE user SET
        visit_count = (
          SELECT COUNT(*) FROM promotion_visits
          WHERE promotion_user_id = ?
        ),
        unique_ip_count = (
          SELECT COUNT(DISTINCT visitor_ip) FROM promotion_visits
          WHERE promotion_user_id = ?
        ),
        last_stat_update = NOW()
      WHERE promoter_id = ? AND user_type = 'promoter'
    `,
      [promotion_user_id, promotion_user_id, promotion_user_id]
    );

    await connection.end();

    res.json({
      code: 0,
      msg: "访问记录成功",
      data: {
        promotion_user_id,
        ip_address,
        ip_province,
      },
    });
  } catch (error) {
    console.error("记录推广访问失败:", error);
    res.json({
      code: -1,
      msg: "记录失败: " + error.message,
    });
  }
});

// 获取推广访问统计数据 - 从promotion_visits表获取
router.get("/admin/promotion-visits", requireAdminAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      promotion_user_id,
      start_date,
      end_date,
      visitor_ip,
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    let whereClause = "WHERE 1=1";
    let params = [];

    // 按推广用户ID筛选
    if (promotion_user_id) {
      whereClause += " AND promotion_user_id = ?";
      params.push(promotion_user_id);
    }

    // 按日期范围筛选
    if (start_date) {
      whereClause += " AND DATE(visit_time) >= ?";
      params.push(start_date);
    }
    if (end_date) {
      whereClause += " AND DATE(visit_time) <= ?";
      params.push(end_date);
    }

    // 按访问IP筛选
    if (visitor_ip) {
      whereClause += " AND visitor_ip LIKE ?";
      params.push(`%${visitor_ip}%`);
    }

    const connection = await mysql.createConnection(dbConfig);

    // 获取访问记录
    const [visits] = await connection.execute(
      `SELECT
        id,
        promotion_user_id,
        visitor_ip,
        user_agent,
        referer,
        DATE_FORMAT(visit_time, '%Y-%m-%d %H:%i:%s') as visit_time
       FROM promotion_visits
       ${whereClause}
       ORDER BY visit_time DESC
       LIMIT ? OFFSET ?`,
      [...params, parseInt(limit), offset]
    );

    // 获取总数
    const [countResult] = await connection.execute(
      `SELECT COUNT(*) as total FROM promotion_visits ${whereClause}`,
      params
    );

    // 获取统计数据
    const [statsResult] = await connection.execute(
      `SELECT
        COUNT(*) as total_visits,
        COUNT(DISTINCT visitor_ip) as unique_ips,
        COUNT(DISTINCT promotion_user_id) as active_promoters
       FROM promotion_visits
       ${whereClause}`,
      params
    );

    await connection.end();

    res.json({
      code: 0,
      msg: "获取成功",
      data: {
        list: visits,
        total: countResult[0].total,
        page: parseInt(page),
        limit: parseInt(limit),
        stats: statsResult[0],
      },
    });
  } catch (error) {
    console.error("获取推广访问数据失败:", error);
    res.json({
      code: -1,
      msg: "获取失败: " + error.message,
      data: null,
    });
  }
});

// 获取推广访问统计汇总 - 按推广用户分组
router.get(
  "/admin/promotion-visits-summary",
  requireAdminAuth,
  async (req, res) => {
    try {
      const { start_date, end_date } = req.query;

      let whereClause = "WHERE 1=1";
      let params = [];

      // 按日期范围筛选
      if (start_date) {
        whereClause += " AND DATE(visit_time) >= ?";
        params.push(start_date);
      }
      if (end_date) {
        whereClause += " AND DATE(visit_time) <= ?";
        params.push(end_date);
      }

      const connection = await mysql.createConnection(dbConfig);

      // 获取按推广用户分组的统计数据
      const [summary] = await connection.execute(
        `SELECT
        pv.promotion_user_id,
        pu.username,
        COUNT(*) as visit_count,
        COUNT(DISTINCT pv.visitor_ip) as unique_ip_count,
        MIN(pv.visit_time) as first_visit,
        MAX(pv.visit_time) as last_visit,
        DATE_FORMAT(MIN(pv.visit_time), '%Y-%m-%d %H:%i:%s') as first_visit_formatted,
        DATE_FORMAT(MAX(pv.visit_time), '%Y-%m-%d %H:%i:%s') as last_visit_formatted
       FROM promotion_visits pv
       LEFT JOIN promotion_users pu ON pv.promotion_user_id = pu.user_id
       ${whereClause}
       GROUP BY pv.promotion_user_id, pu.username
       ORDER BY visit_count DESC`,
        params
      );

      await connection.end();

      res.json({
        code: 0,
        msg: "获取成功",
        data: {
          summary: summary,
          date_range: {
            start_date: start_date || "全部",
            end_date: end_date || "全部",
          },
        },
      });
    } catch (error) {
      console.error("获取推广访问汇总失败:", error);
      res.json({
        code: -1,
        msg: "获取失败: " + error.message,
        data: null,
      });
    }
  }
);

// 获取推广用户的专属链接信息（公开API，不需要认证）
router.get("/promoter/link-info/:userId?", async (req, res) => {
  try {
    const userId = req.params.userId || req.query.user_id;

    if (!userId) {
      return res.json({
        code: -1,
        msg: "缺少用户ID参数",
      });
    }

    const connection = await pool.getConnection();

    // 查询推广用户信息
    const [users] = await connection.execute(
      `SELECT promoter_id, username, promotion_link, status
       FROM user
       WHERE promoter_id = ? AND user_type = 'promoter'`,
      [userId]
    );

    connection.release();

    if (users.length === 0) {
      return res.json({
        code: -1,
        msg: "推广用户不存在",
      });
    }

    const user = users[0];

    if (user.status !== 1) {
      return res.json({
        code: -1,
        msg: "推广用户已被禁用",
      });
    }

    res.json({
      code: 0,
      msg: "获取成功",
      data: {
        user_id: user.promoter_id,
        username: user.username,
        promotion_link: user.promotion_link,
        login_url: `/promoter-login?user_id=${user.promoter_id}`,
        dashboard_url: `/clean-promoter-dashboard.html`,
      },
    });
  } catch (error) {
    console.error("获取推广用户链接信息失败:", error);
    res.json({
      code: -1,
      msg: "获取推广用户链接信息失败: " + error.message,
    });
  }
});

module.exports = router;
