<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化调试登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            width: 100%;
            padding: 12px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        .warning {
            color: #ffc107;
        }
        
        .step {
            background: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 简化调试登录</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            无任何外部依赖，纯净JavaScript调试
        </p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="user_id">用户ID:</label>
                <input type="text" id="user_id" name="user_id" value="1001" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="123456" required>
            </div>
            
            <button type="submit" id="loginBtn">登录</button>
        </form>
        
        <div class="log" id="log">等待操作...\n</div>
        
        <div style="margin-top: 20px;">
            <div class="step">
                <strong>调试步骤：</strong><br>
                1. 点击登录按钮<br>
                2. 查看详细日志<br>
                3. 确认API响应<br>
                4. 验证跳转执行
            </div>
        </div>
    </div>

    <script>
        // 完全不依赖任何外部库的纯净JavaScript
        
        const log = document.getElementById('log');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            
            log.textContent += logMessage;
            log.scrollTop = log.scrollHeight;
            
            // 同时输出到控制台
            console.log(`${prefix} ${message}`);
        }
        
        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('简化调试登录页面加载完成', 'success');
            addLog(`当前URL: ${window.location.href}`, 'info');
            addLog(`当前cookies: ${document.cookie || '无'}`, 'info');
            
            // 检查URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const userId = urlParams.get('user_id');
            if (userId) {
                document.getElementById('user_id').value = userId;
                addLog(`从URL参数获取用户ID: ${userId}`, 'info');
            }
        });
        
        // 监听表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            addLog('=== 开始登录流程 ===', 'info');
            
            const formData = new FormData(this);
            const loginData = {
                user_id: formData.get('user_id').trim(),
                password: formData.get('password').trim()
            };
            
            addLog(`登录数据: 用户ID=${loginData.user_id}`, 'info');
            
            if (!loginData.user_id || !loginData.password) {
                addLog('用户ID或密码为空', 'error');
                return;
            }
            
            // 禁用按钮
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                addLog('步骤1: 发送登录请求...', 'info');
                addLog(`请求URL: /api/promotion/promoter/login`, 'info');
                addLog(`请求数据: ${JSON.stringify(loginData)}`, 'info');
                
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                
                addLog(`步骤2: 收到HTTP响应`, 'info');
                addLog(`HTTP状态码: ${response.status}`, 'info');
                addLog(`HTTP状态文本: ${response.statusText}`, 'info');
                addLog(`响应头Content-Type: ${response.headers.get('content-type')}`, 'info');
                
                const result = await response.json();
                
                addLog(`步骤3: 解析JSON响应`, 'info');
                addLog(`响应数据: ${JSON.stringify(result, null, 2)}`, 'info');
                addLog(`更新后的cookies: ${document.cookie}`, 'info');
                
                if (result.code === 0) {
                    addLog('步骤4: 登录成功！', 'success');
                    addLog(`用户信息: ${result.data.username} (ID: ${result.data.user_id})`, 'success');
                    
                    addLog('步骤5: 准备跳转...', 'info');
                    addLog('目标URL: /clean-promoter-dashboard.html', 'info');
                    
                    // 显示倒计时
                    let countdown = 3;
                    const countdownInterval = setInterval(() => {
                        addLog(`${countdown}秒后跳转...`, 'warning');
                        countdown--;
                        
                        if (countdown < 0) {
                            clearInterval(countdownInterval);
                            addLog('步骤6: 执行跳转', 'info');
                            addLog('执行: window.location.href = "/clean-promoter-dashboard.html"', 'info');
                            
                            // 实际跳转
                            window.location.href = '/clean-promoter-dashboard.html';
                        }
                    }, 1000);
                    
                } else {
                    addLog(`步骤4: 登录失败`, 'error');
                    addLog(`失败原因: ${result.msg}`, 'error');
                    addLog(`错误代码: ${result.code}`, 'error');
                    
                    // 分析失败原因
                    if (result.msg && result.msg.includes('用户ID')) {
                        addLog('分析: 用户ID相关错误', 'warning');
                    } else if (result.msg && result.msg.includes('密码')) {
                        addLog('分析: 密码相关错误', 'warning');
                    } else if (result.msg && result.msg.includes('数据库')) {
                        addLog('分析: 数据库连接错误', 'warning');
                    } else {
                        addLog('分析: 其他服务器错误', 'warning');
                    }
                }
                
            } catch (error) {
                addLog(`步骤X: 请求失败`, 'error');
                addLog(`错误类型: ${error.name}`, 'error');
                addLog(`错误消息: ${error.message}`, 'error');
                addLog(`错误堆栈: ${error.stack}`, 'error');
                
                // 分析网络错误
                if (error.message.includes('fetch')) {
                    addLog('分析: 网络请求错误，可能是服务器未启动', 'warning');
                } else if (error.message.includes('JSON')) {
                    addLog('分析: JSON解析错误，服务器可能返回了非JSON数据', 'warning');
                } else {
                    addLog('分析: 其他JavaScript执行错误', 'warning');
                }
                
            } finally {
                // 恢复按钮
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
                addLog('=== 登录流程结束 ===', 'info');
            }
        });
        
        // 监听页面卸载（检测跳转）
        window.addEventListener('beforeunload', function(e) {
            addLog('页面即将卸载/跳转', 'warning');
        });
        
        // 监听所有点击事件
        document.addEventListener('click', function(e) {
            if (e.target.tagName !== 'INPUT') {
                addLog(`点击事件: ${e.target.tagName} - ${e.target.textContent || e.target.value}`, 'info');
            }
        });
    </script>
</body>
</html>
