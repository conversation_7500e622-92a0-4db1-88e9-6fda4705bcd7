<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <!-- <fieldset>
                <legend>查询设备执行结果</legend>
                 <div class="layui-form-item">
                    <label class="layui-form-label">选择日期</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="datePicker" placeholder="请选择日期">
                    </div>
                    <button class="layui-btn layui-btn-normal" onclick="fetchData()">查询</button>
                </div> 
            </fieldset> -->

            <div id="tableContainer">
                <table class="layui-table" id="resultTable"></table>
                <div id="pagination"></div>
            </div>

            <fieldset>
                <legend>统计数据</legend>
                <p>总正确数: <span id="totalSuccess">0</span></p>
                <p>总错误数: <span id="totalFailure">0</span></p>
                <p>总执行数: <span id="totalCount">0</span></p>
                <p>正确率: <span id="successRate">0%</span></p>
            </fieldset>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script>
        layui.use(['laydate', 'table', 'laypage'], function () {
            var laydate = layui.laydate;
            var table = layui.table;
            var laypage = layui.laypage;

            // 获取当前日期（格式：YYYY-MM-DD）
            function getToday() {
                let now = new Date();
                let year = now.getFullYear();
                let month = String(now.getMonth() + 1).padStart(2, '0');
                let day = String(now.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // 渲染日期选择器
            laydate.render({
                elem: '#datePicker',
                type: 'date',
                value: getToday(),
                done: function (value) {
                    fetchData(value);
                }
            });

            // 获取查询时间范围
            function getFullDateRange(date) {
                return {
                    startTime: `${date} 00:00:00`,
                    endTime: `${date} 23:59:59`
                };
            }

            // 查询数据
            function fetchData(date = getToday()) {
                axios.get(`http://114.55.151.143:3101/getExecutionSummary`)
                    .then(response => {
                        var data = response.data.data.map(row => ({
                            deviceId: row.deviceId,
                            totalSuccess: Number(row.successCount) || 0,
                            totalFailure: Number(row.failureCount) || 0,
                            date: row.created_at
                        }));

                        var totalSuccess = data.reduce((sum, row) => sum + row.totalSuccess, 0);
                        var totalFailure = data.reduce((sum, row) => sum + row.totalFailure, 0);
                        var totalCount = totalSuccess + totalFailure;
                        var successRate = totalCount ? ((totalSuccess / totalCount) * 100).toFixed(2) + '%' : '0%';

                        document.getElementById('totalSuccess').innerText = totalSuccess;
                        document.getElementById('totalFailure').innerText = totalFailure;
                        document.getElementById('totalCount').innerText = totalCount;
                        document.getElementById('successRate').innerText = successRate;

                        // 渲染表格
                        table.render({
                            elem: '#resultTable',
                            cols: [[
                                { field: 'deviceId', title: '设备ID' },
                                { field: 'totalSuccess', title: '成功次数' },
                                { field: 'totalFailure', title: '失败次数' },
                                { field: 'date', title: '执行时间' }
                            ]],
                            data: data.slice(0, 5),
                            limit: 5
                        });

                        // 渲染分页
                        laypage.render({
                            elem: 'pagination',
                            count: data.length,
                            limit: 5,
                            jump: function (obj, first) {
                                if (!first) {
                                    table.reload('resultTable', {
                                        data: data.slice((obj.curr - 1) * obj.limit, obj.curr * obj.limit)
                                    });
                                }
                            }
                        });
                    })
                    .catch(error => {
                        console.error('数据查询失败:', error);
                    });
            }

            // 默认加载当天数据
            fetchData();
        });
    </script>

</body>

</html>