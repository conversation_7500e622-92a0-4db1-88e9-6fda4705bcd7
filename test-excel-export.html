<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel导出功能测试</title>
    <!-- SheetJS库用于Excel导出 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .test-section h3 {
            color: #764ba2;
            margin-top: 0;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        th,
        td {
            border: 1px solid #dee2e6;
            padding: 12px 8px;
            text-align: left;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background: #f9f9f9;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background: #e9ecef;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>📊 Excel导出功能测试</h1>

        <div class="test-section">
            <h3>📋 测试数据预览</h3>
            <p>以下是模拟的推广操作记录数据，用于测试Excel导出功能：</p>

            <table id="testDataTable">
                <thead>
                    <tr>
                        <th>推广用户ID</th>
                        <th>用户ID</th>
                        <th>操作时间</th>
                        <th>IP地址</th>
                        <th>IP省份</th>
                        <th>操作状态</th>
                        <th>抖音名称</th>
                        <th>抖音号</th>
                        <th>CK数据长度</th>
                        <th>CK完整数据</th>
                    </tr>
                </thead>
                <tbody id="testDataBody">
                    <!-- 测试数据将在这里生成 -->
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🧪 测试操作</h3>
            <button class="btn" onclick="generateTestData()">🔄 生成测试数据</button>
            <button class="btn btn-success" onclick="exportTestDataToExcel()">📊 导出Excel</button>
            <button class="btn" onclick="clearTestData()">🗑️ 清除数据</button>
        </div>

        <div id="statusContainer" class="status" style="display: none;">
            <h4>测试状态</h4>
            <div id="statusContent"></div>
        </div>
    </div>

    <script>
        // 测试数据
        let testActionsData = [];

        // 生成测试数据
        function generateTestData() {
            console.log('🔄 生成测试数据...');

            testActionsData = [
                {
                    promotion_user_id: '1001',
                    user_id: 'user123',
                    time: '2024-01-20 10:30:15',
                    ip: '**************',
                    ip_province: '河南',
                    status: '登录成功',
                    douyin_name: '测试用户1',
                    douyin_id: 'dy123456',
                    ck: '[{"name":"sessionid","value":"abc123def456","domain":".douyin.com"}]'
                },
                {
                    promotion_user_id: '1002',
                    user_id: 'user456',
                    time: '2024-01-20 11:15:30',
                    ip: '*************',
                    ip_province: '广东',
                    status: '扫码',
                    douyin_name: '测试用户2',
                    douyin_id: 'dy789012',
                    ck: 'sessionid=xyz789; uid=user456; token=abc123'
                },
                {
                    promotion_user_id: '1003',
                    user_id: 'user789',
                    time: '2024-01-20 12:45:20',
                    ip: '***************',
                    ip_province: '北京',
                    status: '登录失败',
                    douyin_name: '测试用户3',
                    douyin_id: 'dy345678',
                    ck: ''
                },
                {
                    promotion_user_id: '1001',
                    user_id: 'user101',
                    time: '2024-01-20 14:20:45',
                    ip: '*******',
                    ip_province: '未知',
                    status: '请求过期',
                    douyin_name: '',
                    douyin_id: '',
                    ck: 'cookie_with_special_chars="value with spaces & symbols < > \' \" \\ / \n \r \t"'
                },
                {
                    promotion_user_id: '1004',
                    user_id: 'user202',
                    time: '2024-01-20 15:10:12',
                    ip: '*********',
                    ip_province: '浙江',
                    status: '登录成功',
                    douyin_name: '长用户名测试用户名称',
                    douyin_id: 'dy901234567890',
                    ck: '[{"name":"bd_ticket_guard_client_data","value":"eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxfQ%3D%3D","domain":".douyin.com"}]'
                }
            ];

            renderTestData();
            updateStatus('测试数据已生成，共 ' + testActionsData.length + ' 条记录', 'success');
        }

        // 渲染测试数据
        function renderTestData() {
            const tbody = document.getElementById('testDataBody');
            tbody.innerHTML = '';

            testActionsData.forEach(action => {
                // 处理CK数据
                let ckLength = 0;
                let ckPreview = '';

                if (action.ck && action.ck !== '-') {
                    ckLength = action.ck.length;
                    ckPreview = action.ck.length > 30 ?
                        action.ck.substring(0, 30) + '...' :
                        action.ck;
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${action.promotion_user_id || ''}</td>
                    <td>${action.user_id || ''}</td>
                    <td>${action.time || ''}</td>
                    <td>${action.ip || ''}</td>
                    <td>${action.ip_province || ''}</td>
                    <td>${action.status || ''}</td>
                    <td>${action.douyin_name || ''}</td>
                    <td>${action.douyin_id || ''}</td>
                    <td>${ckLength}</td>
                    <td title="${action.ck || ''}" style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${ckPreview}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 导出测试数据为Excel
        function exportTestDataToExcel() {
            console.log('📊 开始导出测试数据为Excel...');

            if (!testActionsData || testActionsData.length === 0) {
                updateStatus('没有可导出的数据，请先生成测试数据', 'error');
                return;
            }

            try {
                // 准备Excel数据
                const excelData = prepareExcelData(testActionsData);

                // 创建工作簿
                const workbook = createWorkbook(excelData);

                // 生成文件名
                const fileName = generateExcelFileName();

                // 导出Excel文件
                exportWorkbook(workbook, fileName);

                updateStatus(`Excel文件导出成功：${fileName}`, 'success');
                console.log('✅ Excel导出完成');

            } catch (error) {
                console.error('❌ Excel导出失败:', error);
                updateStatus('Excel导出失败: ' + error.message, 'error');
            }
        }

        // 准备Excel数据
        function prepareExcelData(actions) {
            console.log('📋 准备Excel数据，记录数量:', actions.length);

            // Excel表头
            const headers = [
                '推广用户ID',
                '用户ID',
                '操作时间',
                'IP地址',
                'IP省份',
                '操作状态',
                '抖音名称',
                '抖音号',
                'CK数据长度',
                'CK完整数据'
            ];

            // 转换数据
            const rows = actions.map(action => {
                // 处理CK数据 - 保存完整数据
                let ckLength = 0;
                let ckFullData = '';

                if (action.ck && action.ck !== '-') {
                    ckLength = action.ck.length;
                    ckFullData = action.ck; // 保存完整的CK数据
                }

                return [
                    action.promotion_user_id || '',
                    action.user_id || '',
                    action.time || '',
                    action.ip || '',
                    action.ip_province || '',
                    action.status || '',
                    action.douyin_name || '',
                    action.douyin_id || '',
                    ckLength,
                    ckFullData
                ];
            });

            return {
                headers: headers,
                rows: rows
            };
        }

        // 创建工作簿
        function createWorkbook(data) {
            console.log('📊 创建Excel工作簿...');

            // 创建工作表数据
            const wsData = [data.headers, ...data.rows];

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(wsData);

            // 设置列宽
            const colWidths = [
                { wch: 12 }, // 推广用户ID
                { wch: 10 }, // 用户ID
                { wch: 20 }, // 操作时间
                { wch: 15 }, // IP地址
                { wch: 10 }, // IP省份
                { wch: 12 }, // 操作状态
                { wch: 15 }, // 抖音名称
                { wch: 15 }, // 抖音号
                { wch: 12 }, // CK数据长度
                { wch: 80 }  // CK完整数据（增加列宽以容纳完整数据）
            ];
            ws['!cols'] = colWidths;

            // 设置表头样式
            const headerStyle = {
                font: { bold: true, color: { rgb: "FFFFFF" } },
                fill: { fgColor: { rgb: "366092" } },
                alignment: { horizontal: "center", vertical: "center" }
            };

            // 应用表头样式
            for (let i = 0; i < data.headers.length; i++) {
                const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
                if (!ws[cellRef]) ws[cellRef] = {};
                ws[cellRef].s = headerStyle;
            }

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "推广操作记录");

            return wb;
        }

        // 生成Excel文件名
        function generateExcelFileName() {
            const now = new Date();
            const dateStr = now.getFullYear() +
                String(now.getMonth() + 1).padStart(2, '0') +
                String(now.getDate()).padStart(2, '0');
            const timeStr = String(now.getHours()).padStart(2, '0') +
                String(now.getMinutes()).padStart(2, '0') +
                String(now.getSeconds()).padStart(2, '0');

            return `推广操作记录_${dateStr}_${timeStr}.xlsx`;
        }

        // 导出工作簿
        function exportWorkbook(workbook, fileName) {
            console.log('💾 导出Excel文件:', fileName);

            // 生成Excel文件
            const excelBuffer = XLSX.write(workbook, {
                bookType: 'xlsx',
                type: 'array',
                compression: true
            });

            // 创建Blob对象
            const blob = new Blob([excelBuffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;

            // 触发下载
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // 释放URL对象
            window.URL.revokeObjectURL(url);
        }

        // 清除测试数据
        function clearTestData() {
            testActionsData = [];
            document.getElementById('testDataBody').innerHTML = '';
            updateStatus('测试数据已清除', 'success');
        }

        // 更新状态显示
        function updateStatus(message, type = 'success') {
            const container = document.getElementById('statusContainer');
            const content = document.getElementById('statusContent');

            container.className = `status ${type}`;
            content.innerHTML = message;
            container.style.display = 'block';
        }

        // 页面加载时自动生成测试数据
        document.addEventListener('DOMContentLoaded', function () {
            generateTestData();
            console.log('🧪 Excel导出功能测试页面已加载');
        });
    </script>
</body>

</html>