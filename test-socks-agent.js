const { SocksProxyAgent } = require('socks-proxy-agent');

// 测试SocksProxyAgent的创建
function testSocksAgent() {
  console.log('🧪 测试SocksProxyAgent创建...');
  
  const testCases = [
    {
      name: '简单格式',
      proxy: '127.0.0.1:1080',
      url: 'socks5://127.0.0.1:1080'
    },
    {
      name: '带认证格式',
      proxy: 'user:<EMAIL>:1080',
      url: 'socks5://user:<EMAIL>:1080'
    },
    {
      name: '无效格式',
      proxy: 'invalid',
      url: 'socks5://invalid',
      expectError: true
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n🔍 测试: ${testCase.name}`);
    console.log(`   代理地址: ${testCase.proxy}`);
    console.log(`   SOCKS URL: ${testCase.url}`);
    
    try {
      const agent = new SocksProxyAgent(testCase.url);
      
      if (testCase.expectError) {
        console.log('   ❌ 预期失败但成功了');
      } else {
        console.log('   ✅ SocksProxyAgent创建成功');
        console.log(`   Agent类型: ${agent.constructor.name}`);
      }
    } catch (error) {
      if (testCase.expectError) {
        console.log(`   ✅ 预期失败: ${error.message}`);
      } else {
        console.log(`   ❌ 意外失败: ${error.message}`);
      }
    }
  });
  
  console.log('\n🎉 SocksProxyAgent测试完成！');
}

// 运行测试
if (require.main === module) {
  testSocksAgent();
}

module.exports = testSocksAgent;
