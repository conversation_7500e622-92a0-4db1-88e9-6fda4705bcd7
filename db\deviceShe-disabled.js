// MongoDB设备Schema已禁用
// 这个文件替代原来的deviceShe.js，避免MongoDB依赖

console.log('🚫 MongoDB设备Schema已禁用');

// 创建一个模拟的Schema对象以保持兼容性
const mockDeviceSchema = {
    // 模拟Schema属性
    deviceName: { type: String, index: { unique: true } },
    socketId: { type: String, index: true },
    device_width: Number,
    device_height: Number,
    device_UUID: String,
    taskStatus: { type: String, default: "空闲", index: true },
    deviceMsg: { type: String, default: "设备上线了" },
    taskName: { type: String, default: "无任务" },
    groupId: { type: String, ref: "groupMod" },
    
    // 模拟方法
    pre: () => {
        console.log('MongoDB Schema方法已禁用');
    },
    post: () => {
        console.log('MongoDB Schema方法已禁用');
    }
};

// 导出模拟对象
module.exports = mockDeviceSchema;

console.log('💡 提示: 设备管理功能已禁用，如需设备管理请使用MySQL数据库');
