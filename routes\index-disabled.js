const express = require("express");
const router = express.Router();
const debug = require("debug")("a-sokio-yun:server");

let multiparty = require("multiparty");
let fs = require("fs");

const {
  publicPath,
  getServerUrl,
  serverPort,
  appName,
  adminName,
} = require("../serverOpt");

// 使用禁用版本的依赖
const userMod = require("../db/userMod-disabled");

router.get("/getServerUrl", function (req, res, next) {
  res.send(getServerUrl().replace("http://", "") + ":" + serverPort);
});

router.get("/getAppName", function (req, res, next) {
  res.send(appName);
});

// 判断用户是否登录 是则跳转首页 否则跳转登录
router.get("/", function (req, res) {
  // 由于MongoDB已禁用，简化登录检查
  if (req.session && req.session.userName) {
    res.sendFile(publicPath + "/jinzhu.html");
  } else {
    res.sendFile(publicPath + "/page/login/login.html");
  }
});

router.get("/login", function (req, res) {
  if (req.session && req.session.userName) {
    res.sendFile(publicPath + "/jinzhu.html");
  } else {
    res.sendFile(publicPath + "/page/login/login.html");
  }
});

// 简化的登录接口 - MongoDB已禁用
router.post("/login", async function (req, res) {
  console.log("登录接口 - MongoDB已禁用，使用简化验证");

  try {
    const { txtUserName, txtUserPwd } = req.body;

    // 简化的验证逻辑 - 只验证管理员账户
    if (txtUserName === adminName && txtUserPwd === "123456") {
      req.session.userName = txtUserName;
      req.session.userId = "admin_id_" + Date.now();
      req.session.userLevel = 0;

      return res.json({
        code: 0,
        msg: "登录成功 (MongoDB已禁用，使用简化验证)",
        data: {
          userName: txtUserName,
          userId: req.session.userId,
          userLevel: 0,
        },
      });
    } else {
      throw new Error(
        "账号或密码验证失败 (仅支持管理员账户: " + adminName + "/123456)"
      );
    }
  } catch (error) {
    debug("登录错误:", error.message);
    res.json({
      code: -1,
      msg: "登录失败: " + error.message,
      data: null,
    });
  }
});

router.post("/storage/getconf", function (req, res) {
  res.json({
    code: 200,
    data: {
      DownloadTarUrl: "http://************:15001/tars",
      UploadTarUrl: "http://************:15001/uploadTar",
      msg: "",
    },
  });
});

router.post("/trillck/checkdn", function (req, res) {
  res.json({
    code: 200,
  });
});

router.post("/trillck/addone", function (req, res) {
  res.json({
    code: 200,
  });
});

// 通用文件上传接口
router.post("/uploads", function (req, res) {
  const form = new multiparty.Form();
  form.uploadDir = publicPath + "/uploads";
  if (!fs.existsSync(form.uploadDir)) {
    fs.mkdirSync(form.uploadDir, { recursive: true });
  }
  form.parse(req, function (err, fields, files) {
    if (err) {
      return res.status(500).json({
        code: -1,
        msg: "上传失败: " + err.message,
      });
    }

    const inputFile = files.file[0];
    const newPath = form.uploadDir + "/" + inputFile.originalFilename;

    fs.rename(inputFile.path, newPath, (err) => {
      if (err) {
        return res.status(500).json({
          code: -1,
          msg: "文件保存失败: " + err.message,
        });
      }
      res.json({
        code: 200,
        msg: "上传成功",
        path: "/uploads/" + inputFile.originalFilename,
      });
    });
  });
});

// 上传文件并更新version文件版本信息
router.post("/uploadTar", function (req, res) {
  let form = new multiparty.Form();
  form.uploadDir = publicPath + "/tars";
  if (!fs.existsSync(publicPath + "/tars")) {
    fs.mkdirSync(publicPath + "/tars");
  }
  form.maxFilesSize = 2000 * 1024 * 1024;

  form.parse(req, function (err, fields, files) {
    let inputFile = files.file[0];
    let newPath = form.uploadDir + "/" + inputFile.originalFilename;
    fs.renameSync(inputFile.path, newPath);
    res.json({
      code: 200,
      msg: "成功,版本",
    });
  });
});

router.post("/logout", function (req, res) {
  debug("退出登录之前req.session", req.session);
  req.session.destroy();
  debug("退出登录之后req.session", req.session);
  res.json({ code: 0, msg: "登出成功" });
});

module.exports = router;
