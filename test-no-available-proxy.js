const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

async function testNoAvailableProxy() {
    console.log(colors.bold('🧪 测试无可用账号情况下的API返回'));
    console.log('='.repeat(70));
    console.log(colors.cyan('目的: 验证当没有可用代理时API返回的数据结构\n'));

    try {
        // 1. 测试不存在的省份（单个代理）
        console.log(colors.blue('📋 1. 测试获取不存在省份的单个代理'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxy-by-province?province=不存在的省份'));
        
        const singleResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=不存在的省份`);
        console.log(colors.yellow('⚠️  单个代理无可用账号响应:'));
        console.log(JSON.stringify(singleResponse.data, null, 2));
        
        // 分析数据结构
        console.log(colors.yellow('\n📊 数据结构分析:'));
        console.log(`   - code: ${singleResponse.data.code} (${typeof singleResponse.data.code})`);
        console.log(`   - msg: "${singleResponse.data.msg}" (${typeof singleResponse.data.msg})`);
        console.log(`   - data: Object (${typeof singleResponse.data.data})`);
        
        if (singleResponse.data.data) {
            const data = singleResponse.data.data;
            console.log(`   - data.error_type: "${data.error_type}" (${typeof data.error_type})`);
            console.log(`   - data.province_searched: "${data.province_searched}" (${typeof data.province_searched})`);
            console.log(`   - data.province_converted: "${data.province_converted}" (${typeof data.province_converted})`);
            console.log(`   - data.reason: "${data.reason}" (${typeof data.reason})`);
            console.log(`   - data.suggestion: "${data.suggestion}" (${typeof data.suggestion})`);
            console.log(`   - data.searched_at: "${data.searched_at}" (${typeof data.searched_at})`);
        }

        // 2. 测试不存在的省份（批量代理）
        console.log(colors.blue('\n📦 2. 测试批量获取不存在省份的代理'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxies-by-province?province=不存在的省份&limit=5'));
        
        const batchResponse = await axios.get(`${BASE_URL}/get-proxies-by-province?province=不存在的省份&limit=5`);
        console.log(colors.yellow('⚠️  批量代理无可用账号响应:'));
        console.log(JSON.stringify(batchResponse.data, null, 2));
        
        // 分析数据结构
        console.log(colors.yellow('\n📊 数据结构分析:'));
        console.log(`   - code: ${batchResponse.data.code} (${typeof batchResponse.data.code})`);
        console.log(`   - msg: "${batchResponse.data.msg}" (${typeof batchResponse.data.msg})`);
        console.log(`   - data: Object (${typeof batchResponse.data.data})`);
        
        if (batchResponse.data.data) {
            const data = batchResponse.data.data;
            console.log(`   - data.error_type: "${data.error_type}" (${typeof data.error_type})`);
            console.log(`   - data.province_searched: "${data.province_searched}" (${typeof data.province_searched})`);
            console.log(`   - data.province_converted: "${data.province_converted}" (${typeof data.province_converted})`);
            console.log(`   - data.reason: "${data.reason}" (${typeof data.reason})`);
            console.log(`   - data.suggestion: "${data.suggestion}" (${typeof data.suggestion})`);
            console.log(`   - data.requested_limit: ${data.requested_limit} (${typeof data.requested_limit})`);
            console.log(`   - data.searched_at: "${data.searched_at}" (${typeof data.searched_at})`);
        }

        // 3. 测试拼音输入不存在的省份
        console.log(colors.blue('\n🔤 3. 测试拼音输入不存在的省份'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxy-by-province?province=nonexistent'));
        
        const pinyinResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=nonexistent`);
        console.log(colors.yellow('⚠️  拼音输入无可用账号响应:'));
        console.log(JSON.stringify(pinyinResponse.data, null, 2));

        // 4. 测试存在但无可用代理的省份（如果有的话）
        console.log(colors.blue('\n🚫 4. 测试可能存在但无可用代理的省份'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxy-by-province?province=西藏'));
        
        const tibetResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=西藏`);
        console.log(colors.yellow('⚠️  西藏省份响应:'));
        console.log(JSON.stringify(tibetResponse.data, null, 2));

        // 5. 对比有可用代理的情况
        console.log(colors.blue('\n✅ 5. 对比有可用代理的情况'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxy-by-province?province=浙江省'));
        
        const availableResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=浙江省`);
        console.log(colors.green('✅ 有可用代理的响应:'));
        console.log(JSON.stringify(availableResponse.data, null, 2));

        console.log(colors.bold('\n🎉 无可用账号测试完成!'));
        console.log('='.repeat(70));
        
        // 总结数据结构
        console.log(colors.bold('\n📋 无可用账号时的数据结构总结:'));
        
        console.log(colors.red('\n❌ 单个代理无可用账号响应格式:'));
        console.log('   {');
        console.log('     "code": -1,                    // Number: 失败状态码');
        console.log('     "msg": "无可用账号",            // String: 错误消息');
        console.log('     "data": {                      // Object: 详细错误信息');
        console.log('       "error_type": "NO_AVAILABLE_PROXY",');
        console.log('       "province_searched": "不存在的省份",');
        console.log('       "province_converted": "不存在的省份",');
        console.log('       "reason": "省份\\"不存在的省份\\"暂无可用代理IP",');
        console.log('       "suggestion": "请稍后重试或联系管理员添加该省份的代理",');
        console.log('       "searched_at": "2025-06-18T03:30:00.000Z"');
        console.log('     }');
        console.log('   }');
        
        console.log(colors.red('\n❌ 批量代理无可用账号响应格式:'));
        console.log('   {');
        console.log('     "code": -1,                    // Number: 失败状态码');
        console.log('     "msg": "无可用账号",            // String: 错误消息');
        console.log('     "data": {                      // Object: 详细错误信息');
        console.log('       "error_type": "NO_AVAILABLE_PROXY",');
        console.log('       "province_searched": "不存在的省份",');
        console.log('       "province_converted": "不存在的省份",');
        console.log('       "reason": "省份\\"不存在的省份\\"暂无可用代理IP",');
        console.log('       "suggestion": "请稍后重试或联系管理员添加该省份的代理",');
        console.log('       "requested_limit": 5,        // Number: 请求的数量限制');
        console.log('       "searched_at": "2025-06-18T03:30:00.000Z"');
        console.log('     }');
        console.log('   }');
        
        console.log(colors.green('\n✅ 有可用代理时的响应格式:'));
        console.log('   {');
        console.log('     "code": 0,                     // Number: 成功状态码');
        console.log('     "msg": "获取代理成功",          // String: 成功消息');
        console.log('     "data": {                      // Object: 代理信息');
        console.log('       "id": 1,');
        console.log('       "sk5": "proxy.example.com:1080",');
        console.log('       "ip": "***********",');
        console.log('       "province": "浙江省",');
        console.log('       "usage_count": 5,');
        console.log('       "status": "active"');
        console.log('     }');
        console.log('   }');

        console.log(colors.bold('\n🔍 关键区别:'));
        console.log('✅ 有可用代理: code=0, data包含代理信息');
        console.log('❌ 无可用代理: code=-1, msg="无可用账号", data包含错误详情');

    } catch (error) {
        console.error(colors.red('❌ 测试过程中发生错误:'));
        console.error('错误信息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
console.log(colors.bold('🚀 无可用账号API测试脚本'));
console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
console.log('\n' + colors.yellow('⏳ 3秒后开始测试...'));

setTimeout(() => {
    testNoAvailableProxy().catch(error => {
        console.error(colors.red('💥 测试脚本执行失败:'), error.message);
        process.exit(1);
    });
}, 3000);
