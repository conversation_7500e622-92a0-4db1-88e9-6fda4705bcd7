const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    magenta: (text) => `\x1b[35m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`,
    underline: (text) => `\x1b[4m${text}\x1b[0m`
};

// 分析代理信息
function analyzeProxyInfo(proxy) {
    console.log(colors.bold('\n🔍 代理信息详细分析:'));
    console.log('─'.repeat(60));
    
    // 基础信息
    console.log(colors.cyan('📋 基础信息:'));
    console.log(`   🆔 代理ID: ${colors.bold(proxy.id)}`);
    console.log(`   🌐 IP地址: ${colors.bold(proxy.ip)}`);
    console.log(`   📍 所在省份: ${colors.bold(proxy.province)}`);
    console.log(`   📊 使用次数: ${colors.bold(proxy.usage_count)}`);
    
    // SOCKS5地址分析
    console.log(colors.cyan('\n🔗 SOCKS5地址分析:'));
    console.log(`   完整地址: ${colors.bold(proxy.sk5)}`);
    
    // 尝试解析SOCKS5地址格式
    if (proxy.sk5.includes('|')) {
        const parts = proxy.sk5.split('|');
        console.log(`   🏠 主机地址: ${colors.yellow(parts[0])}`);
        console.log(`   🚪 端口号: ${colors.yellow(parts[1])}`);
        if (parts[2]) console.log(`   👤 用户名: ${colors.yellow(parts[2])}`);
        if (parts[3]) console.log(`   🔐 密码: ${colors.yellow(parts[3])}`);
        if (parts[4]) console.log(`   📅 到期时间: ${colors.yellow(parts[4])}`);
    } else if (proxy.sk5.includes(':')) {
        const [host, port] = proxy.sk5.split(':');
        console.log(`   🏠 主机地址: ${colors.yellow(host)}`);
        console.log(`   🚪 端口号: ${colors.yellow(port)}`);
    }
    
    // IP地址分析
    console.log(colors.cyan('\n🌍 IP地址分析:'));
    const ipParts = proxy.ip.split('.');
    if (ipParts.length === 4) {
        console.log(`   📊 IP段: ${colors.yellow(ipParts[0])}.${colors.yellow(ipParts[1])}.${colors.yellow(ipParts[2])}.${colors.yellow(ipParts[3])}`);
        console.log(`   🏢 网络段: ${colors.yellow(ipParts[0])}.${colors.yellow(ipParts[1])}.0.0/16`);
        
        // 判断IP类型
        const firstOctet = parseInt(ipParts[0]);
        let ipType = '';
        if (firstOctet >= 1 && firstOctet <= 126) {
            ipType = 'A类地址 (******* - ***************)';
        } else if (firstOctet >= 128 && firstOctet <= 191) {
            ipType = 'B类地址 (********* - ***************)';
        } else if (firstOctet >= 192 && firstOctet <= 223) {
            ipType = 'C类地址 (********* - ***************)';
        } else {
            ipType = '特殊地址段';
        }
        console.log(`   📋 IP类型: ${colors.yellow(ipType)}`);
        
        // 判断是否为私有IP
        if ((firstOctet === 10) ||
            (firstOctet === 172 && parseInt(ipParts[1]) >= 16 && parseInt(ipParts[1]) <= 31) ||
            (firstOctet === 192 && parseInt(ipParts[1]) === 168)) {
            console.log(`   🏠 地址性质: ${colors.red('私有IP地址')}`);
        } else {
            console.log(`   🌐 地址性质: ${colors.green('公网IP地址')}`);
        }
    }
    
    console.log('─'.repeat(60));
}

// 测试多次获取，观察负载均衡
async function testLoadBalancing() {
    console.log(colors.bold('\n⚖️  负载均衡测试'));
    console.log('连续获取5次山东省代理，观察使用次数变化...\n');
    
    const results = [];
    
    for (let i = 1; i <= 5; i++) {
        try {
            console.log(colors.blue(`🔄 第${i}次获取:`));
            const response = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
                params: { province: '山东省' }
            });
            
            if (response.data.code === 0 && response.data.data) {
                const proxy = response.data.data;
                results.push({
                    attempt: i,
                    id: proxy.id,
                    sk5: proxy.sk5,
                    usage_count: proxy.usage_count
                });
                
                console.log(`   ✅ 获取成功 - ID: ${proxy.id}, 使用次数: ${proxy.usage_count}`);
            } else {
                console.log(`   ❌ 获取失败: ${response.data.msg}`);
            }
            
            // 间隔1秒
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.log(`   ❌ 请求失败: ${error.message}`);
        }
    }
    
    // 分析结果
    console.log(colors.bold('\n📊 负载均衡分析结果:'));
    console.log('─'.repeat(50));
    
    if (results.length > 0) {
        const uniqueProxies = [...new Set(results.map(r => r.id))];
        console.log(`🎯 使用的不同代理数量: ${colors.bold(uniqueProxies.length)}`);
        
        results.forEach(result => {
            console.log(`   第${result.attempt}次: 代理ID ${result.id}, 使用次数 ${result.usage_count}`);
        });
        
        // 检查使用次数是否递增
        const usageCounts = results.map(r => r.usage_count);
        const isIncreasing = usageCounts.every((count, index) => 
            index === 0 || count >= usageCounts[index - 1]
        );
        
        if (isIncreasing) {
            console.log(colors.green('✅ 使用次数正确递增，负载均衡正常工作'));
        } else {
            console.log(colors.yellow('⚠️  使用次数变化异常，可能存在多个代理轮换'));
        }
    }
}

// 测试不同省份名称格式
async function testProvinceFormats() {
    console.log(colors.bold('\n🔤 省份名称格式测试'));
    console.log('测试不同的省份名称格式...\n');
    
    const testFormats = [
        '山东省',
        '山东',
        'shandong',
        'SHANDONG',
        '山东省济南市',
        '山东省青岛市',
        '山东济南',
        '山东青岛'
    ];
    
    for (const format of testFormats) {
        try {
            console.log(colors.blue(`🔍 测试格式: "${format}"`));
            const response = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
                params: { province: format }
            });
            
            if (response.data.code === 0 && response.data.data) {
                const proxy = response.data.data;
                console.log(colors.green(`   ✅ 匹配成功 - 返回: ${proxy.province} (${proxy.sk5.substring(0, 30)}...)`));
            } else {
                console.log(colors.red(`   ❌ 未找到匹配: ${response.data.msg}`));
            }
            
        } catch (error) {
            console.log(colors.red(`   ❌ 请求失败: ${error.message}`));
        }
        
        // 短暂延迟
        await new Promise(resolve => setTimeout(resolve, 500));
    }
}

// 主测试函数
async function runDetailedTest() {
    console.log(colors.bold('🧪 山东省代理IP详细测试报告'));
    console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
    console.log('='.repeat(70));
    
    try {
        // 1. 获取基础代理信息
        console.log(colors.bold('\n1️⃣  获取山东省代理基础信息'));
        const response = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
            params: { province: '山东省' }
        });
        
        if (response.data.code === 0 && response.data.data) {
            const proxy = response.data.data;
            
            // 显示原始响应
            console.log(colors.cyan('\n📄 原始API响应:'));
            console.log(JSON.stringify(response.data, null, 2));
            
            // 详细分析
            analyzeProxyInfo(proxy);
            
            // 2. 负载均衡测试
            await testLoadBalancing();
            
            // 3. 省份格式测试
            await testProvinceFormats();
            
            // 4. 最终状态检查
            console.log(colors.bold('\n4️⃣  最终状态检查'));
            const finalResponse = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
                params: { province: '山东省' }
            });
            
            if (finalResponse.data.code === 0 && finalResponse.data.data) {
                const finalProxy = finalResponse.data.data;
                console.log(colors.green(`✅ 最终使用次数: ${finalProxy.usage_count}`));
                console.log(colors.green(`✅ 代理仍然可用: ${finalProxy.sk5}`));
            }
            
        } else {
            console.log(colors.red('❌ 未找到山东省代理'));
            console.log('响应:', response.data);
        }
        
        console.log(colors.bold('\n🎉 详细测试完成!'));
        console.log('='.repeat(70));
        
    } catch (error) {
        console.error(colors.red('❌ 测试过程中发生错误:'));
        console.error(error.message);
        if (error.response) {
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
runDetailedTest().catch(error => {
    console.error(colors.red('💥 测试脚本执行失败:'), error.message);
    process.exit(1);
});
