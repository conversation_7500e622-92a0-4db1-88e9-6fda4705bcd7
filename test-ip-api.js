const axios = require('axios');

async function testIPAPI() {
  try {
    console.log('开始测试IP代理API...');
    
    // 测试获取代理列表
    console.log('\n1. 测试获取代理列表...');
    try {
      const response = await axios.get('http://localhost:15001/api/douyin/proxies');
      console.log('✓ 获取代理列表成功:', response.status);
      console.log('返回数据:', response.data);
    } catch (error) {
      console.error('✗ 获取代理列表失败:', error.response?.status, error.response?.data || error.message);
    }
    
    // 测试添加代理
    console.log('\n2. 测试添加代理...');
    try {
      const newProxy = {
        sk5: '127.0.0.1:1080',
        ip: '127.0.0.1',
        province: '北京市',
        usage_count: 0,
        status: 'active'
      };
      
      const response = await axios.post('http://localhost:15001/api/douyin/proxies', newProxy);
      console.log('✓ 添加代理成功:', response.status);
      console.log('返回数据:', response.data);
    } catch (error) {
      console.error('✗ 添加代理失败:', error.response?.status, error.response?.data || error.message);
    }
    
    // 再次获取列表验证
    console.log('\n3. 再次获取代理列表验证...');
    try {
      const response = await axios.get('http://localhost:15001/api/douyin/proxies');
      console.log('✓ 验证成功，当前代理数量:', response.data.data?.length || 0);
    } catch (error) {
      console.error('✗ 验证失败:', error.response?.status, error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testIPAPI();
