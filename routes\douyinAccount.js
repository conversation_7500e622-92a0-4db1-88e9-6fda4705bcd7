const express = require("express");
const router = express.Router();
const mysql = require("mysql2/promise");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456", // MySQL密码
  database: "douyin",
  charset: "utf8mb4",
  connectionLimit: 10,
  waitForConnections: true,
  queueLimit: 0,
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 初始化数据库和表
async function initDatabase() {
  try {
    // 先创建一个不指定数据库的连接来创建数据库
    const tempConfig = { ...dbConfig };
    delete tempConfig.database;
    const tempPool = mysql.createPool(tempConfig);

    const connection = await tempPool.getConnection();

    // 创建数据库
    await connection.query(
      `CREATE DATABASE IF NOT EXISTS douyin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`
    );
    console.log("数据库 douyin 已创建或已存在");

    connection.release();
    await tempPool.end();

    // 现在使用指定数据库的连接创建表
    const dbConnection = await pool.getConnection();
    await dbConnection.query(`
      CREATE TABLE IF NOT EXISTS user (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        status TINYINT DEFAULT 1 COMMENT '1=active, 0=inactive, 2=disabled',
        ckcount INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    dbConnection.release();
    console.log("user表已创建或已存在");
  } catch (err) {
    console.error("初始化数据库失败:", err);
  }
}

// 初始化数据库和表
initDatabase();

// 状态映射函数
function getStatusValue(statusStr) {
  const statusMap = {
    active: 1,
    inactive: 0,
    disabled: 2,
  };
  return statusMap[statusStr] !== undefined ? statusMap[statusStr] : statusStr;
}

function getStatusText(statusValue) {
  const statusMap = {
    1: "active",
    0: "inactive",
    2: "disabled",
  };
  return statusMap[statusValue] || "unknown";
}

// 获取所有账号
router.get("/accounts", async (req, res) => {
  try {
    const { page = 1, limit = 20, search, status } = req.query;

    // 构建查询条件
    let whereClause = "WHERE 1=1";
    let queryParams = [];

    if (search) {
      whereClause += " AND username LIKE ?";
      queryParams.push(`%${search}%`);
    }

    if (status) {
      whereClause += " AND status = ?";
      queryParams.push(getStatusValue(status));
    }

    // 分页查询
    const offset = (page - 1) * limit;
    const accountsQuery = `
      SELECT id, username, password, status, ckcount, created_at, updated_at
      FROM user
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    queryParams.push(parseInt(limit), offset);

    const [accounts] = await pool.execute(accountsQuery, queryParams);

    // 转换状态为字符串
    const formattedAccounts = accounts.map((account) => ({
      ...account,
      status: getStatusText(account.status),
    }));

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM user ${whereClause}`;
    const [countResult] = await pool.execute(
      countQuery,
      queryParams.slice(0, -2)
    );
    const total = countResult[0].total;

    // 统计信息
    const [statsResult] = await pool.execute(`
      SELECT
        status,
        COUNT(*) as count
      FROM user
      GROUP BY status
    `);

    const statsObj = {
      total: total,
      active: 0,
      inactive: 0,
      disabled: 0,
    };

    statsResult.forEach((stat) => {
      const statusText = getStatusText(stat.status);
      statsObj[statusText] = stat.count;
    });

    res.json({
      code: 0,
      msg: "获取成功",
      data: formattedAccounts,
      stats: statsObj,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("获取账号列表失败:", error);
    res.json({ code: -1, msg: "获取账号列表失败: " + error.message });
  }
});

// 添加账号
router.post("/accounts", async (req, res) => {
  try {
    const { username, password, status, ckcount } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return res.json({ code: -1, msg: "用户名和密码为必填项" });
    }

    // 检查用户名是否重复
    const [existingUsers] = await pool.execute(
      "SELECT id FROM user WHERE username = ?",
      [username]
    );

    if (existingUsers.length > 0) {
      return res.json({ code: -1, msg: "用户名已存在" });
    }

    // 插入新账号
    const statusValue = getStatusValue(status || "active");
    const [result] = await pool.execute(
      "INSERT INTO user (username, password, status, ckcount) VALUES (?, ?, ?, ?)",
      [username, password, statusValue, parseInt(ckcount) || 0]
    );

    // 获取新插入的账号信息
    const [newAccount] = await pool.execute(
      "SELECT id, username, password, status, ckcount, created_at, updated_at FROM user WHERE id = ?",
      [result.insertId]
    );

    // 转换状态为字符串
    const formattedAccount = {
      ...newAccount[0],
      status: getStatusText(newAccount[0].status),
    };

    res.json({
      code: 0,
      msg: "账号添加成功",
      data: formattedAccount,
    });
  } catch (error) {
    console.error("添加账号失败:", error);
    res.json({ code: -1, msg: "添加账号失败: " + error.message });
  }
});

// 更新账号
router.put("/accounts/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const { username, password, status, ckcount } = req.body;

    // 检查账号是否存在
    const [existingAccount] = await pool.execute(
      "SELECT id FROM user WHERE id = ?",
      [id]
    );

    if (existingAccount.length === 0) {
      return res.json({ code: -1, msg: "账号不存在" });
    }

    // 检查用户名是否与其他账号重复
    const [duplicateUsers] = await pool.execute(
      "SELECT id FROM user WHERE username = ? AND id != ?",
      [username, id]
    );

    if (duplicateUsers.length > 0) {
      return res.json({ code: -1, msg: "用户名已存在" });
    }

    // 更新账号信息
    const statusValue = getStatusValue(status);
    await pool.execute(
      "UPDATE user SET username = ?, password = ?, status = ?, ckcount = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
      [username, password, statusValue, parseInt(ckcount) || 0, id]
    );

    // 获取更新后的账号信息
    const [updatedAccount] = await pool.execute(
      "SELECT id, username, password, status, ckcount, created_at, updated_at FROM user WHERE id = ?",
      [id]
    );

    // 转换状态为字符串
    const formattedAccount = {
      ...updatedAccount[0],
      status: getStatusText(updatedAccount[0].status),
    };

    res.json({
      code: 0,
      msg: "账号更新成功",
      data: formattedAccount,
    });
  } catch (error) {
    console.error("更新账号失败:", error);
    res.json({ code: -1, msg: "更新账号失败: " + error.message });
  }
});

// 删除账号
router.delete("/accounts/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // 检查账号是否存在
    const [existingAccount] = await pool.execute(
      "SELECT id FROM user WHERE id = ?",
      [id]
    );

    if (existingAccount.length === 0) {
      return res.json({ code: -1, msg: "账号不存在" });
    }

    // 删除账号
    await pool.execute("DELETE FROM user WHERE id = ?", [id]);

    res.json({
      code: 0,
      msg: "账号删除成功",
    });
  } catch (error) {
    console.error("删除账号失败:", error);
    res.json({ code: -1, msg: "删除账号失败: " + error.message });
  }
});

// 批量导入账号
router.post("/accounts/batch-import", async (req, res) => {
  try {
    const { accounts } = req.body;

    if (!Array.isArray(accounts) || accounts.length === 0) {
      return res.json({ code: -1, msg: "请提供有效的账号数据" });
    }

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    for (let i = 0; i < accounts.length; i++) {
      try {
        const accountData = accounts[i];

        // 验证必填字段
        if (!accountData.username || !accountData.password) {
          throw new Error("用户名和密码为必填项");
        }

        // 检查重复
        const [existingUsers] = await pool.execute(
          "SELECT id FROM user WHERE username = ?",
          [accountData.username]
        );

        if (existingUsers.length > 0) {
          throw new Error("用户名已存在");
        }

        // 插入新账号
        const statusValue = getStatusValue(accountData.status || "active");
        await pool.execute(
          "INSERT INTO user (username, password, status, ckcount) VALUES (?, ?, ?, ?)",
          [
            accountData.username,
            accountData.password,
            statusValue,
            parseInt(accountData.ckcount) || 0,
          ]
        );

        successCount++;
      } catch (error) {
        errorCount++;
        errors.push(`第${i + 1}行: ${error.message}`);
      }
    }

    res.json({
      code: 0,
      msg: `批量导入完成：成功${successCount}个，失败${errorCount}个`,
      data: {
        successCount,
        errorCount,
        errors: errors.slice(0, 10), // 只返回前10个错误
      },
    });
  } catch (error) {
    console.error("批量导入失败:", error);
    res.json({ code: -1, msg: "批量导入失败: " + error.message });
  }
});

module.exports = router;
