const mysql = require("mysql2/promise");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
};

async function modifyUserTable() {
  console.log("🔧 开始修改user表结构以适应推广用户管理...");

  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log("✅ 数据库连接成功");

    // 检查并添加推广用户管理所需的字段
    const fieldsToAdd = [
      {
        name: "user_type",
        sql: `ALTER TABLE user ADD COLUMN user_type ENUM('normal', 'promoter') DEFAULT 'normal' COMMENT '用户类型：normal=普通用户，promoter=推广用户'`,
      },
      {
        name: "promotion_link",
        sql: `ALTER TABLE user ADD COLUMN promotion_link VARCHAR(500) NULL COMMENT '推广链接'`,
      },
      {
        name: "promoter_id",
        sql: `ALTER TABLE user ADD COLUMN promoter_id VARCHAR(50) NULL COMMENT '推广用户ID，仅推广用户使用'`,
      },
      {
        name: "visit_count",
        sql: `ALTER TABLE user ADD COLUMN visit_count INT DEFAULT 0 COMMENT '访问次数'`,
      },
      {
        name: "unique_ip_count",
        sql: `ALTER TABLE user ADD COLUMN unique_ip_count INT DEFAULT 0 COMMENT '独立IP数'`,
      },
      {
        name: "scan_count",
        sql: `ALTER TABLE user ADD COLUMN scan_count INT DEFAULT 0 COMMENT '扫码数量'`,
      },
      {
        name: "success_count",
        sql: `ALTER TABLE user ADD COLUMN success_count INT DEFAULT 0 COMMENT '成功数量'`,
      },
      {
        name: "fail_count",
        sql: `ALTER TABLE user ADD COLUMN fail_count INT DEFAULT 0 COMMENT '失败数量'`,
      },
      {
        name: "expire_count",
        sql: `ALTER TABLE user ADD COLUMN expire_count INT DEFAULT 0 COMMENT '过期数量'`,
      },
      {
        name: "last_stat_update",
        sql: `ALTER TABLE user ADD COLUMN last_stat_update TIMESTAMP NULL COMMENT '最后统计更新时间'`,
      },
    ];

    // 检查现有字段
    const [existingColumns] = await connection.execute("DESCRIBE user");
    const existingFieldNames = existingColumns.map((col) => col.Field);

    console.log(
      `📋 准备添加字段，当前表有 ${existingFieldNames.length} 个字段...`
    );

    // 添加不存在的字段
    for (const field of fieldsToAdd) {
      if (!existingFieldNames.includes(field.name)) {
        try {
          await connection.execute(field.sql);
          console.log(`✅ 添加字段成功: ${field.name}`);
        } catch (error) {
          console.error(`❌ 添加字段失败 ${field.name}:`, error.message);
        }
      } else {
        console.log(`⚠️  字段已存在: ${field.name}`);
      }
    }

    // 添加索引
    const indexesToAdd = [
      {
        name: "idx_promoter_id",
        sql: `ALTER TABLE user ADD INDEX idx_promoter_id (promoter_id)`,
      },
      {
        name: "idx_user_type",
        sql: `ALTER TABLE user ADD INDEX idx_user_type (user_type)`,
      },
    ];

    for (const index of indexesToAdd) {
      try {
        await connection.execute(index.sql);
        console.log(`✅ 添加索引成功: ${index.name}`);
      } catch (error) {
        if (error.code === "ER_DUP_KEYNAME") {
          console.log(`⚠️  索引已存在: ${index.name}`);
        } else {
          console.error(`❌ 添加索引失败 ${index.name}:`, error.message);
        }
      }
    }

    console.log(`📋 准备执行 ${alterStatements.length} 条ALTER语句...`);

    for (let i = 0; i < alterStatements.length; i++) {
      const statement = alterStatements[i];
      try {
        await connection.execute(statement);
        console.log(
          `✅ 执行成功 (${i + 1}/${
            alterStatements.length
          }): ${statement.substring(0, 60)}...`
        );
      } catch (error) {
        if (
          error.code === "ER_DUP_FIELDNAME" ||
          error.code === "ER_DUP_KEYNAME"
        ) {
          console.log(
            `⚠️  字段/索引已存在 (${i + 1}/${
              alterStatements.length
            }): ${statement.substring(0, 60)}...`
          );
        } else {
          console.error(
            `❌ 执行失败 (${i + 1}/${alterStatements.length}):`,
            error.message
          );
        }
      }
    }

    // 插入示例推广用户数据
    console.log("\n📊 插入示例推广用户数据...");

    const insertPromotionUsers = [
      {
        username: "promoter1001",
        password: "123456",
        status: 1,
        user_type: "promoter",
        promoter_id: "1001",
        promotion_link: "http://localhost:15001?id=1001",
        ckcount: 0,
      },
      {
        username: "promoter1002",
        password: "123456",
        status: 1,
        user_type: "promoter",
        promoter_id: "1002",
        promotion_link: "http://localhost:15001?id=1002",
        ckcount: 0,
      },
    ];

    for (const userData of insertPromotionUsers) {
      try {
        await connection.execute(
          `INSERT INTO user (username, password, status, user_type, promoter_id, promotion_link, ckcount)
           VALUES (?, ?, ?, ?, ?, ?, ?)
           ON DUPLICATE KEY UPDATE
           user_type = VALUES(user_type),
           promoter_id = VALUES(promoter_id),
           promotion_link = VALUES(promotion_link)`,
          [
            userData.username,
            userData.password,
            userData.status,
            userData.user_type,
            userData.promoter_id,
            userData.promotion_link,
            userData.ckcount,
          ]
        );
        console.log(
          `✅ 插入/更新推广用户: ${userData.username} (ID: ${userData.promoter_id})`
        );
      } catch (error) {
        if (error.code === "ER_DUP_ENTRY") {
          console.log(`⚠️  推广用户已存在: ${userData.username}`);
        } else {
          console.error(`❌ 插入推广用户失败:`, error.message);
        }
      }
    }

    // 验证修改结果
    console.log("\n🔍 验证修改结果...");

    // 查看新的表结构
    const [columns] = await connection.execute("DESCRIBE user");
    console.log("\n📋 修改后的user表结构:");
    console.log("字段名\t\t类型\t\t\t空值\t键\t默认值");
    console.log("=".repeat(70));
    columns.forEach((col) => {
      console.log(
        `${col.Field.padEnd(20)}\t${col.Type.padEnd(20)}\t${col.Null}\t${
          col.Key
        }\t${col.Default || "NULL"}`
      );
    });

    // 查看推广用户数据
    const [promoters] = await connection.execute(
      "SELECT username, promoter_id, promotion_link, user_type, status FROM user WHERE user_type = 'promoter'"
    );
    console.log("\n📊 推广用户数据:");
    promoters.forEach((user) => {
      console.log(
        `✅ ${user.username} (ID: ${user.promoter_id}) - ${
          user.promotion_link
        } - 状态: ${user.status ? "启用" : "禁用"}`
      );
    });

    await connection.end();

    console.log("\n🎉 user表结构修改完成！");
    console.log("\n📋 新增字段说明:");
    console.log("- user_type: 用户类型 (normal/promoter)");
    console.log("- promoter_id: 推广用户ID");
    console.log("- promotion_link: 推广链接");
    console.log("- visit_count: 访问次数");
    console.log("- unique_ip_count: 独立IP数");
    console.log("- scan_count: 扫码数量");
    console.log("- success_count: 成功数量");
    console.log("- fail_count: 失败数量");
    console.log("- expire_count: 过期数量");
    console.log("- last_stat_update: 最后统计更新时间");
  } catch (error) {
    console.error("💥 修改失败:", error.message);
    process.exit(1);
  }
}

modifyUserTable();
