<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>推广API测试工具</h1>
        
        <div class="test-section">
            <h3>1. 管理员登录测试</h3>
            <button class="btn" onclick="testAdminLogin()">测试管理员登录</button>
            <div id="adminLoginResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 获取推广用户列表</h3>
            <button class="btn" onclick="testGetPromotionUsers()">获取推广用户列表</button>
            <div id="promotionUsersResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 添加推广用户测试</h3>
            <button class="btn" onclick="testAddPromotionUser()">添加测试推广用户</button>
            <div id="addUserResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 推广用户登录测试</h3>
            <button class="btn" onclick="testPromoterLogin()">测试推广用户登录</button>
            <div id="promoterLoginResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 获取推广统计数据</h3>
            <button class="btn" onclick="testGetStats()">获取统计数据</button>
            <div id="statsResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>6. 数据库直接查询测试</h3>
            <button class="btn" onclick="testDirectQuery()">直接查询user表</button>
            <div id="directQueryResult" class="result"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<span class="${isError ? 'error' : 'success'}">${new Date().toLocaleTimeString()}</span>\n${message}`;
        }

        async function testAdminLogin() {
            try {
                showResult('adminLoginResult', '正在测试管理员登录...');
                
                const response = await fetch('/api/promotion/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: '123456'
                    })
                });
                
                const result = await response.json();
                showResult('adminLoginResult', `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, result.code !== 0);
            } catch (error) {
                showResult('adminLoginResult', `错误: ${error.message}`, true);
            }
        }

        async function testGetPromotionUsers() {
            try {
                showResult('promotionUsersResult', '正在获取推广用户列表...');
                
                const response = await fetch('/api/promotion/admin/promotion-users');
                const result = await response.json();
                
                showResult('promotionUsersResult', `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, result.code !== 0);
            } catch (error) {
                showResult('promotionUsersResult', `错误: ${error.message}`, true);
            }
        }

        async function testAddPromotionUser() {
            try {
                showResult('addUserResult', '正在添加测试推广用户...');
                
                const response = await fetch('/api/promotion/admin/add-promotion-user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 'test' + Date.now(),
                        username: 'test_user_' + Date.now(),
                        password: '123456'
                    })
                });
                
                const result = await response.json();
                showResult('addUserResult', `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, result.code !== 0);
            } catch (error) {
                showResult('addUserResult', `错误: ${error.message}`, true);
            }
        }

        async function testPromoterLogin() {
            try {
                showResult('promoterLoginResult', '正在测试推广用户登录...');
                
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: '1001',
                        password: '123456'
                    })
                });
                
                const result = await response.json();
                showResult('promoterLoginResult', `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, result.code !== 0);
            } catch (error) {
                showResult('promoterLoginResult', `错误: ${error.message}`, true);
            }
        }

        async function testGetStats() {
            try {
                showResult('statsResult', '正在获取统计数据...');
                
                const response = await fetch('/api/promotion/promoter/today-stats');
                const result = await response.json();
                
                showResult('statsResult', `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, result.code !== 0);
            } catch (error) {
                showResult('statsResult', `错误: ${error.message}`, true);
            }
        }

        async function testDirectQuery() {
            try {
                showResult('directQueryResult', '正在直接查询数据库...');
                
                // 这里我们通过一个简单的API来查询数据库
                const response = await fetch('/api/douyin/test-user-table');
                
                if (response.ok) {
                    const result = await response.text();
                    showResult('directQueryResult', `数据库查询成功:\n${result}`);
                } else {
                    showResult('directQueryResult', `数据库查询失败: HTTP ${response.status}`, true);
                }
            } catch (error) {
                showResult('directQueryResult', `错误: ${error.message}`, true);
            }
        }

        // 页面加载时自动运行一些基础测试
        window.addEventListener('load', () => {
            console.log('推广API测试页面已加载');
            
            // 自动测试获取推广用户列表
            setTimeout(() => {
                testGetPromotionUsers();
            }, 1000);
        });
    </script>
</body>
</html>
