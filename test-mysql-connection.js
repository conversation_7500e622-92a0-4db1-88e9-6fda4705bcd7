const mysql = require('mysql2/promise');

// 测试不同的MySQL配置
const testConfigs = [
    {
        name: "无密码配置",
        config: {
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'mysql',
            connectTimeout: 5000
        }
    },
    {
        name: "123456密码配置",
        config: {
            host: 'localhost',
            user: 'root',
            password: '123456',
            database: 'mysql',
            connectTimeout: 5000
        }
    },
    {
        name: "root密码配置",
        config: {
            host: 'localhost',
            user: 'root',
            password: 'root',
            database: 'mysql',
            connectTimeout: 5000
        }
    },
    {
        name: "空字符串密码配置",
        config: {
            host: 'localhost',
            user: 'root',
            password: null,
            database: 'mysql',
            connectTimeout: 5000
        }
    }
];

async function testMySQLConnection() {
    console.log('🧪 MySQL连接测试工具');
    console.log('='.repeat(50));
    
    for (const testConfig of testConfigs) {
        console.log(`\n📋 测试配置: ${testConfig.name}`);
        console.log(`   用户: ${testConfig.config.user}`);
        console.log(`   密码: ${testConfig.config.password === '' ? '(空字符串)' : testConfig.config.password === null ? '(null)' : testConfig.config.password}`);
        
        try {
            const connection = await mysql.createConnection(testConfig.config);
            
            // 测试查询
            const [rows] = await connection.execute('SELECT VERSION() as version, USER() as user');
            
            console.log(`   ✅ 连接成功!`);
            console.log(`   📊 MySQL版本: ${rows[0].version}`);
            console.log(`   👤 当前用户: ${rows[0].user}`);
            
            // 测试数据库权限
            try {
                await connection.execute('SHOW DATABASES');
                console.log(`   🔑 数据库权限: 正常`);
            } catch (error) {
                console.log(`   ⚠️  数据库权限: 受限 - ${error.message}`);
            }
            
            await connection.end();
            
            // 如果这个配置成功，更新config.json
            const fs = require('fs');
            const configPath = './config.json';
            const newConfig = {
                mysql: {
                    host: testConfig.config.host,
                    user: testConfig.config.user,
                    password: testConfig.config.password || '',
                    database: 'douyin',
                    connectionLimit: 10,
                    multipleStatements: true,
                    insecureAuth: true
                }
            };
            
            fs.writeFileSync(configPath, JSON.stringify(newConfig, null, 2));
            console.log(`   💾 已更新config.json配置文件`);
            
            break; // 找到可用配置就停止测试
            
        } catch (error) {
            console.log(`   ❌ 连接失败: ${error.message}`);
            
            if (error.code === 'ER_ACCESS_DENIED_ERROR') {
                console.log(`   💡 建议: 密码不正确，请尝试其他密码`);
            } else if (error.code === 'ECONNREFUSED') {
                console.log(`   💡 建议: MySQL服务未启动`);
            } else if (error.code === 'ETIMEDOUT') {
                console.log(`   💡 建议: 连接超时，检查MySQL服务状态`);
            }
        }
    }
    
    console.log('\n🔧 如果所有配置都失败，请尝试以下解决方案:');
    console.log('1. 运行 reset-mysql.bat 重置MySQL密码');
    console.log('2. 手动重启MySQL服务: net stop mysql && net start mysql');
    console.log('3. 检查MySQL服务是否正在运行: sc query mysql');
    console.log('4. 查看MySQL错误日志');
    
    console.log('\n📞 常用MySQL命令:');
    console.log('- 连接MySQL: mysql -u root -p');
    console.log('- 查看用户: SELECT User, Host FROM mysql.user;');
    console.log('- 重置密码: ALTER USER "root"@"localhost" IDENTIFIED BY "";');
}

// 运行测试
testMySQLConnection().catch(error => {
    console.error('💥 测试脚本执行失败:', error.message);
    process.exit(1);
});
