const mysql = require('mysql2/promise');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4"
};

async function checkUserTable() {
  console.log('🔍 检查user表结构...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 查看表结构
    const [columns] = await connection.execute('DESCRIBE user');
    console.log('\n📋 当前user表结构:');
    console.log('字段名\t\t类型\t\t\t空值\t键\t默认值\t\t额外');
    console.log('='.repeat(80));
    columns.forEach(col => {
      console.log(`${col.Field.padEnd(15)}\t${col.Type.padEnd(20)}\t${col.Null}\t${col.Key}\t${col.Default || 'NULL'}\t\t${col.Extra}`);
    });
    
    // 查看现有数据
    const [rows] = await connection.execute('SELECT * FROM user LIMIT 5');
    console.log(`\n📊 现有数据 (共 ${rows.length} 条示例):`);
    if (rows.length > 0) {
      console.log(JSON.stringify(rows, null, 2));
    } else {
      console.log('暂无数据');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('💥 检查失败:', error.message);
  }
}

checkUserTable();
