const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
  multipleStatements: true
};

async function initPromotionSystem() {
  console.log('🚀 开始初始化推广用户管理系统...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 读取SQL文件
    const sqlFile = path.join(__dirname, 'db', 'promotion-tables.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // 分割SQL语句
    const statements = sqlContent.split(';').filter(stmt => stmt.trim());
    
    console.log(`📋 准备执行 ${statements.length} 条SQL语句...`);
    
    // 执行SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          await connection.execute(statement);
          console.log(`✅ 执行成功 (${i + 1}/${statements.length}): ${statement.substring(0, 50)}...`);
        } catch (error) {
          if (error.code === 'ER_TABLE_EXISTS_ERROR' || error.code === 'ER_DUP_ENTRY') {
            console.log(`⚠️  跳过已存在 (${i + 1}/${statements.length}): ${statement.substring(0, 50)}...`);
          } else {
            console.error(`❌ 执行失败 (${i + 1}/${statements.length}):`, error.message);
          }
        }
      }
    }
    
    // 验证表是否创建成功
    console.log('\n🔍 验证数据库表...');
    
    const tables = [
      'promotion_users',
      'promotion_visits', 
      'promotion_actions',
      'promotion_daily_stats',
      'admin_users'
    ];
    
    for (const tableName of tables) {
      try {
        const [rows] = await connection.execute(`SHOW TABLES LIKE '${tableName}'`);
        if (rows.length > 0) {
          console.log(`✅ 表 ${tableName} 存在`);
          
          // 显示表结构
          const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
          console.log(`   字段数: ${columns.length}`);
        } else {
          console.log(`❌ 表 ${tableName} 不存在`);
        }
      } catch (error) {
        console.error(`❌ 检查表 ${tableName} 失败:`, error.message);
      }
    }
    
    // 检查示例数据
    console.log('\n📊 检查示例数据...');
    
    try {
      const [adminUsers] = await connection.execute('SELECT COUNT(*) as count FROM admin_users');
      console.log(`✅ 管理员用户数: ${adminUsers[0].count}`);
      
      const [promotionUsers] = await connection.execute('SELECT COUNT(*) as count FROM promotion_users');
      console.log(`✅ 推广用户数: ${promotionUsers[0].count}`);
      
      if (promotionUsers[0].count > 0) {
        const [users] = await connection.execute('SELECT user_id, username, promotion_link FROM promotion_users LIMIT 3');
        console.log('📋 推广用户示例:');
        users.forEach(user => {
          console.log(`   ${user.user_id} - ${user.username}: ${user.promotion_link}`);
        });
      }
    } catch (error) {
      console.error('❌ 检查示例数据失败:', error.message);
    }
    
    await connection.end();
    
    console.log('\n🎉 推广用户管理系统初始化完成！');
    console.log('\n📋 系统功能说明:');
    console.log('1. 管理员可以添加推广用户');
    console.log('2. 推广用户可以登录查看推广数据');
    console.log('3. 系统自动记录访问和操作统计');
    console.log('4. 支持数据导出功能');
    
    console.log('\n🔗 访问地址:');
    console.log('- 推广用户登录: http://localhost:15001/page/promotion/promoter-login.html');
    console.log('- 管理员推广管理: http://localhost:15001/page/promotion/admin-promotion.html');
    
    console.log('\n🔑 默认账户:');
    console.log('- 管理员: admin / 123456');
    console.log('- 推广用户示例: 1001 / 123456');
    
  } catch (error) {
    console.error('💥 初始化失败:', error.message);
    process.exit(1);
  }
}

// 运行初始化
if (require.main === module) {
  initPromotionSystem();
}

module.exports = initPromotionSystem;
