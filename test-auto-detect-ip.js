const axios = require('axios');

// 测试自动检测IP功能
async function testAutoDetectIP() {
  console.log('🧪 测试自动检测SOCKS5代理IP功能...');
  
  const baseURL = 'http://localhost:15001';
  
  try {
    // 1. 测试检测API
    console.log('\n📋 1. 测试代理IP检测API:');
    
    // 这里使用一个示例代理地址进行测试
    // 注意：实际使用时需要替换为真实可用的SOCKS5代理
    const testProxies = [
      '127.0.0.1:1080',           // 本地代理
      '*************:1080',       // 内网代理
      'user:<EMAIL>:1080'  // 带认证的代理
    ];
    
    for (const proxyAddress of testProxies) {
      console.log(`\n🔍 测试代理: ${proxyAddress}`);
      
      try {
        const response = await axios.post(`${baseURL}/api/douyin/test-proxy-detection`, {
          sk5: proxyAddress
        });
        
        console.log('📡 API响应:', response.data);
        
        if (response.data.code === 0) {
          console.log('✅ 检测成功:');
          console.log(`   IP地址: ${response.data.data.ip}`);
          console.log(`   省份: ${response.data.data.province}`);
        } else {
          console.log('❌ 检测失败:', response.data.msg);
        }
      } catch (error) {
        console.log('❌ 请求失败:', error.message);
      }
    }
    
    // 2. 测试自动添加代理
    console.log('\n📋 2. 测试自动添加代理:');
    
    const testAddData = {
      sk5: '127.0.0.1:1080',
      auto_detect_ip: true,
      usage_count: 0,
      status: 'active'
    };
    
    console.log('📤 发送添加代理请求（启用自动检测）...');
    console.log('请求数据:', testAddData);
    
    try {
      const addResponse = await axios.post(`${baseURL}/api/douyin/proxies`, testAddData);
      
      console.log('📡 添加代理响应:', addResponse.data);
      
      if (addResponse.data.code === 0) {
        console.log('✅ 代理添加成功:');
        console.log(`   ID: ${addResponse.data.data.id}`);
        console.log(`   SOCKS5: ${addResponse.data.data.sk5}`);
        console.log(`   IP: ${addResponse.data.data.ip}`);
        console.log(`   省份: ${addResponse.data.data.province}`);
        console.log(`   状态: ${addResponse.data.data.status}`);
      } else {
        console.log('❌ 代理添加失败:', addResponse.data.msg);
      }
    } catch (error) {
      console.log('❌ 添加代理请求失败:', error.message);
      if (error.response) {
        console.log('响应数据:', error.response.data);
      }
    }
    
    // 3. 测试手动添加代理（不自动检测）
    console.log('\n📋 3. 测试手动添加代理（不自动检测）:');
    
    const manualAddData = {
      sk5: '*************:1080',
      ip: '*************',
      province: '测试省份',
      auto_detect_ip: false,
      usage_count: 0,
      status: 'active'
    };
    
    console.log('📤 发送手动添加代理请求...');
    console.log('请求数据:', manualAddData);
    
    try {
      const manualResponse = await axios.post(`${baseURL}/api/douyin/proxies`, manualAddData);
      
      console.log('📡 手动添加代理响应:', manualResponse.data);
      
      if (manualResponse.data.code === 0) {
        console.log('✅ 手动代理添加成功:');
        console.log(`   ID: ${manualResponse.data.data.id}`);
        console.log(`   SOCKS5: ${manualResponse.data.data.sk5}`);
        console.log(`   IP: ${manualResponse.data.data.ip}`);
        console.log(`   省份: ${manualResponse.data.data.province}`);
      } else {
        console.log('❌ 手动代理添加失败:', manualResponse.data.msg);
      }
    } catch (error) {
      console.log('❌ 手动添加代理请求失败:', error.message);
      if (error.response) {
        console.log('响应数据:', error.response.data);
      }
    }
    
    // 4. 查看当前所有代理
    console.log('\n📋 4. 查看当前所有代理:');
    
    try {
      const listResponse = await axios.get(`${baseURL}/api/douyin/proxies?limit=10`);
      
      if (listResponse.data.code === 0) {
        console.log('✅ 代理列表获取成功:');
        console.log(`   总数: ${listResponse.data.data.total}`);
        console.log('   代理列表:');
        
        listResponse.data.data.proxies.forEach((proxy, index) => {
          console.log(`   ${index + 1}. ID:${proxy.id} SOCKS5:${proxy.sk5} IP:${proxy.ip} 省份:${proxy.province} 状态:${proxy.status}`);
        });
      } else {
        console.log('❌ 获取代理列表失败:', listResponse.data.msg);
      }
    } catch (error) {
      console.log('❌ 获取代理列表失败:', error.message);
    }
    
    console.log('\n🎉 自动检测IP功能测试完成！');
    
    console.log('\n📋 测试总结:');
    console.log('✅ 功能已实现:');
    console.log('   - SOCKS5代理IP自动检测API');
    console.log('   - 添加代理时自动检测IP和省份');
    console.log('   - 支持手动输入和自动检测两种模式');
    console.log('   - 前端界面支持测试检测功能');
    
    console.log('\n💡 使用说明:');
    console.log('   1. 访问代理管理页面: http://localhost:15001/page/task/douyin514CKIP.html');
    console.log('   2. 点击"添加代理"按钮');
    console.log('   3. 输入SOCKS5地址');
    console.log('   4. 勾选"自动检测IP地址"或点击"测试检测"按钮');
    console.log('   5. 系统会自动填充IP地址和省份信息');
    
    console.log('\n⚠️  注意事项:');
    console.log('   - 自动检测需要代理服务器可正常连接');
    console.log('   - 检测过程可能需要15-30秒时间');
    console.log('   - 如果检测失败，可以手动输入IP和省份');
    console.log('   - 支持带用户名密码的代理格式');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
if (require.main === module) {
  testAutoDetectIP();
}

module.exports = testAutoDetectIP;
