const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 检查当前数据状态
async function checkCurrentData() {
  console.log('🔍 检查当前数据状态...');
  
  const testUserId = '1001';
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查promotion_daily_stats表
    console.log('\n1️⃣ 检查promotion_daily_stats表...');
    const [dailyStats] = await connection.execute(
      `SELECT *, DATE(stat_date) as date_str FROM promotion_daily_stats 
       WHERE promotion_user_id = ? 
       ORDER BY stat_date DESC LIMIT 5`,
      [testUserId]
    );
    
    console.log('promotion_daily_stats数据:');
    dailyStats.forEach((stat, index) => {
      console.log(`   ${index + 1}. 日期: ${stat.date_str}, 访问: ${stat.visit_count}, 成功: ${stat.success_count}`);
    });
    
    // 2. 检查今日数据
    console.log('\n2️⃣ 检查今日数据...');
    const [todayStats] = await connection.execute(
      `SELECT *, DATE(stat_date) as date_str FROM promotion_daily_stats 
       WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    if (todayStats.length > 0) {
      console.log('✅ 找到今日数据:', todayStats[0]);
    } else {
      console.log('❌ 没有找到今日数据');
      
      // 创建今日数据
      console.log('🔧 创建今日数据...');
      await connection.execute(
        `INSERT INTO promotion_daily_stats 
         (promotion_user_id, stat_date, visit_count, unique_ip_count, 
          scan_count, success_count, fail_count, expire_count)
         VALUES (?, CURDATE(), 5, 1, 0, 5, 0, 0)`,
        [testUserId]
      );
      console.log('✅ 今日数据已创建');
    }
    
    // 3. 验证API查询
    console.log('\n3️⃣ 验证API查询...');
    const [apiResult] = await connection.execute(
      `SELECT
        visit_count,
        unique_ip_count,
        scan_count,
        success_count,
        fail_count,
        expire_count
       FROM promotion_daily_stats
       WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    if (apiResult.length > 0) {
      console.log('✅ API查询结果:', {
        visit_count: parseInt(apiResult[0].visit_count) || 0,
        unique_ip_count: parseInt(apiResult[0].unique_ip_count) || 0,
        scan_count: parseInt(apiResult[0].scan_count) || 0,
        success_count: parseInt(apiResult[0].success_count) || 0,
        fail_count: parseInt(apiResult[0].fail_count) || 0,
        expire_count: parseInt(apiResult[0].expire_count) || 0,
      });
    } else {
      console.log('❌ API查询无结果');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkCurrentData().catch(console.error);
