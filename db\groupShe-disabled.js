// MongoDB分组Schema已禁用
// 这个文件替代原来的groupShe.js，避免MongoDB依赖

console.log('🚫 MongoDB分组Schema已禁用');

// 创建一个模拟的Schema对象以保持兼容性
const mockGroupSchema = {
    // 模拟Schema属性
    groupName: { type: String, index: { unique: true } },
    groupDesc: String,
    devices: [{ type: String, ref: 'deviceMod' }],
    createTime: Date,
    
    // 模拟方法
    pre: () => {
        console.log('MongoDB Schema方法已禁用');
    },
    post: () => {
        console.log('MongoDB Schema方法已禁用');
    }
};

// 导出模拟对象
module.exports = mockGroupSchema;

console.log('💡 提示: 分组管理功能已禁用，如需分组管理请使用MySQL数据库');
