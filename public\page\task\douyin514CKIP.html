<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>SOCKS5代理IP管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .layuimini-container {
            padding: 15px;
        }

        .proxy-card {
            border: 1px solid #e6e6e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .proxy-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .proxy-ip {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            line-height: 1.4;
            max-width: 300px;
        }

        .proxy-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }

        .status-active {
            background: #e8f5e8;
            color: #52c41a;
        }

        .status-inactive {
            background: #fff2e8;
            color: #fa8c16;
        }

        .status-disabled {
            background: #ffebee;
            color: #f5222d;
        }

        .proxy-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .info-label {
            font-weight: bold;
            color: #666;
            min-width: 70px;
            flex-shrink: 0;
            font-size: 12px;
        }

        .info-value {
            color: #333;
            word-break: break-all;
            line-height: 1.4;
            flex: 1;
            min-width: 0;
        }

        .socks5-value {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .socks5-value:hover {
            background: #e8f4fd;
            border-color: #1890ff;
        }

        .socks5-display {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }

        .socks5-full {
            display: none;
            word-break: break-all;
            white-space: normal;
            line-height: 1.4;
        }

        .socks5-value.expanded .socks5-display {
            display: none;
        }

        .socks5-value.expanded .socks5-full {
            display: block;
        }

        .copy-btn {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 2px;
            padding: 2px 6px;
            font-size: 10px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .socks5-value:hover .copy-btn {
            opacity: 1;
        }

        .copy-btn:hover {
            background: #40a9ff;
        }

        .proxy-actions {
            text-align: right;
        }

        .search-bar {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .stats-bar {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 6px;
            color: white;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .location-tag {
            background: #f0f0f0;
            color: #666;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            margin-left: 5px;
        }

        .usage-count {
            background: #e6f7ff;
            color: #1890ff;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }

        .usage-count.warning {
            background: #fff7e6;
            color: #fa8c16;
        }

        .usage-count.danger {
            background: #fff2f0;
            color: #ff4d4f;
        }

        .auto-disable-warning {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 10px 0;
            font-size: 12px;
            color: #fa8c16;
        }

        .auto-disable-warning i {
            margin-right: 5px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .proxy-info {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .info-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .info-label {
                min-width: auto;
                font-size: 11px;
            }

            .socks5-value {
                font-size: 11px;
                padding: 3px 6px;
            }

            .proxy-ip {
                font-size: 12px;
                max-width: 200px;
            }
        }

        @media (max-width: 480px) {
            .proxy-card {
                margin-bottom: 10px;
            }

            .proxy-info {
                grid-template-columns: 1fr;
            }

            .socks5-display {
                max-width: 180px;
            }
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <!-- 统计信息 -->
            <div class="stats-bar">
                <div class="stat-item">
                    <span class="stat-number" id="totalProxies">0</span>
                    <span class="stat-label">总代理数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="activeProxies">0</span>
                    <span class="stat-label">正常代理</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="inactiveProxies">0</span>
                    <span class="stat-label">异常代理</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="disabledProxies">0</span>
                    <span class="stat-label">禁用代理</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="totalUsage">0</span>
                    <span class="stat-label">总使用次数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="nearLimitProxies">0</span>
                    <span class="stat-label">接近上限代理</span>
                </div>
            </div>

            <!-- 搜索和操作栏 -->
            <div class="search-bar">
                <form class="layui-form" lay-filter="searchForm">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md3">
                            <input type="text" name="searchKeyword" placeholder="搜索IP地址" class="layui-input">
                        </div>
                        <div class="layui-col-md2">
                            <select name="statusFilter">
                                <option value="">全部状态</option>
                                <option value="active">正常</option>
                                <option value="inactive">异常</option>
                                <option value="disabled">禁用</option>
                            </select>
                        </div>
                        <div class="layui-col-md2">
                            <input type="text" name="provinceFilter" placeholder="省份筛选" class="layui-input">
                        </div>
                        <div class="layui-col-md5">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search"></i> 搜索
                            </button>
                            <button type="reset" class="layui-btn layui-btn-sm layui-btn-primary">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="addProxyBtn">
                                <i class="layui-icon layui-icon-add-1"></i> 添加代理
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" id="importProxyBtn">
                                <i class="layui-icon layui-icon-upload"></i> 批量导入
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="batchDeleteBtn">
                                <i class="layui-icon layui-icon-delete"></i> 批量删除
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="testAllBtn">
                                <i class="layui-icon layui-icon-play"></i> 批量测试
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 代理列表 -->
            <div id="proxyList">
                <!-- 代理卡片将在这里动态生成 -->
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="layui-icon layui-icon-face-cry"></i>
                <div>暂无代理数据</div>
                <div style="margin-top: 10px;">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" id="addFirstProxyBtn">
                        <i class="layui-icon layui-icon-add-1"></i> 添加第一个代理
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use(['form', 'layer', 'element'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var element = layui.element;

            // 代理数据
            let proxiesData = [];
            let filteredData = [];
            let currentStats = {
                total: 0,
                active: 0,
                inactive: 0,
                disabled: 0,
                totalUsage: 0
            };

            // 初始化页面
            function init() {
                loadProxies();
                bindEvents();
            }

            // 加载代理数据
            async function loadProxies(searchParams = {}) {
                try {
                    const params = new URLSearchParams(searchParams);
                    const response = await axios.get(`/api/douyin/proxies?${params}`);

                    if (response.data.code === 0) {
                        proxiesData = response.data.data;
                        filteredData = [...proxiesData];
                        currentStats = response.data.stats;
                        updateStats();
                        renderProxyList();
                    } else {
                        layer.msg(response.data.msg || '加载代理数据失败', { icon: 2 });
                    }
                } catch (error) {
                    console.error('加载代理数据失败:', error);
                    layer.msg('加载代理数据失败', { icon: 2 });
                }
            }



            // 更新统计信息
            function updateStats() {
                document.getElementById('totalProxies').textContent = currentStats.total || 0;
                document.getElementById('activeProxies').textContent = currentStats.active || 0;
                document.getElementById('inactiveProxies').textContent = currentStats.inactive || 0;
                document.getElementById('disabledProxies').textContent = currentStats.disabled || 0;
                document.getElementById('totalUsage').textContent = currentStats.totalUsage || 0;

                // 计算接近使用上限的代理数量（使用次数>=8）
                const nearLimitCount = filteredData.filter(proxy => proxy.usage_count >= 8).length;
                document.getElementById('nearLimitProxies').textContent = nearLimitCount;
            }

            // 渲染代理列表
            function renderProxyList() {
                const container = document.getElementById('proxyList');
                const emptyState = document.getElementById('emptyState');

                if (filteredData.length === 0) {
                    container.innerHTML = '';
                    emptyState.style.display = 'block';
                    return;
                }

                emptyState.style.display = 'none';
                container.innerHTML = filteredData.map(proxy => createProxyCard(proxy)).join('');
            }

            // 创建代理卡片HTML
            function createProxyCard(proxy) {
                const statusClass = `status-${proxy.status}`;
                const statusText = {
                    'active': '正常',
                    'inactive': '异常',
                    'disabled': '禁用'
                }[proxy.status];

                // 格式化时间
                const createTime = proxy.created_at ? new Date(proxy.created_at).toLocaleString('zh-CN') : '未知';
                const lastUsed = proxy.last_used || '从未使用';

                // 使用次数样式和警告
                let usageCountClass = 'usage-count';
                let warningMessage = '';

                if (proxy.usage_count >= 10) {
                    usageCountClass += ' danger';
                    warningMessage = '<div class="auto-disable-warning"><i class="layui-icon layui-icon-close-fill"></i>此代理已达到使用上限(10次)，将被自动禁用</div>';
                } else if (proxy.usage_count >= 8) {
                    usageCountClass += ' danger';
                    warningMessage = '<div class="auto-disable-warning"><i class="layui-icon layui-icon-tips-fill"></i>警告：使用次数接近上限，还可使用 ' + (10 - proxy.usage_count) + ' 次</div>';
                } else if (proxy.usage_count >= 6) {
                    usageCountClass += ' warning';
                    warningMessage = '<div class="auto-disable-warning"><i class="layui-icon layui-icon-tips-fill"></i>提醒：使用次数较多，还可使用 ' + (10 - proxy.usage_count) + ' 次</div>';
                }

                // 处理SOCKS5地址显示
                const sk5Display = proxy.sk5.length > 30 ? proxy.sk5.substring(0, 30) + '...' : proxy.sk5;
                const sk5Id = `socks5-${proxy.id}`;

                return `
                    <div class="proxy-card" data-id="${proxy.id}">
                        <div class="proxy-header">
                            <div class="proxy-ip" title="${proxy.sk5}">${sk5Display}</div>
                            <div class="proxy-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="proxy-info">
                            <div class="info-item">
                                <span class="info-label">SOCKS5:</span>
                                <div class="info-value">
                                    <div class="socks5-value" id="${sk5Id}" onclick="toggleSocks5Display('${sk5Id}')" title="点击展开/收起完整地址">
                                        <span class="socks5-display">${sk5Display}</span>
                                        <span class="socks5-full">${proxy.sk5}</span>
                                        <button class="copy-btn" onclick="copySocks5('${proxy.sk5}', event)" title="复制地址">复制</button>
                                    </div>
                                </div>
                            </div>
                            <div class="info-item">
                                <span class="info-label">IP地址:</span>
                                <span class="info-value">${proxy.ip}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">所在地:</span>
                                <span class="info-value">${proxy.province}<span class="location-tag">${proxy.province}</span></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">使用次数:</span>
                                <span class="info-value"><span class="${usageCountClass}">${proxy.usage_count}/10</span></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">最后使用:</span>
                                <span class="info-value">${lastUsed}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">创建时间:</span>
                                <span class="info-value">${createTime}</span>
                            </div>
                        </div>
                        ${warningMessage}
                        <div class="proxy-actions">
                            <button class="layui-btn layui-btn-xs layui-btn-primary test-btn" data-id="${proxy.id}">
                                <i class="layui-icon layui-icon-play"></i> 测试
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-normal edit-btn" data-id="${proxy.id}">
                                <i class="layui-icon layui-icon-edit"></i> 编辑
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-warm view-btn" data-id="${proxy.id}">
                                <i class="layui-icon layui-icon-about"></i> 详情
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-danger delete-btn" data-id="${proxy.id}">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                            </button>
                        </div>
                    </div>
                `;
            }

            // 绑定事件
            function bindEvents() {
                // 搜索功能
                form.on('submit(search)', function (data) {
                    const formData = data.field;
                    filterProxies(formData);
                    return false;
                });

                // 添加代理按钮
                document.getElementById('addProxyBtn').addEventListener('click', function () {
                    showProxyForm();
                });

                document.getElementById('addFirstProxyBtn').addEventListener('click', function () {
                    showProxyForm();
                });

                // 批量导入按钮
                document.getElementById('importProxyBtn').addEventListener('click', function () {
                    showImportDialog();
                });

                // 批量删除按钮
                document.getElementById('batchDeleteBtn').addEventListener('click', function () {
                    layer.msg('批量删除功能开发中...', { icon: 6 });
                });

                // 批量测试按钮
                document.getElementById('testAllBtn').addEventListener('click', function () {
                    testAllProxies();
                });

                // 代理操作按钮事件委托
                document.getElementById('proxyList').addEventListener('click', function (e) {
                    const target = e.target.closest('button');
                    if (!target) return;

                    const proxyId = parseInt(target.dataset.id);

                    if (target.classList.contains('test-btn')) {
                        testProxy(proxyId);
                    } else if (target.classList.contains('edit-btn')) {
                        editProxy(proxyId);
                    } else if (target.classList.contains('view-btn')) {
                        viewProxy(proxyId);
                    } else if (target.classList.contains('delete-btn')) {
                        deleteProxy(proxyId);
                    }
                });
            }

            // 过滤代理
            function filterProxies(filters) {
                const searchParams = {};

                if (filters.searchKeyword) {
                    searchParams.search = filters.searchKeyword;
                }

                if (filters.statusFilter) {
                    searchParams.status = filters.statusFilter;
                }

                if (filters.provinceFilter) {
                    searchParams.province = filters.provinceFilter;
                }

                loadProxies(searchParams);
            }

            // 显示代理表单
            function showProxyForm(proxy = null) {
                const isEdit = !!proxy;
                const title = isEdit ? '编辑代理' : '添加代理';

                const formHtml = `
                    <form class="layui-form" lay-filter="proxyForm" style="padding: 20px;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">SK5代理数据</label>
                            <div class="layui-input-block">
                                <input type="text" name="sk5" value="${proxy?.sk5 || ''}" id="sk5Input"
                                       placeholder="请输入SK5代理数据 (如: 192.168.1.100:1080:user:pass:1735689600)" class="layui-input" lay-verify="required">
                                <div class="layui-form-mid layui-word-aux">
                                    支持格式：<br>
                                    1. 标准格式：代理服务器地址:端口:用户名:密码:过期时间<br>
                                    2. 简单格式：代理服务器地址:端口<br>
                                    3. 认证格式：用户名:密码@代理服务器地址:端口
                                </div>
                            </div>
                        </div>

                        ${!isEdit ? `
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <input type="checkbox" name="auto_detect_ip" id="autoDetectIP" title="自动检测IP地址" ${!proxy ? 'checked' : ''}>
                                <div class="layui-form-mid layui-word-aux">勾选后将自动通过SK5代理检测真实IP地址和省份，并验证过期时间</div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="testDetectionBtn">
                                    <i class="layui-icon layui-icon-search"></i> 测试检测
                                </button>
                                <span id="detectionResult" style="margin-left: 10px; font-size: 12px;"></span>
                            </div>
                        </div>
                        ` : ''}

                        <div class="layui-form-item">
                            <label class="layui-form-label">IP地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="ip" value="${proxy?.ip || ''}" id="ipInput"
                                       placeholder="IP地址（自动检测时可留空）" class="layui-input" ${isEdit ? 'lay-verify="required"' : ''}>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">所在省份</label>
                            <div class="layui-input-block">
                                <input type="text" name="province" value="${proxy?.province || ''}" id="provinceInput"
                                       placeholder="省份（自动检测时可留空）" class="layui-input" ${isEdit ? 'lay-verify="required"' : ''}>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">使用次数</label>
                            <div class="layui-input-block">
                                <input type="number" name="usage_count" value="${proxy?.usage_count || 0}"
                                       placeholder="请输入使用次数" class="layui-input" lay-verify="number">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <select name="status" lay-verify="required">
                                    <option value="">请选择状态</option>
                                    <option value="active" ${proxy?.status === 'active' ? 'selected' : ''}>正常</option>
                                    <option value="inactive" ${proxy?.status === 'inactive' ? 'selected' : ''}>异常</option>
                                    <option value="disabled" ${proxy?.status === 'disabled' ? 'selected' : ''}>禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="saveProxy">${isEdit ? '更新' : '保存'}</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                `;

                layer.open({
                    type: 1,
                    title: title,
                    content: formHtml,
                    area: ['500px', isEdit ? '550px' : '650px'],
                    success: function (layero, index) {
                        form.render();

                        // 如果是添加模式，绑定测试检测按钮事件
                        if (!isEdit) {
                            const testBtn = document.getElementById('testDetectionBtn');
                            const sk5Input = document.getElementById('sk5Input');
                            const ipInput = document.getElementById('ipInput');
                            const provinceInput = document.getElementById('provinceInput');
                            const resultSpan = document.getElementById('detectionResult');

                            if (testBtn) {
                                testBtn.addEventListener('click', async function () {
                                    const sk5Value = sk5Input.value.trim();

                                    if (!sk5Value) {
                                        layer.msg('请先输入SK5代理数据', { icon: 2 });
                                        return;
                                    }

                                    // 显示检测中状态
                                    testBtn.innerHTML = '<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 检测中...';
                                    testBtn.disabled = true;
                                    resultSpan.innerHTML = '<span style="color: #1890ff;">正在检测代理IP...</span>';

                                    try {
                                        const response = await axios.post('/api/douyin/test-proxy-detection', {
                                            sk5: sk5Value
                                        });

                                        if (response.data.code === 0) {
                                            const data = response.data.data;

                                            // 自动填充检测到的IP和省份
                                            ipInput.value = data.ip;
                                            provinceInput.value = data.province;

                                            resultSpan.innerHTML = `<span style="color: #52c41a;">✅ 检测成功: ${data.ip} (${data.province})</span>`;
                                            layer.msg('IP检测成功，已自动填充', { icon: 1 });
                                        } else {
                                            resultSpan.innerHTML = `<span style="color: #f5222d;">❌ 检测失败: ${response.data.msg}</span>`;
                                            layer.msg('IP检测失败: ' + response.data.msg, { icon: 2 });
                                        }
                                    } catch (error) {
                                        console.error('检测失败:', error);
                                        resultSpan.innerHTML = `<span style="color: #f5222d;">❌ 检测失败: ${error.message}</span>`;
                                        layer.msg('检测失败: ' + error.message, { icon: 2 });
                                    } finally {
                                        // 恢复按钮状态
                                        testBtn.innerHTML = '<i class="layui-icon layui-icon-search"></i> 测试检测';
                                        testBtn.disabled = false;
                                    }
                                });
                            }
                        }

                        // 表单提交
                        form.on('submit(saveProxy)', function (data) {
                            const formData = data.field;

                            if (isEdit) {
                                updateProxy(proxy.id, formData);
                            } else {
                                addProxy(formData);
                            }

                            layer.close(index);
                            return false;
                        });
                    }
                });
            }

            // 添加代理
            async function addProxy(formData) {
                try {
                    const response = await axios.post('/api/douyin/proxies', formData);

                    if (response.data.code === 0) {
                        layer.msg('代理添加成功', { icon: 1 });
                        loadProxies(); // 重新加载数据
                    } else {
                        layer.msg(response.data.msg || '添加代理失败', { icon: 2 });
                    }
                } catch (error) {
                    console.error('添加代理失败:', error);
                    layer.msg('添加代理失败', { icon: 2 });
                }
            }

            // 更新代理
            async function updateProxy(id, formData) {
                try {
                    const response = await axios.put(`/api/douyin/proxies/${id}`, formData);

                    if (response.data.code === 0) {
                        layer.msg('代理更新成功', { icon: 1 });
                        loadProxies(); // 重新加载数据
                    } else {
                        layer.msg(response.data.msg || '更新代理失败', { icon: 2 });
                    }
                } catch (error) {
                    console.error('更新代理失败:', error);
                    layer.msg('更新代理失败', { icon: 2 });
                }
            }

            // 编辑代理
            function editProxy(id) {
                const proxy = proxiesData.find(p => p.id === id);
                if (proxy) {
                    showProxyForm(proxy);
                }
            }

            // 查看代理详情
            function viewProxy(id) {
                const proxy = proxiesData.find(p => p.id === id);
                if (!proxy) return;

                const statusText = {
                    'active': '正常',
                    'inactive': '异常',
                    'disabled': '禁用'
                }[proxy.status];

                const createTime = proxy.created_at ? new Date(proxy.created_at).toLocaleString('zh-CN') : '未知';

                const detailHtml = `
                    <div style="padding: 20px; line-height: 2;">
                        <div style="margin-bottom: 15px;">
                            <strong>SOCKS5地址：</strong>${proxy.sk5}
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>IP地址：</strong>${proxy.ip}
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>所在省份：</strong>${proxy.province}
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>使用次数：</strong>${proxy.usage_count}
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>状态：</strong><span style="color: ${proxy.status === 'active' ? '#52c41a' : proxy.status === 'inactive' ? '#fa8c16' : '#f5222d'}">${statusText}</span>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>最后使用：</strong>${proxy.last_used}
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>创建时间：</strong>${createTime}
                        </div>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '代理详情',
                    content: detailHtml,
                    area: ['450px', '500px'],
                    btn: ['测试连接', '编辑', '关闭'],
                    yes: function (index) {
                        layer.close(index);
                        testProxy(id);
                    },
                    btn2: function (index) {
                        layer.close(index);
                        editProxy(id);
                    }
                });
            }

            // 删除代理
            async function deleteProxy(id) {
                const proxy = proxiesData.find(p => p.id === id);
                if (!proxy) return;

                layer.confirm(`确定要删除代理"${proxy.sk5}"吗？`, {
                    icon: 3,
                    title: '确认删除'
                }, async function (index) {
                    try {
                        const response = await axios.delete(`/api/douyin/proxies/${id}`);

                        if (response.data.code === 0) {
                            layer.msg('代理删除成功', { icon: 1 });
                            loadProxies(); // 重新加载数据
                        } else {
                            layer.msg(response.data.msg || '删除代理失败', { icon: 2 });
                        }
                    } catch (error) {
                        console.error('删除代理失败:', error);
                        layer.msg('删除代理失败', { icon: 2 });
                    }
                    layer.close(index);
                });
            }

            // 测试单个代理
            async function testProxy(id) {
                const proxy = proxiesData.find(p => p.id === id);
                if (!proxy) return;

                // 检查代理状态
                if (proxy.status === 'disabled') {
                    layer.msg('该代理已被禁用，无法测试', { icon: 2 });
                    return;
                }

                // 检查使用次数
                if (proxy.usage_count >= 10) {
                    layer.confirm('该代理使用次数已达到上限(10次)，测试成功后将被自动禁用，是否继续？', {
                        icon: 3,
                        title: '使用次数警告'
                    }, function (index) {
                        layer.close(index);
                        performTest();
                    });
                    return;
                } else if (proxy.usage_count >= 8) {
                    layer.confirm(`该代理使用次数较多(${proxy.usage_count}/10)，测试成功后还可使用${10 - proxy.usage_count - 1}次，是否继续？`, {
                        icon: 3,
                        title: '使用次数提醒'
                    }, function (index) {
                        layer.close(index);
                        performTest();
                    });
                    return;
                }

                performTest();

                async function performTest() {
                    layer.msg('正在测试代理连接...', { icon: 16, time: 0 });

                    try {
                        const response = await axios.post(`/api/douyin/proxies/${id}/test`);

                        layer.closeAll('msg');

                        if (response.data.code === 0) {
                            let message = response.data.msg;

                            // 检查是否因为使用次数达到上限而被禁用
                            if (response.data.data.success && proxy.usage_count + 1 >= 10) {
                                message += '，该代理已达到使用上限并被自动禁用';
                            }

                            layer.msg(message, { icon: response.data.data.success ? 1 : 2 });
                            loadProxies(); // 重新加载数据以更新状态
                        } else {
                            layer.msg(response.data.msg || '测试失败', { icon: 2 });
                        }
                    } catch (error) {
                        layer.closeAll('msg');
                        console.error('测试代理失败:', error);
                        layer.msg('测试代理失败', { icon: 2 });
                    }
                }
            }

            // 批量测试代理
            async function testAllProxies() {
                const activeProxies = proxiesData.filter(p => p.status !== 'disabled');
                if (activeProxies.length === 0) {
                    layer.msg('没有可测试的代理', { icon: 2 });
                    return;
                }

                layer.confirm(`确定要测试 ${activeProxies.length} 个代理吗？`, {
                    icon: 3,
                    title: '批量测试'
                }, async function (index) {
                    layer.close(index);

                    layer.msg(`开始批量测试 ${activeProxies.length} 个代理...`, { icon: 16, time: 0 });

                    try {
                        const response = await axios.post('/api/douyin/proxies/batch-test', {
                            ids: activeProxies.map(p => p.id)
                        });

                        layer.closeAll('msg');

                        if (response.data.code === 0) {
                            layer.msg(response.data.msg, { icon: 1 });
                            loadProxies(); // 重新加载数据以更新状态
                        } else {
                            layer.msg(response.data.msg || '批量测试失败', { icon: 2 });
                        }
                    } catch (error) {
                        layer.closeAll('msg');
                        console.error('批量测试失败:', error);
                        layer.msg('批量测试失败', { icon: 2 });
                    }
                });
            }

            // 显示批量导入对话框
            function showImportDialog() {
                const importHtml = `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <h4>批量导入代理</h4>
                            <p style="color: #666; font-size: 12px;">
                                请按以下格式输入代理信息，每行一个：<br>
                                <code>SOCKS5地址,IP地址,省份,使用次数,状态</code><br>
                                例如：<code>127.0.0.1:1080,127.0.0.1,北京市,10,active</code>
                            </p>
                        </div>
                        <textarea id="importData" placeholder="请输入代理数据..." style="width: 100%; height: 200px; border: 1px solid #ddd; padding: 10px; resize: vertical;"></textarea>
                        <div style="margin-top: 15px;">
                            <button type="button" class="layui-btn layui-btn-sm" onclick="processImport()">
                                <i class="layui-icon layui-icon-upload"></i> 开始导入
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="fillSampleData()">
                                <i class="layui-icon layui-icon-template-1"></i> 填入示例数据
                            </button>
                        </div>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '批量导入代理',
                    content: importHtml,
                    area: ['600px', '450px'],
                    success: function () {
                        // 填入示例数据函数
                        window.fillSampleData = function () {
                            document.getElementById('importData').value =
                                '192.168.1.10:1080,192.168.1.10,北京市,5,active\n' +
                                '192.168.1.11:1080,192.168.1.11,上海市,3,active\n' +
                                '192.168.1.12:1080,192.168.1.12,广东省,8,inactive';
                        };

                        // 处理导入函数
                        window.processImport = async function () {
                            const data = document.getElementById('importData').value.trim();
                            if (!data) {
                                layer.msg('请输入代理数据', { icon: 2 });
                                return;
                            }

                            const lines = data.split('\n').filter(line => line.trim());
                            const proxies = [];

                            lines.forEach((line, index) => {
                                const parts = line.split(',').map(part => part.trim());

                                if (parts.length >= 3) {
                                    const newProxy = {
                                        sk5: parts[0] || '',
                                        ip: parts[1] || '',
                                        province: parts[2] || '',
                                        usage_count: parseInt(parts[3]) || 0,
                                        status: parts[4] || 'active'
                                    };

                                    // 验证必填字段
                                    if (newProxy.sk5 && newProxy.ip && newProxy.province) {
                                        proxies.push(newProxy);
                                    }
                                }
                            });

                            if (proxies.length === 0) {
                                layer.msg('没有有效的代理数据', { icon: 2 });
                                return;
                            }

                            try {
                                const response = await axios.post('/api/douyin/proxies/batch-import', {
                                    proxies: proxies
                                });

                                layer.closeAll();

                                if (response.data.code === 0) {
                                    const { successCount, errorCount, errors } = response.data.data;

                                    if (errorCount > 0) {
                                        layer.alert(`导入完成！成功: ${successCount}, 失败: ${errorCount}<br>错误详情：<br>${errors.slice(0, 5).join('<br>')}`, {
                                            icon: 3,
                                            title: '导入结果'
                                        });
                                    } else {
                                        layer.msg(`导入成功！共导入 ${successCount} 个代理`, { icon: 1 });
                                    }

                                    loadProxies(); // 重新加载数据
                                } else {
                                    layer.msg(response.data.msg || '批量导入失败', { icon: 2 });
                                }
                            } catch (error) {
                                layer.closeAll();
                                console.error('批量导入失败:', error);
                                layer.msg('批量导入失败', { icon: 2 });
                            }
                        };
                    }
                });
            }

            // SOCKS5地址显示切换功能
            window.toggleSocks5Display = function (elementId) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.classList.toggle('expanded');
                }
            };

            // 复制SOCKS5地址功能
            window.copySocks5 = function (text, event) {
                event.stopPropagation(); // 阻止事件冒泡

                // 创建临时文本区域
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();

                try {
                    document.execCommand('copy');
                    layer.msg('SOCKS5地址已复制到剪贴板', { icon: 1, time: 1500 });
                } catch (err) {
                    console.error('复制失败:', err);
                    layer.msg('复制失败，请手动复制', { icon: 2 });
                }

                document.body.removeChild(textarea);
            };

            init();
        });
    </script>
</body>

</html>