const axios = require('axios');

// 简单测试API
async function testSimpleAPI() {
  console.log('🧪 简单测试API...');
  
  const baseURL = 'http://localhost:15001';
  
  try {
    // 测试简单数据
    const testData = {
      promotion_user_id: '1001',
      action_type: 'scan',
      ip_address: '*************',
      ip_province: '北京市',
      douyin_name: '测试用户',
      douyin_id: 'test123',
      ck_data: 'sessionid=test123',
      user_agent: 'Mozilla/5.0 (Test Browser)',
      extra_data: {
        session_id: "session_test_" + Date.now(),
        test: true,
        timestamp: new Date().toISOString()
      }
    };
    
    console.log('📤 发送测试数据:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post(`${baseURL}/api/promotion-actions/save`, testData);
    
    console.log('📥 API响应:', JSON.stringify(response.data, null, 2));
    
    if (response.data.code === 0) {
      console.log('✅ API调用成功');
    } else {
      console.log('❌ API调用失败:', response.data.msg);
    }
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 请确保服务器正在运行: node bin/ceshi');
    }
  }
}

// 运行测试
testSimpleAPI();
