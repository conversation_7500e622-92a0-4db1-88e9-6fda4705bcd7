const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

async function testUserCkcountAPI() {
    console.log(colors.bold('🧪 用户ckcount API测试'));
    console.log('='.repeat(70));
    console.log(colors.cyan('目的: 测试用户ckcount增加和查询功能\n'));

    try {
        // 1. 创建测试用户（如果不存在）
        console.log(colors.blue('📝 步骤1: 准备测试用户'));
        
        const testUsers = ['testuser1', 'testuser2', 'testuser3'];
        
        for (const username of testUsers) {
            try {
                // 尝试创建测试用户（这里假设有创建用户的接口，如果没有需要手动在数据库中创建）
                console.log(`   准备用户: ${username}`);
            } catch (error) {
                console.log(`   用户 ${username} 可能已存在`);
            }
        }

        // 2. 测试获取用户ckcount（初始状态）
        console.log(colors.blue('\n📊 步骤2: 获取用户初始ckcount'));
        
        const initialResponse = await axios.get(`${BASE_URL}/get-user-ckcount?username=testuser1`);
        console.log('初始ckcount查询响应:');
        console.log(JSON.stringify(initialResponse.data, null, 2));
        
        let initialCkcount = 0;
        if (initialResponse.data.code === 0) {
            initialCkcount = initialResponse.data.data.ckcount;
            console.log(colors.green(`✅ 用户testuser1初始ckcount: ${initialCkcount}`));
        } else {
            console.log(colors.yellow(`⚠️  用户testuser1不存在或查询失败: ${initialResponse.data.msg}`));
        }

        // 3. 测试增加单个用户ckcount
        console.log(colors.blue('\n➕ 步骤3: 增加单个用户ckcount'));
        
        const increaseData = {
            username: 'testuser1'
        };
        
        console.log('发送增加请求:', JSON.stringify(increaseData, null, 2));
        
        const increaseResponse = await axios.post(`${BASE_URL}/increase-user-ckcount`, increaseData);
        console.log('增加ckcount响应:');
        console.log(JSON.stringify(increaseResponse.data, null, 2));
        
        if (increaseResponse.data.code === 0) {
            const data = increaseResponse.data.data;
            console.log(colors.green('✅ ckcount增加成功:'));
            console.log(`   用户名: ${data.username}`);
            console.log(`   之前次数: ${data.previous_ckcount}`);
            console.log(`   当前次数: ${data.current_ckcount}`);
            console.log(`   增加数量: ${data.increased_by}`);
        } else {
            console.log(colors.red(`❌ ckcount增加失败: ${increaseResponse.data.msg}`));
        }

        // 4. 验证ckcount是否正确增加
        console.log(colors.blue('\n🔍 步骤4: 验证ckcount是否正确增加'));
        
        const verifyResponse = await axios.get(`${BASE_URL}/get-user-ckcount?username=testuser1`);
        
        if (verifyResponse.data.code === 0) {
            const currentCkcount = verifyResponse.data.data.ckcount;
            console.log(colors.green(`✅ 验证结果: 当前ckcount = ${currentCkcount}`));
            
            if (increaseResponse.data.code === 0) {
                const expectedCount = increaseResponse.data.data.current_ckcount;
                if (currentCkcount === expectedCount) {
                    console.log(colors.green('✅ 验证通过: ckcount正确增加'));
                } else {
                    console.log(colors.red(`❌ 验证失败: 期望${expectedCount}，实际${currentCkcount}`));
                }
            }
        }

        // 5. 测试多次增加
        console.log(colors.blue('\n🔄 步骤5: 测试多次增加ckcount'));
        
        for (let i = 1; i <= 3; i++) {
            console.log(colors.cyan(`\n--- 第${i}次增加 ---`));
            
            const multiResponse = await axios.post(`${BASE_URL}/increase-user-ckcount`, {
                username: 'testuser1'
            });
            
            if (multiResponse.data.code === 0) {
                const data = multiResponse.data.data;
                console.log(`   第${i}次: ${data.previous_ckcount} -> ${data.current_ckcount}`);
            } else {
                console.log(colors.red(`   第${i}次失败: ${multiResponse.data.msg}`));
            }
            
            // 短暂延迟
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        // 6. 测试批量增加ckcount
        console.log(colors.blue('\n📦 步骤6: 测试批量增加ckcount'));
        
        const batchData = {
            usernames: ['testuser1', 'testuser2', 'testuser3', 'nonexistentuser'],
            increment: 2
        };
        
        console.log('批量增加请求:', JSON.stringify(batchData, null, 2));
        
        const batchResponse = await axios.post(`${BASE_URL}/batch-increase-ckcount`, batchData);
        console.log('批量增加响应:');
        console.log(JSON.stringify(batchResponse.data, null, 2));
        
        if (batchResponse.data.code === 0) {
            const data = batchResponse.data.data;
            console.log(colors.green(`✅ 批量处理完成: 成功${data.success_count}个，失败${data.fail_count}个`));
            
            console.log(colors.cyan('\n📋 详细结果:'));
            data.results.forEach((result, index) => {
                if (result.success) {
                    console.log(colors.green(`   ${index + 1}. ${result.username}: ${result.previous_ckcount} -> ${result.current_ckcount} ✅`));
                } else {
                    console.log(colors.red(`   ${index + 1}. ${result.username}: ${result.error} ❌`));
                }
            });
        }

        // 7. 测试错误情况
        console.log(colors.blue('\n❌ 步骤7: 测试错误情况'));
        
        // 测试空用户名
        console.log(colors.cyan('\n--- 测试空用户名 ---'));
        try {
            const emptyResponse = await axios.post(`${BASE_URL}/increase-user-ckcount`, {
                username: ''
            });
            console.log('空用户名响应:', JSON.stringify(emptyResponse.data, null, 2));
        } catch (error) {
            console.log('空用户名请求失败:', error.response?.data || error.message);
        }
        
        // 测试不存在的用户
        console.log(colors.cyan('\n--- 测试不存在的用户 ---'));
        try {
            const notExistResponse = await axios.post(`${BASE_URL}/increase-user-ckcount`, {
                username: 'definitely_not_exist_user_12345'
            });
            console.log('不存在用户响应:', JSON.stringify(notExistResponse.data, null, 2));
        } catch (error) {
            console.log('不存在用户请求失败:', error.response?.data || error.message);
        }

        // 8. 最终状态查询
        console.log(colors.blue('\n📊 步骤8: 查询最终状态'));
        
        for (const username of ['testuser1', 'testuser2', 'testuser3']) {
            try {
                const finalResponse = await axios.get(`${BASE_URL}/get-user-ckcount?username=${username}`);
                if (finalResponse.data.code === 0) {
                    const ckcount = finalResponse.data.data.ckcount;
                    console.log(colors.green(`   ${username}: ckcount = ${ckcount}`));
                } else {
                    console.log(colors.yellow(`   ${username}: ${finalResponse.data.msg}`));
                }
            } catch (error) {
                console.log(colors.red(`   ${username}: 查询失败`));
            }
        }

        console.log(colors.bold('\n🎉 用户ckcount API测试完成!'));
        console.log('='.repeat(70));
        
        // 总结
        console.log(colors.bold('\n📋 API功能总结:'));
        console.log('✅ 单个用户ckcount增加');
        console.log('✅ 用户ckcount查询');
        console.log('✅ 批量用户ckcount增加');
        console.log('✅ 错误处理和验证');
        console.log('✅ 数据一致性保证');

    } catch (error) {
        console.error(colors.red('❌ 测试过程中发生错误:'));
        console.error('错误信息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
console.log(colors.bold('🚀 用户ckcount API测试脚本'));
console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
console.log('\n' + colors.yellow('⏳ 3秒后开始测试...'));

setTimeout(() => {
    testUserCkcountAPI().catch(error => {
        console.error(colors.red('💥 测试脚本执行失败:'), error.message);
        process.exit(1);
    });
}, 3000);
