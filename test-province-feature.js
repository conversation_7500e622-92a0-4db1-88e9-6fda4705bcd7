const mysql = require('mysql2/promise');
const { getProvinceByIP, batchGetProvinceByIP, isValidIP } = require('./utils/ipLocation');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4"
};

async function testProvinceFeature() {
  console.log('🧪 测试IP省份功能...');
  
  try {
    // 1. 测试IP省份解析工具
    console.log('\n📍 1. 测试IP省份解析工具:');
    
    const testIPs = [
      '*************',
      '*******',
      '**************',
      '*************',
      '************',
      '************',
      '**************',
      '************',
      '************',
      'invalid-ip'
    ];
    
    console.log('✅ IP省份解析结果:');
    testIPs.forEach(ip => {
      const province = getProvinceByIP(ip);
      const isValid = isValidIP(ip);
      console.log(`   ${ip} -> ${province} (有效: ${isValid})`);
    });
    
    // 2. 测试批量解析
    console.log('\n📊 2. 测试批量IP省份解析:');
    const batchResult = batchGetProvinceByIP(testIPs.slice(0, 5));
    console.log('✅ 批量解析结果:');
    batchResult.forEach(result => {
      console.log(`   ${result.ip} -> ${result.province}`);
    });
    
    // 3. 检查数据库表结构
    console.log('\n🔍 3. 检查数据库表结构:');
    const connection = await mysql.createConnection(dbConfig);
    
    const [actionsColumns] = await connection.execute('DESCRIBE promotion_actions');
    console.log('✅ promotion_actions表字段:');
    actionsColumns.forEach(col => {
      if (col.Field.includes('ip') || col.Field.includes('province')) {
        console.log(`   ${col.Field} (${col.Type})`);
      }
    });
    
    const [visitsColumns] = await connection.execute('DESCRIBE promotion_visits');
    console.log('\n✅ promotion_visits表字段:');
    visitsColumns.forEach(col => {
      if (col.Field.includes('ip') || col.Field.includes('province')) {
        console.log(`   ${col.Field} (${col.Type})`);
      }
    });
    
    // 4. 检查现有数据的省份信息
    console.log('\n📋 4. 检查现有数据的省份信息:');
    
    const [actionsData] = await connection.execute(
      `SELECT promotion_user_id, ip_address, ip_province, action_type, 
              DATE_FORMAT(action_time, '%Y-%m-%d %H:%i:%s') as action_time
       FROM promotion_actions 
       ORDER BY action_time DESC 
       LIMIT 10`
    );
    
    console.log('✅ promotion_actions数据 (最近10条):');
    actionsData.forEach((action, index) => {
      console.log(`${index + 1}. 推广用户${action.promotion_user_id} - ${action.action_type}`);
      console.log(`   IP: ${action.ip_address}, 省份: ${action.ip_province || '未知'}`);
      console.log(`   时间: ${action.action_time}`);
    });
    
    const [visitsData] = await connection.execute(
      `SELECT promotion_user_id, visitor_ip, ip_province,
              DATE_FORMAT(visit_time, '%Y-%m-%d %H:%i:%s') as visit_time
       FROM promotion_visits 
       ORDER BY visit_time DESC 
       LIMIT 5`
    );
    
    console.log('\n✅ promotion_visits数据 (最近5条):');
    visitsData.forEach((visit, index) => {
      console.log(`${index + 1}. 推广用户${visit.promotion_user_id}`);
      console.log(`   IP: ${visit.visitor_ip}, 省份: ${visit.ip_province || '未知'}`);
      console.log(`   时间: ${visit.visit_time}`);
    });
    
    // 5. 测试新增操作记录（模拟API调用）
    console.log('\n➕ 5. 测试新增操作记录:');
    
    const testAction = {
      promotion_user_id: '1001',
      target_user_id: 'test_user_001',
      action_type: 'scan',
      ip_address: '**************',
      douyin_name: null,
      douyin_id: null,
      ck_data: null,
      user_agent: 'Mozilla/5.0 Test'
    };
    
    // 自动解析省份
    const ip_province = getProvinceByIP(testAction.ip_address);
    console.log(`✅ 自动解析省份: ${testAction.ip_address} -> ${ip_province}`);
    
    // 插入测试数据
    await connection.execute(
      `INSERT INTO promotion_actions 
       (promotion_user_id, target_user_id, action_type, ip_address, ip_province, 
        douyin_name, douyin_id, ck_data, user_agent) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [testAction.promotion_user_id, testAction.target_user_id, testAction.action_type, 
       testAction.ip_address, ip_province, testAction.douyin_name, testAction.douyin_id, 
       testAction.ck_data, testAction.user_agent]
    );
    
    console.log('✅ 测试操作记录插入成功');
    
    // 6. 验证API返回格式
    console.log('\n📡 6. 验证API返回格式:');
    
    const [newAction] = await connection.execute(
      `SELECT 
        pa.promotion_user_id,
        pa.target_user_id as user_id,
        pa.action_type,
        pa.ip_address,
        pa.ip_province,
        pa.douyin_name,
        pa.douyin_id,
        pa.ck_data,
        DATE_FORMAT(pa.action_time, '%H:%i:%s') as action_time,
        u.username as promoter_name
       FROM promotion_actions pa
       LEFT JOIN user u ON pa.promotion_user_id = u.promoter_id AND u.user_type = 'promoter'
       ORDER BY pa.action_time DESC 
       LIMIT 1`
    );
    
    if (newAction.length > 0) {
      const action = newAction[0];
      const apiResponse = {
        promotion_user_id: action.promotion_user_id,
        promoter_name: action.promoter_name,
        user_id: action.user_id || "-",
        time: action.action_time,
        ip: action.ip_address,
        ip_province: action.ip_province || "-",
        status: action.action_type,
        douyin_name: action.douyin_name || "-",
        douyin_id: action.douyin_id || "-",
        ck: action.ck_data || "-"
      };
      
      console.log('✅ API返回格式示例:');
      console.log(JSON.stringify(apiResponse, null, 2));
    }
    
    await connection.end();
    
    console.log('\n🎉 IP省份功能测试完成！');
    console.log('\n📋 功能总结:');
    console.log('✅ IP省份解析工具正常工作');
    console.log('✅ 数据库表结构包含省份字段');
    console.log('✅ 现有数据已更新省份信息');
    console.log('✅ 新增数据自动解析省份');
    console.log('✅ API返回格式包含省份信息');
    
    console.log('\n🔗 修改内容:');
    console.log('- promotion_actions表添加ip_province字段');
    console.log('- promotion_visits表添加ip_province字段');
    console.log('- API接口返回省份信息');
    console.log('- 前端界面显示省份列');
    console.log('- 自动IP省份解析工具');
    console.log('- 新增操作记录API');
    
    console.log('\n🌐 现在可以测试:');
    console.log('- 推广用户管理: http://localhost:15001/page/promotion/admin-promotion-simple.html');
    console.log('- 查看操作记录中的省份信息');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    process.exit(1);
  }
}

testProvinceFeature();
