<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; border-radius: 3px; margin: 10px 0; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 推广用户API调试测试</h1>
    
    <div class="test-section">
        <h3>步骤1: 登录测试</h3>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>步骤2: 用户信息测试</h3>
        <button onclick="testUserInfo()">测试用户信息</button>
        <div id="userInfoResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>步骤3: 统计数据测试</h3>
        <button onclick="testStats()">测试统计数据</button>
        <div id="statsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>步骤4: 操作记录测试</h3>
        <button onclick="testActions()">测试操作记录</button>
        <div id="actionsResult" class="result"></div>
    </div>

    <script>
        // 测试登录
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = '正在测试登录...';
            
            try {
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: '1001', password: '123456' })
                });
                
                const data = await response.json();
                
                resultDiv.textContent = `登录测试结果:
状态码: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}
当前cookies: ${document.cookie}`;
                
                if (data.code === 0) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `登录测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试用户信息
        async function testUserInfo() {
            const resultDiv = document.getElementById('userInfoResult');
            resultDiv.textContent = '正在测试用户信息...';
            
            try {
                const response = await fetch('/api/promotion/promoter/user-info', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.textContent = `用户信息测试结果:
状态码: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}`;
                
                if (data.code === 0) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `用户信息测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试统计数据
        async function testStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.textContent = '正在测试统计数据...';
            
            try {
                const response = await fetch('/api/promotion/promoter/today-stats', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.textContent = `统计数据测试结果:
状态码: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}`;
                
                if (data.code === 0) {
                    const stats = data.data;
                    const hasData = Object.values(stats).some(val => val > 0);
                    
                    resultDiv.textContent += `

数据分析:
访问次数: ${stats.visit_count}
独立IP数: ${stats.unique_ip_count}
成功数量: ${stats.success_count}
是否有非零数据: ${hasData}`;
                    
                    if (hasData) {
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent += '\n\n⚠️ 所有数据都为0！';
                    }
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `统计数据测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试操作记录
        async function testActions() {
            const resultDiv = document.getElementById('actionsResult');
            resultDiv.textContent = '正在测试操作记录...';
            
            try {
                const response = await fetch('/api/promotion/promoter/today-actions?page=1&limit=5', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.textContent = `操作记录测试结果:
状态码: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}`;
                
                if (data.code === 0) {
                    const actions = data.data;
                    
                    resultDiv.textContent += `

数据分析:
记录总数: ${actions.total}
当前页记录数: ${actions.list.length}`;
                    
                    if (actions.list.length > 0) {
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent += '\n\n⚠️ 没有操作记录！';
                    }
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `操作记录测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 页面加载时显示初始状态
        window.onload = function() {
            console.log('📋 API调试测试页面已加载');
            console.log('当前cookies:', document.cookie);
        };
    </script>
</body>
</html>
