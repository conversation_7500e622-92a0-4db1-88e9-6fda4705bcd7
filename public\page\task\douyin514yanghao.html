<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui优化版</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .image-preview {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                
                <fieldset>
                    <legend>【养号任务设置】</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">刷视频时间</label>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="number" id="minTime" name="minTime" placeholder="最小" class="layui-input">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="number" id="maxTime" name="maxTime" placeholder="最大" class="layui-input">
                        </div>
                        <div class="layui-form-mid">秒/视频</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">总任务时间</label>
                        <div class="layui-input-inline" style="width: 120px;">
                            <input type="number" id="totalTime" name="totalTime" placeholder="分钟" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">操作概率</label>
                        <div class="layui-input-inline" style="width: 120px;">
                            <input type="number" id="probability" name="probability" placeholder="1-100" class="layui-input">
                        </div>
                        <div class="layui-form-mid">%</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-block">
                            <textarea id="keyword" name="keyword" placeholder="多个关键词请用换行分隔" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn layui-btn-sm" value="养号" lay-submit="" lay-filter="tijiao">执行任务</button>
                            <button class="layui-btn layui-btn-sm layui-btn-danger" lay-submit="" lay-filter="stopTask">停止任务</button>
                        </div>
                    </div>
                </fieldset>
            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });

        // 任务执行按钮（标准化数据结构）
        document.querySelector(".layui-btn[lay-filter='tijiao']").addEventListener("click", function (event) {
            event.preventDefault();

            // 收集参数
            const formData = {
                minTime: document.getElementById("minTime").value.trim(),
                maxTime: document.getElementById("maxTime").value.trim(),
                totalTime: document.getElementById("totalTime").value.trim(),
                probability: document.getElementById("probability").value.trim(),
                keywords: document.getElementById("keyword").value.trim()
            };

            // 参数验证
            if (!formData.minTime || !formData.maxTime || !formData.totalTime || !formData.probability) {
                return layui.layer.msg('请填写所有必填参数');
            }
            if (parseInt(formData.minTime) <= 0 || parseInt(formData.maxTime) <= 0) {
                return layui.layer.msg('时间必须大于0');
            }
            if (parseInt(formData.minTime) >= parseInt(formData.maxTime)) {
                return layui.layer.msg('最小时间必须小于最大时间');
            }
            if (parseInt(formData.probability) < 1 || parseInt(formData.probability) > 100) {
                return layui.layer.msg('概率必须在1-100之间');
            }

            // 处理关键词为数组格式
            const keywordsArray = formData.keywords.split('\n')
                .filter(k => k.trim() !== '')
                .map(k => k.trim());
            
            // 构建任务数据
            const taskData = {
                type: "deviceTask",
                serverData: {
                    taskName: "养号",
                    taskData: {
                        minTime: formData.minTime,
                        maxTime: formData.maxTime,
                        totalTime: formData.totalTime,
                        probability: formData.probability,
                        keywords: keywordsArray  // 使用数组格式
                    },
                    timeStamp: Date.now()
                }
            };

            // 发送任务
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(taskData));
                layui.layer.msg('任务已发送');
            } else {
                layui.layer.msg('连接异常，正在重连...');
                connectWebSocket();
                setTimeout(() => {
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify(taskData));
                    }
                }, 1000);
            }
        });

        // 停止任务按钮
        document.querySelector(".layui-btn[lay-filter='stopTask']").addEventListener("click", function () {
            layui.layer.confirm('确定要停止当前任务吗？', { icon: 3, title: '提示' }, function (index) {
                // 这里添加停止任务的逻辑
                layui.layer.msg('任务已停止', { icon: 5 });
                layer.close(index);
            });
        });
    </script>
</body>

</html>