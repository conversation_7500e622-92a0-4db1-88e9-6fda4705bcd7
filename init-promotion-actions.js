const mysql = require("mysql2/promise");
const fs = require("fs");
const path = require("path");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
  multipleStatements: true,
};

async function initPromotionActions() {
  console.log("🚀 开始初始化推广操作记录表...");

  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log("✅ 数据库连接成功");

    // 读取SQL文件
    const sqlFile = path.join(__dirname, "db", "promotion-simple.sql");
    const sqlContent = fs.readFileSync(sqlFile, "utf8");

    // 分割SQL语句
    const statements = sqlContent.split(";").filter((stmt) => stmt.trim());

    console.log(`📋 准备执行 ${statements.length} 条SQL语句...`);

    // 执行SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          await connection.execute(statement);
          console.log(
            `✅ 执行成功 (${i + 1}/${statements.length}): ${statement.substring(
              0,
              50
            )}...`
          );
        } catch (error) {
          if (
            error.code === "ER_TABLE_EXISTS_ERROR" ||
            error.code === "ER_DUP_ENTRY"
          ) {
            console.log(
              `⚠️  跳过已存在 (${i + 1}/${
                statements.length
              }): ${statement.substring(0, 50)}...`
            );
          } else {
            console.error(
              `❌ 执行失败 (${i + 1}/${statements.length}):`,
              error.message
            );
          }
        }
      }
    }

    // 验证表是否创建成功
    console.log("\n🔍 验证数据库表...");

    const tables = ["promotion_actions", "promotion_visits"];

    for (const tableName of tables) {
      try {
        const [rows] = await connection.execute(
          `SHOW TABLES LIKE '${tableName}'`
        );
        if (rows.length > 0) {
          console.log(`✅ 表 ${tableName} 存在`);

          // 显示表中的数据量
          const [count] = await connection.execute(
            `SELECT COUNT(*) as count FROM ${tableName}`
          );
          console.log(`   数据量: ${count[0].count} 条记录`);
        } else {
          console.log(`❌ 表 ${tableName} 不存在`);
        }
      } catch (error) {
        console.error(`❌ 检查表 ${tableName} 失败:`, error.message);
      }
    }

    // 检查推广用户统计数据
    console.log("\n📊 检查推广用户统计数据...");

    const [userStats] = await connection.execute(
      `SELECT 
        promoter_id,
        username,
        visit_count,
        unique_ip_count,
        scan_count,
        success_count,
        fail_count,
        expire_count,
        last_stat_update
       FROM user 
       WHERE user_type = 'promoter' 
       ORDER BY promoter_id`
    );

    console.log(`✅ 推广用户统计数据 (${userStats.length} 个用户):`);
    userStats.forEach((user) => {
      console.log(`   ${user.promoter_id} - ${user.username}:`);
      console.log(
        `     访问: ${user.visit_count}, 独立IP: ${user.unique_ip_count}, 扫码: ${user.scan_count}`
      );
      console.log(
        `     成功: ${user.success_count}, 失败: ${user.fail_count}, 过期: ${user.expire_count}`
      );
      console.log(`     更新时间: ${user.last_stat_update}`);
    });

    // 检查操作记录
    console.log("\n📋 检查操作记录...");

    const [actionRecords] = await connection.execute(
      `SELECT
        promotion_user_id,
        action_type,
        ip_address,
        ip_province,
        douyin_name,
        douyin_id,
        DATE_FORMAT(action_time, '%Y-%m-%d %H:%i:%s') as action_time
       FROM promotion_actions
       ORDER BY action_time DESC
       LIMIT 10`
    );

    console.log(`✅ 最近的操作记录 (显示前10条):`);
    actionRecords.forEach((record, index) => {
      console.log(
        `${index + 1}. 推广用户${record.promotion_user_id} - ${
          record.action_type
        }`
      );
      console.log(`   IP: ${record.ip_address} (${record.ip_province || "-"})`);
      console.log(
        `   抖音信息: ${record.douyin_name || "-"} (${record.douyin_id || "-"})`
      );
      console.log(`   时间: ${record.action_time}`);
    });

    await connection.end();

    console.log("\n🎉 推广操作记录表初始化完成！");
    console.log("\n📋 创建的表:");
    console.log("- promotion_actions: 推广操作记录表");
    console.log("- promotion_visits: 推广访问记录表");

    console.log("\n🔗 现在可以测试以下功能:");
    console.log(
      "- 推广用户管理: http://localhost:15001/page/promotion/admin-promotion-simple.html"
    );
    console.log("- 查看详细的推广统计数据和操作记录");
  } catch (error) {
    console.error("💥 初始化失败:", error.message);
    process.exit(1);
  }
}

initPromotionActions();
