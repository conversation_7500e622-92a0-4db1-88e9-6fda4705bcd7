<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>干净测试登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            width: 100%;
            padding: 12px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 干净测试登录</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            无任何外部依赖的纯净登录测试
        </p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="user_id">用户ID:</label>
                <input type="text" id="user_id" name="user_id" value="1001" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="123456" required>
            </div>
            
            <button type="submit" id="loginBtn">登录</button>
        </form>
        
        <div class="log" id="log">等待操作...\n</div>
    </div>

    <script>
        // 完全不依赖任何外部库的纯净JavaScript
        
        const log = document.getElementById('log');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            
            log.textContent += logMessage;
            log.scrollTop = log.scrollHeight;
            
            // 同时输出到控制台
            console.log(`${prefix} ${message}`);
        }
        
        // 监听所有网络请求（如果有外部请求会被捕获）
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            addLog(`网络请求: ${url}`, 'info');
            
            // 检查是否是外部请求
            if (typeof url === 'string' && !url.startsWith('/') && !url.startsWith('http://localhost')) {
                addLog(`⚠️ 检测到外部请求: ${url}`, 'warning');
            }
            
            return originalFetch.apply(this, args);
        };
        
        // 监听页面加载
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面DOM加载完成', 'success');
            addLog(`当前URL: ${window.location.href}`, 'info');
            addLog(`User Agent: ${navigator.userAgent}`, 'info');
            addLog(`当前cookies: ${document.cookie || '无'}`, 'info');
        });
        
        // 监听表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            addLog('开始登录流程', 'info');
            
            const formData = new FormData(this);
            const loginData = {
                user_id: formData.get('user_id').trim(),
                password: formData.get('password').trim()
            };
            
            addLog(`登录数据: 用户ID=${loginData.user_id}`, 'info');
            
            if (!loginData.user_id || !loginData.password) {
                addLog('用户ID或密码为空', 'error');
                return;
            }
            
            // 禁用按钮
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                addLog('发送登录请求...', 'info');
                
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                
                addLog(`收到响应: 状态码=${response.status}`, 'info');
                
                const result = await response.json();
                addLog(`响应数据: ${JSON.stringify(result)}`, 'info');
                addLog(`更新后的cookies: ${document.cookie}`, 'info');
                
                if (result.code === 0) {
                    addLog('登录成功！', 'success');
                    addLog(`用户信息: ${result.data.username} (ID: ${result.data.user_id})`, 'success');
                    
                    addLog('准备跳转到仪表板...', 'info');
                    addLog('执行跳转: window.location.href = "/promoter-dashboard"', 'info');
                    
                    // 延迟2秒跳转，让用户看到日志
                    setTimeout(() => {
                        addLog('执行跳转...', 'info');
                        window.location.href = '/promoter-dashboard';
                    }, 2000);
                    
                } else {
                    addLog(`登录失败: ${result.msg}`, 'error');
                }
                
            } catch (error) {
                addLog(`请求失败: ${error.message}`, 'error');
                addLog(`错误详情: ${error.stack}`, 'error');
            } finally {
                // 恢复按钮
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
        
        // 监听页面卸载（检测是否有意外的跳转）
        window.addEventListener('beforeunload', function(e) {
            addLog('页面即将卸载/跳转', 'warning');
        });
        
        // 监听所有点击事件（检测是否有意外的点击）
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A' || e.target.onclick) {
                addLog(`点击事件: ${e.target.tagName} - ${e.target.textContent || e.target.value}`, 'info');
            }
        });
        
        // 页面加载完成后的初始化
        window.onload = function() {
            addLog('页面完全加载完成', 'success');
            
            // 检查是否有URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const userId = urlParams.get('user_id');
            if (userId) {
                document.getElementById('user_id').value = userId;
                addLog(`从URL参数获取用户ID: ${userId}`, 'info');
            }
            
            const error = urlParams.get('error');
            if (error) {
                addLog(`URL错误参数: ${error}`, 'warning');
            }
        };
    </script>
</body>
</html>
