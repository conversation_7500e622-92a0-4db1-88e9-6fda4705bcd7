const axios = require('axios');

// 测试修复后的IP地理位置查询（使用ip9.com.cn）
async function testIP9Location() {
  console.log('🧪 测试修复后的IP地理位置查询（使用ip9.com.cn）...');
  
  const baseURL = 'http://localhost:15001';
  
  // 测试的IP地址
  const testIPs = [
    {
      ip: '**************',
      description: '从代理检测到的真实IP',
      expectedProvince: '河南'
    },
    {
      ip: '*******',
      description: 'Google DNS',
      expectedProvince: '美国或其他'
    },
    {
      ip: '***************',
      description: '中国DNS',
      expectedProvince: '中国某省'
    },
    {
      ip: '*********',
      description: '阿里DNS',
      expectedProvince: '浙江'
    }
  ];
  
  console.log('\n📋 开始测试各个IP的地理位置查询...\n');
  
  // 1. 直接测试ip9.com.cn服务
  console.log('🔍 第一步：直接测试ip9.com.cn服务');
  console.log('─'.repeat(50));
  
  for (const testCase of testIPs) {
    console.log(`\n📍 测试IP: ${testCase.ip} (${testCase.description})`);
    console.log(`   预期省份: ${testCase.expectedProvince}`);
    
    try {
      const response = await axios.get(`https://ip9.com.cn/get?ip=${testCase.ip}`, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      console.log(`   ✅ ip9.com.cn 查询成功:`);
      console.log(`      完整响应:`, JSON.stringify(response.data, null, 2));
      
      if (response.data.ret === 200 && response.data.data) {
        const data = response.data.data;
        console.log(`      国家: ${data.country}`);
        console.log(`      省份: ${data.prov}`);
        console.log(`      城市: ${data.city}`);
        console.log(`      ISP: ${data.isp}`);
        console.log(`      经纬度: ${data.lng}, ${data.lat}`);
      }
      
    } catch (error) {
      console.log(`   ❌ ip9.com.cn 查询失败: ${error.message}`);
    }
  }
  
  // 2. 测试我们的API接口
  console.log('\n\n🔍 第二步：测试我们的代理IP检测API');
  console.log('─'.repeat(50));
  
  // 使用真实的SK5数据进行测试
  const testSK5 = '450105.dns36.cn|62222|ad9mn4gx|525257|2025-07-20';
  
  console.log(`\n📤 测试SK5: ${testSK5}`);
  
  try {
    const response = await axios.post(`${baseURL}/api/douyin/test-proxy-detection`, {
      sk5: testSK5
    }, {
      timeout: 30000
    });
    
    console.log(`📡 API响应:`, JSON.stringify(response.data, null, 2));
    
    if (response.data.code === 0) {
      const data = response.data.data;
      console.log(`✅ 检测成功:`);
      console.log(`   检测到的IP: ${data.ip}`);
      console.log(`   检测到的省份: ${data.province}`);
      
      // 验证省份是否正确
      if (data.ip === '**************') {
        if (data.province === '河南') {
          console.log(`   🎉 省份检测正确！`);
        } else {
          console.log(`   ⚠️  省份检测可能不准确，预期：河南，实际：${data.province}`);
        }
      }
    } else {
      console.log(`❌ 检测失败: ${response.data.msg}`);
    }
    
  } catch (error) {
    console.log(`❌ API请求失败: ${error.message}`);
  }
  
  // 3. 对比不同服务的结果
  console.log('\n\n🔍 第三步：对比不同IP地理位置服务的结果');
  console.log('─'.repeat(50));
  
  const targetIP = '**************';
  console.log(`\n📍 对比IP: ${targetIP}`);
  
  const services = [
    {
      name: 'ip9.com.cn',
      url: `https://ip9.com.cn/get?ip=${targetIP}`,
      parseProvince: (data) => data.ret === 200 ? data.data.prov : null
    },
    {
      name: 'ipapi.co',
      url: `https://ipapi.co/${targetIP}/json/`,
      parseProvince: (data) => data.region || data.country_name
    },
    {
      name: 'ip-api.com',
      url: `https://ip-api.com/json/${targetIP}?lang=zh-CN`,
      parseProvince: (data) => data.status === 'success' ? data.regionName : null
    }
  ];
  
  for (const service of services) {
    try {
      console.log(`\n🌐 查询服务: ${service.name}`);
      
      const response = await axios.get(service.url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const province = service.parseProvince(response.data);
      console.log(`   结果: ${province || '未知'}`);
      
    } catch (error) {
      console.log(`   ❌ 失败: ${error.message}`);
    }
  }
  
  console.log('\n🎉 IP地理位置查询测试完成！');
  
  console.log('\n💡 总结:');
  console.log('1. ip9.com.cn 专门针对中国IP地址，准确性更高');
  console.log('2. 不同服务可能返回不同的结果，需要选择最可靠的');
  console.log('3. 已将ip9.com.cn设置为首选服务');
  console.log('4. 如果ip9.com.cn失败，会自动尝试其他备选服务');
}

// 运行测试
if (require.main === module) {
  testIP9Location().catch(console.error);
}

module.exports = testIP9Location;
