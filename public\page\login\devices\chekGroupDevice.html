<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <!-- <form class="layui-form layui-form-pane" action="">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">设备名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="deviceName" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button type="submit" class="layui-btn layui-btn-primary" lay-submit
                            lay-filter="data-search-btn"><i class="layui-icon"></i> 模 糊 搜 索</button>
                    </div>
                </div>
            </form> -->
            <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
        </div>
    </div>
    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script>
        var group_Name = ""
        layui.use(['form', 'table'], function () {
            var $ = layui.jquery,
                form = layui.form,
                table = layui.table

            let groupName = parent.groupName

            let tableIns = table.render({
                elem: '#currentTableId',
                url: '/indexDevice/groupDevice',
                // toolbar: '#toolbarDemo',
                // defaultToolbar: ['filter', 'exports', 'print', {
                //     title: '提示',
                //     layEvent: 'LAYTABLE_TIPS',
                //     icon: 'layui-icon-tips'
                // }],
                where: {
                    groupName: groupName,
                },
                cols: [[
                    { type: "checkbox", width: 50 },
                    { field: 'deviceName', width: 100, title: '设备名', sort: true },
                    // { field: 'socketId', width: 190, title: '唯一ID', sort: true },
                    {
                        field: 'taskStatus', width: 100, title: '状态', sort: true, templet: function (d) {
                            // console.log(d); // 得到当前行数据
                            if (d.taskStatus == "掉线") {
                                return '<span style="color: #F00;font-weight:bold">掉线</span>'
                            } else if (d.taskStatus == "空闲") {
                                return '<span style="color: #00CCFF;font-weight:bold">空闲</span>'
                            } else if (d.taskStatus == "忙碌") {
                                return '<span style="color: #FFCC00;font-weight:bold">忙碌</span>'
                            }
                        }
                    },
                    { field: 'taskName', width: 190, title: '当前任务', sort: true },
                    // { field: 'deviceMsg', width: 800, title: '最新消息', sort: true },
                    // { field: 'updateTime', width: 800, title: '心跳时间', sort: true }
                    { title: '操作', width: 150, toolbar: '#currentTableBar', align: "center" },
                ]],
                // limits: [10, 50, 200, 1000, 2000, 5000],
                // limit: 10,
                // page: true,
                // skin: 'line'
            });


            // form.on('submit(data-search-btn)', function (data) {
            //     let datas = data.field
            //     // console.log(datas);
            //     tableIns.reload({
            //         url: '/indexDevice/groupDevice',
            //         method: 'get',
            //         where: {
            //             deviceName: (datas.deviceName + "").replace(/\s/g, ""),
            //             groupName: groupName,
            //         }
            //     })
            //     return false;
            // })

            window.formData = function () {
                var iframeIndex = parent.layer.getFrameIndex(window.name);
                parent.layer.close(iframeIndex);
                return table.checkStatus('currentTableId').data
            }

        });
    </script>
</body>

</html>