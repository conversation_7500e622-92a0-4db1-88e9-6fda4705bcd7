const axios = require("axios");

// 创建axios实例，支持cookie
const axiosInstance = axios.create({
  withCredentials: true,
  timeout: 10000,
});

// 测试推广用户登录界面数据展示
async function testPromoterDashboard() {
  console.log("🧪 测试推广用户登录界面数据展示...");

  const baseURL = "http://localhost:15001";

  // 测试用户信息
  const testUsers = [
    { user_id: "1001", password: "123456", username: "promoter1001" },
    { user_id: "1002", password: "123456", username: "promoter1002" },
    { user_id: "1003", password: "123456", username: "promoter1003" },
  ];

  console.log("\n📋 开始测试推广用户登录和数据查询...\n");

  for (const user of testUsers) {
    console.log(`🔍 测试用户: ${user.username} (ID: ${user.user_id})`);
    console.log("─".repeat(60));

    // 为每个用户创建独立的axios实例
    const userAxios = axios.create({
      baseURL: baseURL,
      withCredentials: true,
      timeout: 10000,
    });

    try {
      // 1. 测试登录
      console.log("1️⃣ 测试登录...");
      const loginResponse = await userAxios.post(
        "/api/promotion/promoter/login",
        {
          user_id: user.user_id,
          password: user.password,
        }
      );

      if (loginResponse.data.code === 0) {
        console.log(`   ✅ 登录成功: ${loginResponse.data.msg}`);

        // 2. 测试获取用户信息
        console.log("2️⃣ 测试获取用户信息...");
        const userInfoResponse = await userAxios.get(
          "/api/promotion/promoter/user-info"
        );

        if (userInfoResponse.data.code === 0) {
          const userInfo = userInfoResponse.data.data;
          console.log(`   ✅ 用户信息获取成功:`);
          console.log(`      用户ID: ${userInfo.user_id}`);
          console.log(`      用户名: ${userInfo.username}`);
        } else {
          console.log(`   ❌ 用户信息获取失败: ${userInfoResponse.data.msg}`);
        }

        // 3. 测试获取今日统计数据
        console.log("3️⃣ 测试获取今日统计数据...");
        const statsResponse = await axios.get(
          `${baseURL}/api/promotion/promoter/today-stats`,
          {
            headers: {
              Cookie: cookieHeader,
            },
          }
        );

        if (statsResponse.data.code === 0) {
          const stats = statsResponse.data.data;
          console.log(`   ✅ 今日统计数据获取成功:`);
          console.log(`      访问次数: ${stats.visit_count}`);
          console.log(`      独立IP数: ${stats.unique_ip_count}`);
          console.log(`      扫码数量: ${stats.scan_count}`);
          console.log(`      成功数量: ${stats.success_count}`);
          console.log(`      失败数量: ${stats.fail_count}`);
          console.log(`      过期数量: ${stats.expire_count}`);
        } else {
          console.log(`   ❌ 今日统计数据获取失败: ${statsResponse.data.msg}`);
        }

        // 4. 测试获取今日操作记录
        console.log("4️⃣ 测试获取今日操作记录...");
        const actionsResponse = await axios.get(
          `${baseURL}/api/promotion/promoter/today-actions?page=1&limit=5`,
          {
            headers: {
              Cookie: cookieHeader,
            },
          }
        );

        if (actionsResponse.data.code === 0) {
          const actions = actionsResponse.data.data;
          console.log(`   ✅ 今日操作记录获取成功:`);
          console.log(`      总记录数: ${actions.total}`);
          console.log(`      当前页记录数: ${actions.list.length}`);

          if (actions.list.length > 0) {
            console.log(`      最近操作记录:`);
            actions.list.slice(0, 3).forEach((action, index) => {
              console.log(
                `        ${index + 1}. 用户: ${action.user_id}, 时间: ${
                  action.time
                }, IP: ${action.ip}, 状态: ${action.status}`
              );
            });
          } else {
            console.log(`      暂无操作记录`);
          }
        } else {
          console.log(
            `   ❌ 今日操作记录获取失败: ${actionsResponse.data.msg}`
          );
        }

        // 5. 测试退出登录
        console.log("5️⃣ 测试退出登录...");
        const logoutResponse = await axios.post(
          `${baseURL}/api/promotion/promoter/logout`,
          {},
          {
            headers: {
              Cookie: cookieHeader,
            },
          }
        );

        if (logoutResponse.data.code === 0) {
          console.log(`   ✅ 退出登录成功: ${logoutResponse.data.msg}`);
        } else {
          console.log(`   ❌ 退出登录失败: ${logoutResponse.data.msg}`);
        }
      } else {
        console.log(`   ❌ 登录失败: ${loginResponse.data.msg}`);
      }
    } catch (error) {
      console.log(`   ❌ 测试失败: ${error.message}`);
      if (error.response) {
        console.log(`      响应状态: ${error.response.status}`);
        console.log(`      响应数据: ${JSON.stringify(error.response.data)}`);
      }
    }

    console.log(""); // 空行分隔
  }

  console.log("🎉 推广用户登录界面测试完成！");

  console.log("\n💡 测试总结:");
  console.log("1. 推广用户登录功能");
  console.log("2. 用户信息获取功能");
  console.log(
    "3. 今日统计数据获取功能（从promotion_daily_stats和promotion_visits表）"
  );
  console.log("4. 今日操作记录获取功能（从promotion_actions表）");
  console.log("5. 退出登录功能");

  console.log("\n🔧 数据查询逻辑:");
  console.log("- 使用登录用户的user_id作为promotion_user_id查询对应数据");
  console.log("- promotion_daily_stats表: 优先查询今日统计数据");
  console.log(
    "- promotion_visits表: 实时计算访问统计（如果daily_stats无数据）"
  );
  console.log("- promotion_actions表: 实时计算操作统计和获取操作记录");

  console.log("\n🌐 前端界面展示:");
  console.log(
    "- 访问 http://localhost:15001/page/promotion/promoter-login.html 进行登录"
  );
  console.log(
    "- 登录后自动跳转到 http://localhost:15001/page/promotion/promoter-dashboard.html"
  );
  console.log("- 界面显示当前登录用户的专属数据");
}

// 运行测试
if (require.main === module) {
  testPromoterDashboard().catch(console.error);
}

module.exports = testPromoterDashboard;
