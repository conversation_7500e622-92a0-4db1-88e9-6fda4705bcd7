const mysql = require("mysql2/promise");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
};

/**
 * 修复promotion_daily_stats表中的scan_count数据
 * 让scan_count = success_count + fail_count + expire_count
 */
async function fixScanCountData() {
  console.log("🔧 开始修复promotion_daily_stats表中的scan_count数据...");
  
  const connection = await mysql.createConnection(dbConfig);
  
  try {
    // 1. 查询所有需要修复的记录
    const [records] = await connection.execute(`
      SELECT 
        id,
        promotion_user_id,
        stat_date,
        scan_count,
        success_count,
        fail_count,
        expire_count,
        (success_count + fail_count + expire_count) as calculated_scan_count
      FROM promotion_daily_stats
      WHERE scan_count != (success_count + fail_count + expire_count)
    `);
    
    console.log(`📊 找到 ${records.length} 条需要修复的记录`);
    
    if (records.length === 0) {
      console.log("✅ 所有数据都是正确的，无需修复");
      return;
    }
    
    // 2. 显示需要修复的记录
    console.log("\n📋 需要修复的记录：");
    records.forEach((record, index) => {
      console.log(`${index + 1}. 用户${record.promotion_user_id} (${record.stat_date}): scan_count ${record.scan_count} -> ${record.calculated_scan_count} (成功:${record.success_count} + 失败:${record.fail_count} + 过期:${record.expire_count})`);
    });
    
    // 3. 批量修复数据
    console.log("\n🔄 开始批量修复...");
    
    for (const record of records) {
      await connection.execute(`
        UPDATE promotion_daily_stats 
        SET scan_count = ?, updated_at = NOW()
        WHERE id = ?
      `, [record.calculated_scan_count, record.id]);
      
      console.log(`✅ 修复完成: 用户${record.promotion_user_id} (${record.stat_date}) scan_count: ${record.scan_count} -> ${record.calculated_scan_count}`);
    }
    
    // 4. 验证修复结果
    console.log("\n🔍 验证修复结果...");
    const [verifyRecords] = await connection.execute(`
      SELECT 
        COUNT(*) as total_records,
        SUM(CASE WHEN scan_count = (success_count + fail_count + expire_count) THEN 1 ELSE 0 END) as correct_records,
        SUM(CASE WHEN scan_count != (success_count + fail_count + expire_count) THEN 1 ELSE 0 END) as incorrect_records
      FROM promotion_daily_stats
    `);
    
    const result = verifyRecords[0];
    console.log(`📊 验证结果:`);
    console.log(`   总记录数: ${result.total_records}`);
    console.log(`   正确记录数: ${result.correct_records}`);
    console.log(`   错误记录数: ${result.incorrect_records}`);
    
    if (result.incorrect_records === 0) {
      console.log("🎉 所有数据修复完成！scan_count逻辑现在是正确的");
    } else {
      console.log("⚠️  仍有错误记录，请检查");
    }
    
  } catch (error) {
    console.error("❌ 修复过程中出现错误:", error);
  } finally {
    await connection.end();
  }
}

/**
 * 重新计算所有推广用户的每日统计数据
 */
async function recalculateAllDailyStats() {
  console.log("\n🔄 重新计算所有推广用户的每日统计数据...");
  
  const connection = await mysql.createConnection(dbConfig);
  
  try {
    // 获取所有推广用户和日期的组合
    const [userDates] = await connection.execute(`
      SELECT DISTINCT 
        promotion_user_id,
        DATE(action_time) as stat_date
      FROM promotion_actions
      ORDER BY promotion_user_id, stat_date
    `);
    
    console.log(`📊 找到 ${userDates.length} 个用户-日期组合需要重新计算`);
    
    for (const userDate of userDates) {
      const { promotion_user_id, stat_date } = userDate;
      
      // 计算访问统计
      const [visitStats] = await connection.execute(`
        SELECT
          COUNT(*) as visit_count,
          COUNT(DISTINCT visitor_ip) as unique_ip_count
        FROM promotion_visits
        WHERE promotion_user_id = ? AND DATE(visit_time) = ?
      `, [promotion_user_id, stat_date]);
      
      // 计算操作统计
      const [actionStats] = await connection.execute(`
        SELECT
          SUM(CASE WHEN action_type = 'login_success' THEN 1 ELSE 0 END) as success_count,
          SUM(CASE WHEN action_type = 'login_fail' THEN 1 ELSE 0 END) as fail_count,
          SUM(CASE WHEN action_type = 'request_expire' THEN 1 ELSE 0 END) as expire_count
        FROM promotion_actions
        WHERE promotion_user_id = ? AND DATE(action_time) = ?
      `, [promotion_user_id, stat_date]);
      
      const visitData = visitStats[0] || { visit_count: 0, unique_ip_count: 0 };
      const actionData = actionStats[0] || { success_count: 0, fail_count: 0, expire_count: 0 };
      
      // 计算扫码数量
      const calculatedScanCount = (actionData.success_count || 0) + 
                                 (actionData.fail_count || 0) + 
                                 (actionData.expire_count || 0);
      
      // 更新或插入统计数据
      await connection.execute(`
        INSERT INTO promotion_daily_stats
        (promotion_user_id, stat_date, visit_count, unique_ip_count, scan_count, success_count, fail_count, expire_count)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        visit_count = VALUES(visit_count),
        unique_ip_count = VALUES(unique_ip_count),
        scan_count = VALUES(scan_count),
        success_count = VALUES(success_count),
        fail_count = VALUES(fail_count),
        expire_count = VALUES(expire_count),
        updated_at = NOW()
      `, [
        promotion_user_id,
        stat_date,
        visitData.visit_count || 0,
        visitData.unique_ip_count || 0,
        calculatedScanCount,
        actionData.success_count || 0,
        actionData.fail_count || 0,
        actionData.expire_count || 0,
      ]);
      
      console.log(`✅ 重新计算完成: 用户${promotion_user_id} (${stat_date}) - 扫码:${calculatedScanCount}, 成功:${actionData.success_count}, 失败:${actionData.fail_count}, 过期:${actionData.expire_count}`);
    }
    
    console.log("🎉 所有每日统计数据重新计算完成！");
    
  } catch (error) {
    console.error("❌ 重新计算过程中出现错误:", error);
  } finally {
    await connection.end();
  }
}

// 主函数
async function main() {
  console.log("🚀 开始修复推广统计数据的scan_count逻辑");
  console.log("📋 修复规则: scan_count = success_count + fail_count + expire_count");
  console.log("=" * 60);
  
  try {
    // 1. 修复现有的错误数据
    await fixScanCountData();
    
    // 2. 重新计算所有统计数据
    await recalculateAllDailyStats();
    
    console.log("\n" + "=" * 60);
    console.log("🎉 所有修复工作完成！");
    console.log("💡 现在scan_count将始终等于success_count + fail_count + expire_count");
    
  } catch (error) {
    console.error("❌ 修复过程中出现严重错误:", error);
    process.exit(1);
  }
}

// 运行修复脚本
if (require.main === module) {
  main();
}

module.exports = {
  fixScanCountData,
  recalculateAllDailyStats
};
