# MongoDB功能禁用说明

## 🚫 已禁用的MongoDB功能

为了让程序能够正常运行而不依赖MongoDB，已经禁用了以下功能：

### 📋 禁用的文件和功能

1. **数据库初始化**
   - 原文件: `db/initdb.js`
   - 替换为: `db/initdb-disabled.js`
   - 功能: 禁用MongoDB用户创建

2. **用户管理路由**
   - 原文件: `routes/user.js`
   - 替换为: `routes/user-disabled.js`
   - 功能: 禁用MongoDB用户管理功能

3. **主页路由**
   - 原文件: `routes/index.js`
   - 替换为: `routes/index-disabled.js`
   - 功能: 简化登录验证，不依赖MongoDB

4. **Session存储**
   - 原配置: MongoDB存储
   - 新配置: 内存存储
   - 注意: 重启服务器会丢失session

### 🔧 修改的配置

1. **app.js修改**
   ```javascript
   // 禁用MongoDB初始化
   require("./db/initdb-disabled");
   
   // 禁用MongoDB session store
   // const MongoStore = require("connect-mongo");
   
   // 使用内存存储替代MongoDB存储
   session({
     // store: new MongoStore({ ... }), // 已注释
     cookie: { maxAge: 7 * 24 * 60 * 60 * 1000 }
   })
   ```

2. **路由替换**
   ```javascript
   const indexRouter = require("./routes/index-disabled");
   const user = require("./routes/user-disabled");
   ```

### ✅ 仍然可用的功能

1. **MySQL数据库功能**
   - 抖音账号管理 (`/api/douyin/accounts`)
   - 抖音IP代理管理 (`/api/douyin/proxies`)
   - 用户ckcount管理 (`/api/douyin/increase-user-ckcount`)

2. **基本Web功能**
   - 静态文件服务
   - 文件上传
   - WebSocket连接
   - API接口

3. **简化的用户认证**
   - 管理员登录: `cjroot / 123456`
   - Session管理（内存存储）

### 🚀 启动服务

现在可以正常启动服务：

```bash
# 方法1: 使用项目启动脚本
node bin/ceshi

# 方法2: 直接启动
node app.js

# 方法3: 使用npm（如果配置了package.json）
npm start
```

### 🌐 访问地址

- **主页**: http://localhost:15001
- **登录**: 用户名 `cjroot`，密码 `123456`
- **API文档**: 
  - 抖音账号管理: http://localhost:15001/api/douyin/accounts
  - 代理IP管理: http://localhost:15001/api/douyin/proxies

### ⚠️ 注意事项

1. **Session丢失**: 重启服务器会丢失所有登录状态
2. **用户管理**: MongoDB用户管理功能已禁用，需要使用MySQL进行用户管理
3. **数据持久化**: 只有MySQL数据会持久化保存
4. **功能限制**: 部分依赖MongoDB的高级功能可能不可用

### 🔄 如需恢复MongoDB功能

如果将来需要恢复MongoDB功能：

1. 安装并启动MongoDB服务
2. 恢复原始文件：
   ```bash
   # 恢复原始路由文件
   mv routes/user.js.backup routes/user.js
   mv routes/index.js.backup routes/index.js
   
   # 修改app.js中的引用
   # require("./routes/index") 
   # require("./routes/user")
   ```
3. 恢复MongoDB session存储配置
4. 重新启动服务

### 📞 技术支持

如果遇到问题，请检查：
1. MySQL服务是否正常运行
2. 端口15001是否被占用
3. 日志输出中的错误信息
