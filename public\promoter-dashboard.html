<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户仪表板</title>
    <link rel="stylesheet" href="/layui/css/layui.css">
    <style>
        body {
            background-color: #f2f2f2;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 24px;
            font-weight: bold;
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-info {
            font-size: 14px;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .actions-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .date-info {
            background: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            text-align: center;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        .error {
            text-align: center;
            padding: 40px;
            color: #ff4757;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-info {
                flex-direction: column;
                gap: 10px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .container {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <!-- 头部 -->
    <div class="header">
        <div class="header-content">
            <div class="header-title">推广用户仪表板</div>
            <div class="header-info">
                <div class="user-info" id="userInfo">加载中...</div>
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
        <!-- 日期信息 -->
        <div class="date-info">
            <strong>今日日期：</strong><span id="todayDate"></span>
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="visitCount">0</div>
                <div class="stat-label">访问次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uniqueIpCount">0</div>
                <div class="stat-label">独立IP数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="scanCount">0</div>
                <div class="stat-label">扫码数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successCount">0</div>
                <div class="stat-label">成功数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failCount">0</div>
                <div class="stat-label">失败数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="expireCount">0</div>
                <div class="stat-label">过期数量</div>
            </div>
        </div>

        <!-- 操作记录 -->
        <div class="actions-section">
            <div class="section-title">今日操作记录</div>
            <table class="layui-table">
                <thead>
                    <tr>
                        <th>用户ID</th>
                        <th>时间</th>
                        <th>IP地址</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody id="actionTableBody">
                    <tr>
                        <td colspan="4" class="loading">加载中...</td>
                    </tr>
                </tbody>
            </table>
            <div id="pagination"></div>
        </div>
    </div>

    <script src="/layui/layui.js"></script>
    <script>
        layui.use(['layer', 'laypage'], function () {
            const layer = layui.layer;
            const laypage = layui.laypage;

            let currentPage = 1;
            const pageSize = 10;

            // 通用请求函数
            async function makeRequest(url, options = {}) {
                const defaultOptions = {
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                };

                const finalOptions = { ...defaultOptions, ...options };

                try {
                    const response = await fetch(url, finalOptions);
                    const data = await response.json();
                    return { success: true, status: response.status, data: data };
                } catch (error) {
                    console.error(`请求失败: ${url}`, error);
                    return { success: false, error: error.message };
                }
            }

            // 加载用户信息
            async function loadUserInfo() {
                console.log('📋 开始加载用户信息...');
                const result = await makeRequest('/api/promotion/promoter/user-info');

                console.log('📋 用户信息API响应:', result);

                if (result.success && result.data.code === 0) {
                    const userInfo = result.data.data;
                    document.getElementById('userInfo').textContent =
                        `推广用户: ${userInfo.username} (ID: ${userInfo.user_id})`;
                    console.log('✅ 用户信息加载成功:', userInfo);
                } else if (result.data?.code === -1 && result.data?.msg?.includes('登录')) {
                    // 未登录，跳转到登录页
                    console.log('❌ 用户未登录，跳转到登录页');
                    window.location.href = '/promoter-login?error=' + encodeURIComponent('请先登录');
                } else {
                    console.error('❌ 获取用户信息失败:', result.data?.msg || result.error);
                    document.getElementById('userInfo').textContent = '获取用户信息失败';
                }
            }

            // 加载统计数据
            async function loadStats() {
                console.log('📊 开始加载统计数据...');
                const result = await makeRequest('/api/promotion/promoter/today-stats');

                console.log('📊 统计数据API响应:', result);

                if (result.success && result.data.code === 0) {
                    const stats = result.data.data;

                    console.log('📊 统计数据详情:', stats);

                    // 更新显示
                    document.getElementById('visitCount').textContent = stats.visit_count || 0;
                    document.getElementById('uniqueIpCount').textContent = stats.unique_ip_count || 0;
                    document.getElementById('scanCount').textContent = stats.scan_count || 0;
                    document.getElementById('successCount').textContent = stats.success_count || 0;
                    document.getElementById('failCount').textContent = stats.fail_count || 0;
                    document.getElementById('expireCount').textContent = stats.expire_count || 0;

                    // 检查数据是否为0
                    const hasData = Object.values(stats).some(val => val > 0);
                    if (hasData) {
                        console.log('✅ 统计数据加载成功，有非零数据');
                    } else {
                        console.warn('⚠️ 统计数据全部为0');
                    }

                } else if (result.data?.code === -1 && result.data?.msg?.includes('登录')) {
                    // 未登录，跳转到登录页
                    console.log('❌ 用户未登录，跳转到登录页');
                    window.location.href = '/promoter-login?error=' + encodeURIComponent('请先登录');
                } else {
                    console.error('❌ 获取统计数据失败:', result.data?.msg || result.error);
                }
            }

            // 加载操作记录
            async function loadActions(page = 1) {
                const result = await makeRequest(`/api/promotion/promoter/today-actions?page=${page}&limit=${pageSize}`);

                if (result.success && result.data.code === 0) {
                    const data = result.data.data;
                    renderActionTable(data.list);
                    renderPagination(data.total, page);
                } else if (result.data?.code === -1 && result.data?.msg?.includes('登录')) {
                    // 未登录，跳转到登录页
                    window.location.href = '/promoter-login?error=' + encodeURIComponent('请先登录');
                } else {
                    document.getElementById('actionTableBody').innerHTML =
                        '<tr><td colspan="4" class="error">加载失败</td></tr>';
                }
            }

            // 渲染操作记录表格
            function renderActionTable(list) {
                const tbody = document.getElementById('actionTableBody');

                if (list.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 40px; color: #999;">暂无数据</td></tr>';
                    return;
                }

                const html = list.map(item => `
                    <tr>
                        <td>${item.user_id || '-'}</td>
                        <td>${item.time}</td>
                        <td>${item.ip}</td>
                        <td>
                            <span class="layui-badge ${getStatusClass(item.status)}">${item.status}</span>
                        </td>
                    </tr>
                `).join('');

                tbody.innerHTML = html;
            }

            // 获取状态样式类
            function getStatusClass(status) {
                switch (status) {
                    case '登录成功': return 'layui-bg-green';
                    case '登录失败': return 'layui-bg-red';
                    case '请求过期': return 'layui-bg-orange';
                    case '扫码': return 'layui-bg-blue';
                    default: return '';
                }
            }

            // 渲染分页
            function renderPagination(total, current) {
                if (total <= pageSize) {
                    document.getElementById('pagination').innerHTML = '';
                    return;
                }

                laypage.render({
                    elem: 'pagination',
                    count: total,
                    limit: pageSize,
                    curr: current,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            loadActions(obj.curr);
                        }
                    }
                });
            }

            // 退出登录
            window.logout = async function () {
                const confirmed = await new Promise(resolve => {
                    layer.confirm('确定要退出登录吗？', { icon: 3, title: '提示' }, function (index) {
                        layer.close(index);
                        resolve(true);
                    }, function () {
                        resolve(false);
                    });
                });

                if (!confirmed) return;

                const result = await makeRequest('/api/promotion/promoter/logout', { method: 'POST' });

                layer.msg('已退出登录', { icon: 1 }, function () {
                    window.location.href = '/promoter-login';
                });
            }

            // 页面初始化
            document.addEventListener('DOMContentLoaded', function () {
                console.log('🚀 推广用户仪表板页面加载完成，开始初始化数据...');

                // 显示今日日期
                const today = new Date();
                document.getElementById('todayDate').textContent = today.toLocaleDateString('zh-CN');

                // 延迟一下确保session已经生效
                setTimeout(() => {
                    console.log('📋 开始加载用户信息...');
                    loadUserInfo();

                    console.log('📊 开始加载统计数据...');
                    loadStats();

                    console.log('📝 开始加载操作记录...');
                    loadActions();
                }, 500);

                // 设置定时刷新（每30秒）
                setInterval(() => {
                    console.log('🔄 定时刷新数据...');
                    loadStats();
                    loadActions();
                }, 30000);
            });
        });
    </script>
</body>

</html>