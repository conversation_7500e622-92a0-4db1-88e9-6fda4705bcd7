<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广管理 - 管理员</title>
    <link rel="stylesheet" href="../../lib/layui/css/layui.css">
    <style>
        .container {
            padding: 20px;
        }

        .section {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
            border-radius: 8px 8px 0 0;
        }

        .section-content {
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #5FB878;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
            margin-top: 5px;
        }

        .toolbar {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .date-picker {
            display: flex;
            align-items: center;
            gap: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 登录状态提示 -->
        <div id="loginStatus" class="layui-card" style="display: none;">
            <div class="layui-card-header" style="background: #f56c6c; color: white;">
                <h3>⚠️ 需要管理员登录</h3>
                <p>请先登录管理员账户才能使用推广管理功能</p>
                <button class="layui-btn layui-btn-primary" onclick="showAdminLogin()" style="margin-top: 10px;">
                    <i class="layui-icon layui-icon-username"></i> 管理员登录
                </button>
            </div>
        </div>

        <!-- 页面标题 -->
        <div id="mainContent" class="layui-card">
            <div class="layui-card-header">
                <h2>推广管理系统</h2>
                <p>管理推广用户，查看推广数据统计</p>
                <div style="float: right;">
                    <span id="adminInfo" style="margin-right: 15px;"></span>
                    <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="adminLogout()">
                        <i class="layui-icon layui-icon-logout"></i> 退出登录
                    </button>
                </div>
                <div style="clear: both;"></div>
            </div>
        </div>

        <!-- 推广用户管理 -->
        <div class="section">
            <div class="section-header">推广用户管理</div>
            <div class="section-content">
                <div class="toolbar">
                    <button class="layui-btn" onclick="showAddUserDialog()">
                        <i class="layui-icon layui-icon-add-1"></i> 添加推广用户
                    </button>
                    <button class="layui-btn layui-btn-normal" onclick="loadPromotionUsers()">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>

                <table class="layui-table" lay-filter="promotionUsersTable">
                    <thead>
                        <tr>
                            <th lay-data="{field:'user_id', width:120}">用户ID</th>
                            <th lay-data="{field:'username', width:150}">用户名</th>
                            <th lay-data="{field:'promotion_link', minWidth:300}">推广链接</th>
                            <th lay-data="{field:'status', width:100, templet:'#statusTpl'}">状态</th>
                            <th lay-data="{field:'created_at', width:180}">创建时间</th>
                            <th lay-data="{fixed:'right', width:150, toolbar:'#operationTpl'}">操作</th>
                        </tr>
                    </thead>
                    <tbody id="promotionUsersBody">
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 40px;">
                                加载中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 统计数据概览 -->
        <div class="section">
            <div class="section-header">今日推广统计概览</div>
            <div class="section-content">
                <div class="toolbar">
                    <div class="date-picker">
                        <label>统计日期：</label>
                        <input type="text" id="statDate" class="layui-input" style="width: 150px;" readonly>
                    </div>
                    <button class="layui-btn layui-btn-sm" onclick="loadAllUsersStats()">
                        <i class="layui-icon layui-icon-search"></i> 查询
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="exportStats()">
                        <i class="layui-icon layui-icon-export"></i> 导出统计
                    </button>
                </div>

                <div id="statsContainer">
                    <div style="text-align: center; padding: 40px; color: #999;">
                        请选择日期查询统计数据
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细操作记录 -->
        <div class="section">
            <div class="section-header">详细操作记录</div>
            <div class="section-content">
                <div class="toolbar">
                    <div class="date-picker">
                        <label>查询日期：</label>
                        <input type="text" id="actionDate" class="layui-input" style="width: 150px;" readonly>
                    </div>
                    <select id="promotionUserFilter" class="layui-input" style="width: 150px;">
                        <option value="">全部推广用户</option>
                    </select>
                    <select id="actionTypeFilter" class="layui-input" style="width: 120px;">
                        <option value="">全部操作</option>
                        <option value="scan">扫码</option>
                        <option value="login_success">登录成功</option>
                        <option value="login_fail">登录失败</option>
                        <option value="request_expire">请求过期</option>
                    </select>
                    <button class="layui-btn layui-btn-sm" onclick="loadAllUsersActions()">
                        <i class="layui-icon layui-icon-search"></i> 查询
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="exportActions()">
                        <i class="layui-icon layui-icon-export"></i> 导出记录
                    </button>
                </div>

                <table class="layui-table" lay-filter="actionsTable">
                    <thead>
                        <tr>
                            <th lay-data="{field:'promotion_user_id', width:100}">推广用户</th>
                            <th lay-data="{field:'user_id', width:120}">用户ID</th>
                            <th lay-data="{field:'time', width:100}">时间</th>
                            <th lay-data="{field:'ip', width:130}">IP地址</th>
                            <th lay-data="{field:'status', width:100, templet:'#actionStatusTpl'}">状态</th>
                            <th lay-data="{field:'douyin_name', width:150}">抖音名</th>
                            <th lay-data="{field:'douyin_id', width:150}">抖音号</th>
                            <th lay-data="{field:'ck', minWidth:200, templet:'#ckTpl'}">CK</th>
                        </tr>
                    </thead>
                    <tbody id="actionsBody">
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 40px;">
                                请选择条件查询操作记录
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- 分页 -->
                <div id="actionsPagination" style="text-align: center; padding: 20px;"></div>
            </div>
        </div>
    </div>

    <!-- 管理员登录对话框 -->
    <div id="adminLoginDialog" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="adminLoginForm">
            <div class="layui-form-item">
                <label class="layui-form-label">用户名</label>
                <div class="layui-input-block">
                    <input type="text" name="username" required lay-verify="required" placeholder="管理员用户名"
                        autocomplete="off" class="layui-input" value="admin">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">密码</label>
                <div class="layui-input-block">
                    <input type="password" name="password" required lay-verify="required" placeholder="管理员密码"
                        autocomplete="off" class="layui-input" value="123456">
                </div>
            </div>
        </form>
    </div>

    <!-- 添加推广用户对话框 -->
    <div id="addUserDialog" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="addUserForm">
            <div class="layui-form-item">
                <label class="layui-form-label">用户ID</label>
                <div class="layui-input-block">
                    <input type="text" name="user_id" required lay-verify="required" placeholder="如：1001"
                        autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">用户名</label>
                <div class="layui-input-block">
                    <input type="text" name="username" required lay-verify="required" placeholder="推广用户名"
                        autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">密码</label>
                <div class="layui-input-block">
                    <input type="password" name="password" required lay-verify="required" placeholder="登录密码"
                        autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">推广链接</label>
                <div class="layui-input-block">
                    <input type="text" name="promotion_link" placeholder="留空自动生成：http://localhost:15001?id=用户ID"
                        autocomplete="off" class="layui-input">
                </div>
            </div>
        </form>
    </div>

    <!-- 模板 -->
    <script type="text/html" id="statusTpl">
        {{# if(d.status == 1) { }}
            <span class="layui-badge layui-bg-green">启用</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-gray">禁用</span>
        {{# } }}
    </script>

    <script type="text/html" id="operationTpl">
        <a class="layui-btn layui-btn-xs" lay-event="toggle">
            {{# if(d.status == 1) { }}禁用{{# } else { }}启用{{# } }}
        </a>
    </script>

    <script type="text/html" id="actionStatusTpl">
        {{# if(d.status == '登录成功') { }}
            <span class="layui-badge layui-bg-green">{{d.status}}</span>
        {{# } else if(d.status == '登录失败') { }}
            <span class="layui-badge layui-bg-red">{{d.status}}</span>
        {{# } else if(d.status == '请求过期') { }}
            <span class="layui-badge layui-bg-orange">{{d.status}}</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-blue">{{d.status}}</span>
        {{# } }}
    </script>

    <script type="text/html" id="ckTpl">
        {{# if(d.ck && d.ck !== '-') { }}
            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                 title="{{d.ck}}">{{d.ck}}</div>
        {{# } else { }}
            <span style="color: #999;">-</span>
        {{# } }}
    </script>

    <script src="../../lib/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer', 'laydate', 'laypage', 'table'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var laydate = layui.laydate;
            var laypage = layui.laypage;
            var table = layui.table;

            let currentActionsPage = 1;
            const actionsPageSize = 20;

            // 初始化日期选择器
            laydate.render({
                elem: '#statDate',
                value: new Date(),
                done: function (value) {
                    loadAllUsersStats();
                }
            });

            laydate.render({
                elem: '#actionDate',
                value: new Date(),
                done: function (value) {
                    loadAllUsersActions();
                }
            });

            // 页面加载时初始化
            // 确保DOM已加载完成后再初始化
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function () {
                    console.log('📋 DOM加载完成，开始初始化...');
                    checkAdminLogin();
                });
            } else {
                console.log('📋 DOM已就绪，立即初始化...');
                checkAdminLogin();
            }

            // 检查管理员登录状态
            function checkAdminLogin() {
                // 直接显示主要内容并加载数据（已简化管理员认证）
                console.log('🔧 初始化推广用户管理界面...');
                showMainContent();
                loadPromotionUsers();
                loadAllUsersStats();
                loadAllUsersActions();
            }

            // 显示需要登录的提示
            function showLoginRequired() {
                document.getElementById('loginStatus').style.display = 'block';
                document.getElementById('mainContent').style.display = 'none';
                document.querySelectorAll('.section').forEach(section => {
                    section.style.display = 'none';
                });
            }

            // 显示主要内容
            function showMainContent() {
                document.getElementById('loginStatus').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                document.querySelectorAll('.section').forEach(section => {
                    section.style.display = 'block';
                });
                document.getElementById('adminInfo').textContent = '管理员已登录';
            }

            // 显示管理员登录对话框
            window.showAdminLogin = function () {
                layer.open({
                    type: 1,
                    title: '管理员登录',
                    content: document.getElementById('adminLoginDialog'),
                    area: ['400px', '300px'],
                    btn: ['登录', '取消'],
                    yes: function (index) {
                        // 获取表单数据
                        var formData = form.val('adminLoginForm');

                        // 发送登录请求
                        fetch('/api/promotion/admin/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(formData)
                        })
                            .then(response => response.json())
                            .then(result => {
                                if (result.code === 0) {
                                    layer.close(index);
                                    layer.msg('登录成功', { icon: 1 });
                                    showMainContent();
                                    loadPromotionUsers();
                                    loadAllUsersStats();
                                    loadAllUsersActions();
                                } else {
                                    layer.msg(result.msg || '登录失败', { icon: 2 });
                                }
                            })
                            .catch(error => {
                                console.error('登录失败:', error);
                                layer.msg('网络错误', { icon: 2 });
                            });
                    }
                });
            }

            // 管理员退出登录
            window.adminLogout = function () {
                layer.confirm('确定要退出登录吗？', { icon: 3, title: '提示' }, function (index) {
                    fetch('/api/promotion/admin/logout', { method: 'POST' })
                        .then(response => response.json())
                        .then(result => {
                            layer.close(index);
                            layer.msg('已退出登录', { icon: 1 });
                            showLoginRequired();
                        })
                        .catch(error => {
                            layer.close(index);
                            console.error('退出登录失败:', error);
                            showLoginRequired();
                        });
                });
            }

            // 显示添加用户对话框
            window.showAddUserDialog = function () {
                layer.open({
                    type: 1,
                    title: '添加推广用户',
                    content: document.getElementById('addUserDialog'),
                    area: ['500px', '400px'],
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        // 获取表单数据
                        var formData = form.val('addUserForm');

                        // 发送添加请求
                        fetch('/api/promotion/admin/add-promotion-user', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(formData)
                        })
                            .then(response => response.json())
                            .then(result => {
                                if (result.code === 0) {
                                    layer.close(index);
                                    layer.msg('添加成功', { icon: 1 });
                                    loadPromotionUsers();
                                    // 清空表单
                                    document.querySelector('#addUserDialog form').reset();
                                } else {
                                    layer.msg(result.msg || '添加失败', { icon: 2 });
                                }
                            })
                            .catch(error => {
                                console.error('添加用户失败:', error);
                                layer.msg('网络错误', { icon: 2 });
                            });
                    }
                });
            }

            // 加载推广用户列表
            window.loadPromotionUsers = function () {
                console.log('🔄 开始加载推广用户列表...');

                fetch('/api/promotion/admin/promotion-users')
                    .then(response => {
                        console.log('📡 API响应状态:', response.status);
                        return response.json();
                    })
                    .then(result => {
                        console.log('📊 API响应数据:', result);

                        if (result.code === 0) {
                            console.log('✅ 推广用户数据加载成功，共', result.data.list.length, '条记录');
                            renderPromotionUsersTable(result.data.list);
                            updatePromotionUserFilter(result.data.list);
                        } else if (result.code === -1 && result.msg.includes('登录')) {
                            console.log('⚠️  需要登录，显示登录界面');
                            showLoginRequired();
                        } else {
                            console.error('❌ API返回错误:', result.msg);
                            layer.msg(result.msg || '加载失败', { icon: 2 });
                        }
                    })
                    .catch(error => {
                        console.error('❌ 加载推广用户失败:', error);
                        layer.msg('网络错误: ' + error.message, { icon: 2 });
                    });
            }

            // 渲染推广用户表格
            function renderPromotionUsersTable(list) {
                console.log('🎨 开始渲染推广用户表格，数据:', list);

                const tbody = document.getElementById('promotionUsersBody');

                if (!tbody) {
                    console.error('❌ 找不到表格元素 promotionUsersBody');
                    return;
                }

                if (list.length === 0) {
                    console.log('⚠️  没有推广用户数据，显示空状态');
                    tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 40px;">暂无数据</td></tr>';
                    return;
                }

                console.log('✅ 渲染', list.length, '条推广用户记录');

                const html = list.map((item, index) => {
                    console.log(`📝 渲染第${index + 1}条记录:`, item);
                    return `
                    <tr>
                        <td>${item.user_id || '-'}</td>
                        <td>${item.username || '-'}</td>
                        <td style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                            title="${item.promotion_link || ''}">${item.promotion_link || '-'}</td>
                        <td>
                            <span class="layui-badge ${item.status == 1 ? 'layui-bg-green' : 'layui-bg-gray'}">
                                ${item.status == 1 ? '启用' : '禁用'}
                            </span>
                        </td>
                        <td>${item.created_at ? new Date(item.created_at).toLocaleString('zh-CN') : '-'}</td>
                        <td>
                            <button class="layui-btn layui-btn-xs" onclick="toggleUserStatus('${item.user_id}', ${item.status})">
                                ${item.status == 1 ? '禁用' : '启用'}
                            </button>
                        </td>
                    </tr>
                `;
                }).join('');

                tbody.innerHTML = html;
                console.log('✅ 表格渲染完成');
            }

            // 切换用户状态
            window.toggleUserStatus = function (userId, currentStatus) {
                const newStatus = currentStatus == 1 ? 0 : 1;
                const action = newStatus == 1 ? '启用' : '禁用';

                layer.confirm(`确定要${action}用户 ${userId} 吗？`, { icon: 3, title: '确认' }, function (index) {
                    fetch('/api/promotion/admin/update-promotion-user-status', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user_id: userId,
                            status: newStatus
                        })
                    })
                        .then(response => response.json())
                        .then(result => {
                            layer.close(index);
                            if (result.code === 0) {
                                layer.msg(`${action}成功`, { icon: 1 });
                                loadPromotionUsers();
                            } else {
                                layer.msg(result.msg || `${action}失败`, { icon: 2 });
                            }
                        })
                        .catch(error => {
                            layer.close(index);
                            console.error('更新状态失败:', error);
                            layer.msg('网络错误', { icon: 2 });
                        });
                });
            }

            // 更新推广用户筛选器
            function updatePromotionUserFilter(users) {
                const select = document.getElementById('promotionUserFilter');
                const currentValue = select.value;

                select.innerHTML = '<option value="">全部推广用户</option>';
                users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.user_id;
                    option.textContent = `${user.user_id} - ${user.username}`;
                    select.appendChild(option);
                });

                select.value = currentValue;
            }

            // 加载所有用户统计数据
            window.loadAllUsersStats = function () {
                const date = document.getElementById('statDate').value;

                fetch(`/api/promotion/admin/all-users-stats?date=${date}`)
                    .then(response => response.json())
                    .then(result => {
                        if (result.code === 0) {
                            renderStatsGrid(result.data.stats);
                        } else {
                            document.getElementById('statsContainer').innerHTML =
                                '<div style="text-align: center; padding: 40px; color: #f56c6c;">加载失败</div>';
                        }
                    })
                    .catch(error => {
                        console.error('加载统计数据失败:', error);
                        document.getElementById('statsContainer').innerHTML =
                            '<div style="text-align: center; padding: 40px; color: #f56c6c;">网络错误</div>';
                    });
            }

            // 渲染统计网格
            function renderStatsGrid(stats) {
                const container = document.getElementById('statsContainer');

                if (stats.length === 0) {
                    container.innerHTML = '<div style="text-align: center; padding: 40px; color: #999;">暂无统计数据</div>';
                    return;
                }

                const html = stats.map(stat => `
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">
                            ${stat.user_id} - ${stat.username}
                        </div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">${stat.visit_count}</div>
                                <div class="stat-label">访问次数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${stat.unique_ip_count}</div>
                                <div class="stat-label">独立IP</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${stat.scan_count}</div>
                                <div class="stat-label">扫码数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${stat.success_count}</div>
                                <div class="stat-label">成功数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${stat.fail_count}</div>
                                <div class="stat-label">失败数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${stat.expire_count}</div>
                                <div class="stat-label">过期数</div>
                            </div>
                        </div>
                    </div>
                `).join('');

                container.innerHTML = html;
            }

            // 加载所有用户操作记录
            window.loadAllUsersActions = function (page = 1) {
                currentActionsPage = page;
                const date = document.getElementById('actionDate').value;
                const promotionUserId = document.getElementById('promotionUserFilter').value;
                const actionType = document.getElementById('actionTypeFilter').value;

                let url = `/api/promotion/admin/all-users-actions?page=${page}&limit=${actionsPageSize}&date=${date}`;
                if (promotionUserId) url += `&promotion_user_id=${promotionUserId}`;
                if (actionType) url += `&action_type=${actionType}`;

                fetch(url)
                    .then(response => response.json())
                    .then(result => {
                        if (result.code === 0) {
                            renderActionsTable(result.data.list);
                            renderActionsPagination(result.data.total, page);
                        } else {
                            document.getElementById('actionsBody').innerHTML =
                                '<tr><td colspan="8" style="text-align: center; padding: 40px; color: #999;">暂无数据</td></tr>';
                            document.getElementById('actionsPagination').innerHTML = '';
                        }
                    })
                    .catch(error => {
                        console.error('加载操作记录失败:', error);
                        document.getElementById('actionsBody').innerHTML =
                            '<tr><td colspan="8" style="text-align: center; padding: 40px; color: #f56c6c;">加载失败</td></tr>';
                    });
            }

            // 渲染操作记录表格
            function renderActionsTable(list) {
                const tbody = document.getElementById('actionsBody');

                if (list.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 40px; color: #999;">暂无数据</td></tr>';
                    return;
                }

                const html = list.map(item => `
                    <tr>
                        <td>${item.promotion_user_id}</td>
                        <td>${item.user_id}</td>
                        <td>${item.time}</td>
                        <td>${item.ip}</td>
                        <td>
                            <span class="layui-badge ${getActionStatusClass(item.status)}">${item.status}</span>
                        </td>
                        <td>${item.douyin_name}</td>
                        <td>${item.douyin_id}</td>
                        <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                            title="${item.ck}">${item.ck}</td>
                    </tr>
                `).join('');

                tbody.innerHTML = html;
            }

            // 获取操作状态样式类
            function getActionStatusClass(status) {
                switch (status) {
                    case '登录成功': return 'layui-bg-green';
                    case '登录失败': return 'layui-bg-red';
                    case '请求过期': return 'layui-bg-orange';
                    case '扫码': return 'layui-bg-blue';
                    default: return '';
                }
            }

            // 渲染操作记录分页
            function renderActionsPagination(total, current) {
                if (total <= actionsPageSize) {
                    document.getElementById('actionsPagination').innerHTML = '';
                    return;
                }

                laypage.render({
                    elem: 'actionsPagination',
                    count: total,
                    limit: actionsPageSize,
                    curr: current,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            loadAllUsersActions(obj.curr);
                        }
                    }
                });
            }

            // 导出统计数据
            window.exportStats = function () {
                const date = document.getElementById('statDate').value;
                window.open(`/api/promotion/admin/export-data?type=stats&date=${date}`);
            }

            // 导出操作记录
            window.exportActions = function () {
                const date = document.getElementById('actionDate').value;
                const promotionUserId = document.getElementById('promotionUserFilter').value;
                const actionType = document.getElementById('actionTypeFilter').value;

                let url = `/api/promotion/admin/export-data?type=actions&date=${date}`;
                if (promotionUserId) url += `&promotion_user_id=${promotionUserId}`;
                if (actionType) url += `&action_type=${actionType}`;

                window.open(url);
            }
        });
    </script>
</body>

</html>