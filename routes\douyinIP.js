const express = require("express");
const router = express.Router();
const mysql = require("mysql2/promise");
const axios = require("axios");
const { SocksProxyAgent } = require("socks-proxy-agent");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456", // MySQL密码
  database: "douyin",
  charset: "utf8mb4",
  connectionLimit: 10,
  waitForConnections: true,
  queueLimit: 0,
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 初始化数据库和表
async function initDatabase() {
  try {
    const connection = await pool.getConnection();

    // 创建ip表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS ip (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sk5 VARCHAR(255) NOT NULL COMMENT 'SOCKS5代理地址',
        ip VARCHAR(255) NOT NULL COMMENT 'IP地址',
        province VARCHAR(100) NOT NULL COMMENT '所在省份',
        usage_count INT DEFAULT 0 COMMENT '使用次数',
        status TINYINT DEFAULT 1 COMMENT '1=正常, 0=异常, 2=禁用',
        last_used TIMESTAMP NULL COMMENT '最后使用时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_ip (ip),
        INDEX idx_province (province),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SOCKS5代理IP表'
    `);
    console.log("ip表已创建或已存在");

    connection.release();
  } catch (error) {
    console.error("初始化ip表失败:", error);
  }
}

// 初始化数据库和表
initDatabase();

/**
 * 查询IP地理位置信息
 * @param {string} ip - IP地址
 * @returns {Promise<string>} 省份名称
 */
async function queryIPLocation(ip) {
  console.log("🌍 开始查询IP地理位置信息:", ip);

  // 多个地理位置查询服务（按优先级排序）
  const locationServices = [
    {
      name: "ip9.com.cn (中国专用)",
      url: `https://ip9.com.cn/get?ip=${ip}`,
      parseResponse: (data) => {
        if (data.ret === 200 && data.data) {
          const province = data.data.prov;
          const city = data.data.city;
          const country = data.data.country;

          console.log(
            `📍 ip9.com.cn 详细信息: 国家=${country}, 省份=${province}, 城市=${city}`
          );

          // 优先返回省份，如果没有省份则返回城市或国家
          return province || city || country || null;
        }
        return null;
      },
    },
    {
      name: "ipapi.co",
      url: `https://ipapi.co/${ip}/json/`,
      parseResponse: (data) => {
        const region = data.region;
        const country = data.country_name;

        // 如果是中国，尝试转换英文省份名为中文
        if (country === "China" && region) {
          const chineseProvince = englishToChineseProvince(region);
          return chineseProvince || region;
        }

        return region || country || null;
      },
    },
    {
      name: "ip-api.com (中文)",
      url: `https://ip-api.com/json/${ip}?lang=zh-CN`,
      parseResponse: (data) => {
        if (data.status === "success") {
          return data.regionName || data.country || null;
        }
        return null;
      },
    },
    {
      name: "ipinfo.io",
      url: `https://ipinfo.io/${ip}/json`,
      parseResponse: (data) => {
        const region = data.region;
        const country = data.country;

        // 如果是中国，尝试转换
        if (country === "CN" && region) {
          const chineseProvince = englishToChineseProvince(region);
          return chineseProvince || region;
        }

        return region || country || null;
      },
    },
    {
      name: "ip-api.com (英文)",
      url: `https://ip-api.com/json/${ip}`,
      parseResponse: (data) => {
        if (data.status === "success") {
          const region = data.regionName;
          const country = data.country;

          // 如果是中国，尝试转换英文省份名为中文
          if (country === "China" && region) {
            const chineseProvince = englishToChineseProvince(region);
            return chineseProvince || region;
          }

          return region || country || null;
        }
        return null;
      },
    },
  ];

  // 尝试各个服务
  for (const service of locationServices) {
    try {
      console.log(`🌐 尝试服务: ${service.name}`);

      const response = await axios.get(service.url, {
        timeout: 8000,
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        },
      });

      console.log(`📡 ${service.name} 响应:`, response.data);

      const province = service.parseResponse(response.data);

      if (province) {
        console.log(`✅ 通过 ${service.name} 查询到省份: ${province}`);
        return province;
      }
    } catch (error) {
      console.log(`❌ ${service.name} 查询失败:`, error.message);
      continue;
    }
  }

  console.log("❌ 所有地理位置查询服务都失败了");
  return "未知";
}

/**
 * 英文省份名转中文
 * @param {string} englishName - 英文省份名
 * @returns {string|null} 中文省份名
 */
function englishToChineseProvince(englishName) {
  if (!englishName || typeof englishName !== "string") return null;

  const englishToChinese = {
    // 直辖市
    Beijing: "北京",
    Tianjin: "天津",
    Shanghai: "上海",
    Chongqing: "重庆",

    // 省份
    Hebei: "河北",
    Shanxi: "山西",
    "Inner Mongolia": "内蒙古",
    "Nei Mongol": "内蒙古",
    Liaoning: "辽宁",
    Jilin: "吉林",
    Heilongjiang: "黑龙江",
    Jiangsu: "江苏",
    Zhejiang: "浙江",
    Anhui: "安徽",
    Fujian: "福建",
    Jiangxi: "江西",
    Shandong: "山东",
    Henan: "河南",
    Hubei: "湖北",
    Hunan: "湖南",
    Guangdong: "广东",
    Guangxi: "广西",
    Hainan: "海南",
    Sichuan: "四川",
    Guizhou: "贵州",
    Yunnan: "云南",
    Tibet: "西藏",
    Xizang: "西藏",
    Shaanxi: "陕西",
    Gansu: "甘肃",
    Qinghai: "青海",
    Ningxia: "宁夏",
    Xinjiang: "新疆",

    // 特别行政区
    "Hong Kong": "香港",
    Macau: "澳门",
    Macao: "澳门",
    Taiwan: "台湾",

    // 常见变体
    Guangzhou: "广东", // 广州市 -> 广东省
    Shenzhen: "广东", // 深圳市 -> 广东省
    Hangzhou: "浙江", // 杭州市 -> 浙江省
    Nanjing: "江苏", // 南京市 -> 江苏省
    Wuhan: "湖北", // 武汉市 -> 湖北省
    Chengdu: "四川", // 成都市 -> 四川省
    "Xi'an": "陕西", // 西安市 -> 陕西省
    Qingdao: "山东", // 青岛市 -> 山东省
    Dalian: "辽宁", // 大连市 -> 辽宁省
    Xiamen: "福建", // 厦门市 -> 福建省
    Suzhou: "江苏", // 苏州市 -> 江苏省
    Dongguan: "广东", // 东莞市 -> 广东省
    Foshan: "广东", // 佛山市 -> 广东省
    Zhongshan: "广东", // 中山市 -> 广东省
    Zhuhai: "广东", // 珠海市 -> 广东省
    Jinan: "山东", // 济南市 -> 山东省
    Harbin: "黑龙江", // 哈尔滨市 -> 黑龙江省
    Changchun: "吉林", // 长春市 -> 吉林省
    Shenyang: "辽宁", // 沈阳市 -> 辽宁省
    Kunming: "云南", // 昆明市 -> 云南省
    Lanzhou: "甘肃", // 兰州市 -> 甘肃省
    Urumqi: "新疆", // 乌鲁木齐市 -> 新疆
    Lhasa: "西藏", // 拉萨市 -> 西藏
    Hohhot: "内蒙古", // 呼和浩特市 -> 内蒙古
    Yinchuan: "宁夏", // 银川市 -> 宁夏
    Xining: "青海", // 西宁市 -> 青海
  };

  // 直接匹配
  const directMatch = englishToChinese[englishName];
  if (directMatch) {
    return directMatch;
  }

  // 模糊匹配（忽略大小写）
  const lowerName = englishName.toLowerCase();
  for (const [english, chinese] of Object.entries(englishToChinese)) {
    if (english.toLowerCase() === lowerName) {
      return chinese;
    }
  }

  // 部分匹配（包含关系）
  for (const [english, chinese] of Object.entries(englishToChinese)) {
    if (englishName.includes(english) || english.includes(englishName)) {
      return chinese;
    }
  }

  return null;
}

/**
 * 自动检测SOCKS5代理的真实IP地址和省份
 * @param {string} sk5Data - SK5代理数据，格式：代理服务器地址:端口:用户名:密码:过期时间
 * @returns {Promise<{success: boolean, ip?: string, province?: string, error?: string}>}
 */
async function detectProxyIP(sk5Data) {
  try {
    console.log("🔍 开始检测代理IP:", sk5Data);

    // 验证SK5数据格式
    if (!sk5Data || typeof sk5Data !== "string") {
      throw new Error("SK5数据格式无效");
    }

    // 清理SK5数据
    const cleanSK5 = sk5Data.trim();

    // 解析SK5格式：支持冒号(:)和管道符(|)分隔
    // 格式1：代理服务器地址:端口:用户名:密码:过期时间
    // 格式2：代理服务器地址|端口|用户名|密码|过期时间
    let parts;
    let separator;

    if (cleanSK5.includes("|")) {
      parts = cleanSK5.split("|");
      separator = "|";
      console.log("📋 检测到管道符分隔格式");
    } else {
      parts = cleanSK5.split(":");
      separator = ":";
      console.log("📋 检测到冒号分隔格式");
    }

    let host, port, username, password, expireTime;
    let proxyUrl;

    if (parts.length === 5) {
      // 标准5段式格式：host:port:username:password:expireTime
      [host, port, username, password, expireTime] = parts;

      console.log("📋 解析SK5格式:", {
        host,
        port,
        username: username ? "***" : "(空)",
        password: password ? "***" : "(空)",
        expireTime,
      });

      // 验证必要字段
      if (!host || !port) {
        throw new Error("代理服务器地址或端口不能为空");
      }

      const portNum = parseInt(port);
      if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
        throw new Error("端口号无效，应为1-65535之间的数字");
      }

      // 检查过期时间
      if (expireTime) {
        const expireTimestamp = parseInt(expireTime);
        if (!isNaN(expireTimestamp)) {
          const currentTime = Math.floor(Date.now() / 1000);
          if (expireTimestamp < currentTime) {
            console.log(
              "⚠️  代理已过期:",
              new Date(expireTimestamp * 1000).toLocaleString()
            );
            // 不阻止检测，只是警告
          }
        }
      }

      // 构造SOCKS5 URL
      if (username && password) {
        proxyUrl = `socks5://${username}:${password}@${host}:${port}`;
      } else {
        proxyUrl = `socks5://${host}:${port}`;
      }
    } else if (parts.length === 2) {
      // 简单格式：host:port（兼容旧格式）
      [host, port] = parts;

      console.log("📋 解析简单格式:", { host, port });

      if (!host || !port) {
        throw new Error("主机名或端口号不能为空");
      }

      const portNum = parseInt(port);
      if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
        throw new Error("端口号无效，应为1-65535之间的数字");
      }

      proxyUrl = `socks5://${host}:${port}`;
    } else if (cleanSK5.includes("@")) {
      // 兼容@格式：username:password@host:port
      const atParts = cleanSK5.split("@");
      if (atParts.length !== 2) {
        throw new Error(
          "带认证的代理地址格式错误，应为: username:password@host:port"
        );
      }

      const [auth, hostPort] = atParts;
      const authParts = auth.split(":");
      const hostParts = hostPort.split(":");

      if (authParts.length !== 2 || hostParts.length !== 2) {
        throw new Error("代理认证信息格式错误");
      }

      [username, password] = authParts;
      [host, port] = hostParts;

      if (!username || !password || !host || !port) {
        throw new Error("代理认证信息不完整");
      }

      proxyUrl = `socks5://${username}:${password}@${host}:${port}`;
    } else {
      throw new Error(`SK5格式错误。支持的格式：
        1. 标准格式（冒号分隔）：代理服务器地址:端口:用户名:密码:过期时间
        2. 标准格式（管道分隔）：代理服务器地址|端口|用户名|密码|过期时间
        3. 简单格式：代理服务器地址:端口
        4. 认证格式：用户名:密码@代理服务器地址:端口
        当前格式使用 ${separator} 分隔，有 ${parts.length} 段，无法识别`);
    }

    console.log("🔗 代理URL:", proxyUrl);

    // 创建SOCKS代理agent
    let agent;
    try {
      agent = new SocksProxyAgent(proxyUrl);
      console.log("✅ SOCKS代理agent创建成功");
    } catch (agentError) {
      console.error("❌ 创建SOCKS代理agent失败:", agentError.message);
      throw new Error(`创建代理连接失败: ${agentError.message}`);
    }

    // 设置超时时间
    const timeout = 15000; // 15秒超时

    // 使用多个IP检测服务，提高成功率
    const ipServices = [
      "https://api.ipify.org?format=json",
      "https://httpbin.org/ip",
      "https://api.myip.com",
      "https://ipapi.co/json/",
      "https://ip-api.com/json/",
    ];

    let detectedIP = null;
    let detectedProvince = null;
    let lastError = null;

    // 尝试多个服务
    for (const service of ipServices) {
      try {
        console.log(`🌐 尝试服务: ${service}`);

        const response = await axios.get(service, {
          httpsAgent: agent,
          httpAgent: agent,
          timeout: timeout,
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
          },
        });

        console.log(`📡 服务响应:`, response.data);

        // 解析不同服务的响应格式
        if (service.includes("ipify.org")) {
          detectedIP = response.data.ip;
        } else if (service.includes("httpbin.org")) {
          detectedIP = response.data.origin;
        } else if (service.includes("myip.com")) {
          detectedIP = response.data.ip;
          detectedProvince = response.data.country;
        } else if (service.includes("ipapi.co")) {
          detectedIP = response.data.ip;
          detectedProvince = response.data.region || response.data.country_name;
        } else if (service.includes("ip-api.com")) {
          detectedIP = response.data.query;
          detectedProvince = response.data.regionName || response.data.country;
        }

        if (detectedIP) {
          console.log(
            `✅ 成功检测到IP: ${detectedIP}, 省份: ${
              detectedProvince || "未知"
            }`
          );
          break;
        }
      } catch (serviceError) {
        console.log(`❌ 服务 ${service} 失败:`, serviceError.message);
        lastError = serviceError;
        continue;
      }
    }

    if (!detectedIP) {
      return {
        success: false,
        error: `所有IP检测服务都失败了。最后错误: ${
          lastError?.message || "未知错误"
        }`,
      };
    }

    // 如果没有检测到省份，尝试通过IP地址查询
    if (!detectedProvince) {
      detectedProvince = await queryIPLocation(detectedIP);
    }

    return {
      success: true,
      ip: detectedIP,
      province: detectedProvince || "未知",
    };
  } catch (error) {
    console.error("❌ 检测代理IP时发生错误:", error);
    return {
      success: false,
      error: error.message,
    };
  }
}

// 省份拼音映射表
const PROVINCE_PINYIN_MAP = {
  // 直辖市
  beijing: "北京",
  tianjin: "天津",
  shanghai: "上海",
  chongqing: "重庆",

  // 省份
  hebei: "河北",
  shanxi: "山西",
  neimenggu: "内蒙古",
  neimeng: "内蒙古",
  liaoning: "辽宁",
  jilin: "吉林",
  heilongjiang: "黑龙江",
  jiangsu: "江苏",
  zhejiang: "浙江",
  anhui: "安徽",
  fujian: "福建",
  jiangxi: "江西",
  shandong: "山东",
  henan: "河南",
  hubei: "湖北",
  hunan: "湖南",
  guangdong: "广东",
  guangxi: "广西",
  hainan: "海南",
  sichuan: "四川",
  guizhou: "贵州",
  yunnan: "云南",
  xizang: "西藏",
  shaanxi: "陕西",
  gansu: "甘肃",
  qinghai: "青海",
  ningxia: "宁夏",
  xinjiang: "新疆",

  // 特别行政区
  xianggang: "香港",
  aomen: "澳门",
  taiwan: "台湾",

  // 常见简称
  jing: "北京",
  jin: "天津",
  hu: "上海",
  yu: "重庆",
  ji: "吉林",
  su: "江苏",
  zhe: "浙江",
  wan: "安徽",
  min: "福建",
  gan: "江西",
  lu: "山东",
  yu2: "河南",
  e: "湖北",
  xiang: "湖南",
  yue: "广东",
  gui: "广西",
  qiong: "海南",
  chuan: "四川",
  qian: "贵州",
  dian: "云南",
  zang: "西藏",
  qin: "陕西",
  long: "甘肃",
  qing: "青海",
  ning: "宁夏",
  xin: "新疆",
};

// 拼音转汉字函数
function pinyinToHanzi(input) {
  if (!input || typeof input !== "string") return input;

  const lowerInput = input.toLowerCase().trim();

  // 直接匹配
  if (PROVINCE_PINYIN_MAP[lowerInput]) {
    return PROVINCE_PINYIN_MAP[lowerInput];
  }

  // 模糊匹配（去掉省、市、自治区等后缀）
  const cleanInput = lowerInput
    .replace(/sheng$/, "") // 去掉"省"
    .replace(/shi$/, "") // 去掉"市"
    .replace(/zizhiqu$/, "") // 去掉"自治区"
    .replace(/qu$/, ""); // 去掉"区"

  if (PROVINCE_PINYIN_MAP[cleanInput]) {
    return PROVINCE_PINYIN_MAP[cleanInput];
  }

  // 如果没有匹配到，返回原始输入
  return input;
}

// 状态映射函数
function getStatusValue(statusStr) {
  const statusMap = {
    active: 1,
    inactive: 0,
    disabled: 2,
  };
  return statusMap[statusStr] !== undefined ? statusMap[statusStr] : statusStr;
}

function getStatusText(statusValue) {
  const statusMap = {
    1: "active",
    0: "inactive",
    2: "disabled",
  };
  return statusMap[statusValue] || "unknown";
}

// 获取所有代理IP
router.get("/proxies", async (req, res) => {
  try {
    const { page = 1, limit = 20, search, status, province } = req.query;

    // 构建查询条件
    let whereClause = "WHERE 1=1";
    let queryParams = [];

    if (search) {
      whereClause += " AND (sk5 LIKE ? OR ip LIKE ?)";
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    if (status) {
      whereClause += " AND status = ?";
      queryParams.push(getStatusValue(status));
    }

    if (province) {
      whereClause += " AND province LIKE ?";
      queryParams.push(`%${province}%`);
    }

    // 分页查询
    const offset = (page - 1) * limit;
    const proxiesQuery = `
      SELECT id, sk5, ip, province, usage_count, status, last_used, created_at, updated_at
      FROM ip
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    queryParams.push(parseInt(limit), offset);

    const [proxies] = await pool.execute(proxiesQuery, queryParams);

    // 转换状态为字符串
    const formattedProxies = proxies.map((proxy) => ({
      ...proxy,
      status: getStatusText(proxy.status),
    }));

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM ip ${whereClause}`;
    const [countResult] = await pool.execute(
      countQuery,
      queryParams.slice(0, -2)
    );
    const total = countResult[0].total;

    // 统计信息
    const [statsResult] = await pool.execute(`
      SELECT 
        status,
        COUNT(*) as count,
        SUM(usage_count) as total_usage
      FROM ip 
      GROUP BY status
    `);

    const statsObj = {
      total: total,
      active: 0,
      inactive: 0,
      disabled: 0,
      totalUsage: 0,
    };

    statsResult.forEach((stat) => {
      const statusText = getStatusText(stat.status);
      statsObj[statusText] = stat.count;
      statsObj.totalUsage += stat.total_usage || 0;
    });

    res.json({
      code: 0,
      msg: "获取成功",
      data: formattedProxies,
      stats: statsObj,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("获取代理列表失败:", error);
    res.json({ code: -1, msg: "获取代理列表失败: " + error.message });
  }
});

// 添加代理IP（支持自动获取IP）
router.post("/proxies", async (req, res) => {
  try {
    let { sk5, ip, province, usage_count, status, auto_detect_ip } = req.body;

    // 验证必填字段
    if (!sk5) {
      return res.json({ code: -1, msg: "SOCKS5地址为必填字段" });
    }

    // 如果启用自动检测IP或者没有提供IP地址，则自动获取
    if (auto_detect_ip || !ip) {
      console.log("🔍 自动检测SOCKS5代理的真实IP地址:", sk5);

      try {
        const detectedInfo = await detectProxyIP(sk5);

        if (detectedInfo.success) {
          ip = detectedInfo.ip;
          province = detectedInfo.province || province || "未知";
          console.log("✅ 自动检测成功:", { ip, province });
        } else {
          console.log("❌ 自动检测失败:", detectedInfo.error);

          // 如果自动检测失败且没有手动提供IP，则返回错误
          if (!req.body.ip) {
            return res.json({
              code: -1,
              msg: `无法自动获取代理IP: ${detectedInfo.error}。请手动输入IP地址。`,
            });
          }

          // 使用手动提供的IP
          ip = req.body.ip;
        }
      } catch (detectError) {
        console.error("自动检测IP时发生错误:", detectError);

        // 如果没有手动提供IP，则返回错误
        if (!req.body.ip) {
          return res.json({
            code: -1,
            msg: `自动检测IP失败: ${detectError.message}。请手动输入IP地址。`,
          });
        }

        // 使用手动提供的IP
        ip = req.body.ip;
      }
    }

    // 验证最终的必填字段
    if (!ip || !province) {
      return res.json({ code: -1, msg: "IP地址和省份为必填字段" });
    }

    // 检查是否已存在相同的代理
    const [existingProxies] = await pool.execute(
      "SELECT id FROM ip WHERE sk5 = ? OR ip = ?",
      [sk5, ip]
    );

    if (existingProxies.length > 0) {
      return res.json({ code: -1, msg: "代理地址或IP已存在" });
    }

    // 插入新代理
    const statusValue = getStatusValue(status || "active");
    const [result] = await pool.execute(
      "INSERT INTO ip (sk5, ip, province, usage_count, status) VALUES (?, ?, ?, ?, ?)",
      [sk5, ip, province, parseInt(usage_count) || 0, statusValue]
    );

    // 获取新插入的代理信息
    const [newProxy] = await pool.execute(
      "SELECT id, sk5, ip, province, usage_count, status, last_used, created_at, updated_at FROM ip WHERE id = ?",
      [result.insertId]
    );

    // 转换状态为字符串
    const formattedProxy = {
      ...newProxy[0],
      status: getStatusText(newProxy[0].status),
    };

    res.json({
      code: 0,
      msg:
        auto_detect_ip || !req.body.ip
          ? "代理添加成功（已自动检测IP）"
          : "代理添加成功",
      data: formattedProxy,
    });
  } catch (error) {
    console.error("添加代理失败:", error);
    res.json({ code: -1, msg: "添加代理失败: " + error.message });
  }
});

// 更新代理IP
router.put("/proxies/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const { sk5, ip, province, usage_count, status } = req.body;

    // 验证必填字段
    if (!sk5 || !ip || !province) {
      return res.json({ code: -1, msg: "sk5、ip和province为必填字段" });
    }

    // 检查代理是否存在
    const [existingProxy] = await pool.execute(
      "SELECT id FROM ip WHERE id = ?",
      [id]
    );

    if (existingProxy.length === 0) {
      return res.json({ code: -1, msg: "代理不存在" });
    }

    // 检查是否有重复的代理地址或IP（排除当前记录）
    const [duplicateProxies] = await pool.execute(
      "SELECT id FROM ip WHERE (sk5 = ? OR ip = ?) AND id != ?",
      [sk5, ip, id]
    );

    if (duplicateProxies.length > 0) {
      return res.json({ code: -1, msg: "代理地址或IP已存在" });
    }

    // 更新代理信息
    const statusValue = getStatusValue(status);
    await pool.execute(
      "UPDATE ip SET sk5 = ?, ip = ?, province = ?, usage_count = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
      [sk5, ip, province, parseInt(usage_count) || 0, statusValue, id]
    );

    // 获取更新后的代理信息
    const [updatedProxy] = await pool.execute(
      "SELECT id, sk5, ip, province, usage_count, status, last_used, created_at, updated_at FROM ip WHERE id = ?",
      [id]
    );

    // 转换状态为字符串
    const formattedProxy = {
      ...updatedProxy[0],
      status: getStatusText(updatedProxy[0].status),
    };

    res.json({
      code: 0,
      msg: "代理更新成功",
      data: formattedProxy,
    });
  } catch (error) {
    console.error("更新代理失败:", error);
    res.json({ code: -1, msg: "更新代理失败: " + error.message });
  }
});

// 删除代理IP
router.delete("/proxies/:id", async (req, res) => {
  try {
    const { id } = req.params;

    // 检查代理是否存在
    const [existingProxy] = await pool.execute(
      "SELECT id, sk5 FROM ip WHERE id = ?",
      [id]
    );

    if (existingProxy.length === 0) {
      return res.json({ code: -1, msg: "代理不存在" });
    }

    // 删除代理
    await pool.execute("DELETE FROM ip WHERE id = ?", [id]);

    res.json({
      code: 0,
      msg: "代理删除成功",
    });
  } catch (error) {
    console.error("删除代理失败:", error);
    res.json({ code: -1, msg: "删除代理失败: " + error.message });
  }
});

// 批量导入代理IP
router.post("/proxies/batch-import", async (req, res) => {
  try {
    const { proxies } = req.body;

    if (!Array.isArray(proxies) || proxies.length === 0) {
      return res.json({ code: -1, msg: "请提供有效的代理数据" });
    }

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    for (let i = 0; i < proxies.length; i++) {
      const proxyData = proxies[i];

      try {
        // 验证必填字段
        if (!proxyData.sk5 || !proxyData.ip || !proxyData.province) {
          errors.push(`第${i + 1}行：缺少必填字段`);
          errorCount++;
          continue;
        }

        // 检查是否已存在
        const [existingProxies] = await pool.execute(
          "SELECT id FROM ip WHERE sk5 = ? OR ip = ?",
          [proxyData.sk5, proxyData.ip]
        );

        if (existingProxies.length > 0) {
          errors.push(`第${i + 1}行：代理地址或IP已存在`);
          errorCount++;
          continue;
        }

        // 插入新代理
        const statusValue = getStatusValue(proxyData.status || "active");
        await pool.execute(
          "INSERT INTO ip (sk5, ip, province, usage_count, status) VALUES (?, ?, ?, ?, ?)",
          [
            proxyData.sk5,
            proxyData.ip,
            proxyData.province,
            parseInt(proxyData.usage_count) || 0,
            statusValue,
          ]
        );

        successCount++;
      } catch (error) {
        console.error(`导入第${i + 1}行失败:`, error);
        errors.push(`第${i + 1}行：${error.message}`);
        errorCount++;
      }
    }

    res.json({
      code: 0,
      msg: `批量导入完成：成功${successCount}个，失败${errorCount}个`,
      data: {
        successCount,
        errorCount,
        errors: errors.slice(0, 10), // 只返回前10个错误
      },
    });
  } catch (error) {
    console.error("批量导入失败:", error);
    res.json({ code: -1, msg: "批量导入失败: " + error.message });
  }
});

// 测试代理连接
router.post("/proxies/:id/test", async (req, res) => {
  try {
    const { id } = req.params;

    // 检查代理是否存在
    const [existingProxy] = await pool.execute(
      "SELECT id, sk5, ip, status FROM ip WHERE id = ?",
      [id]
    );

    if (existingProxy.length === 0) {
      return res.json({ code: -1, msg: "代理不存在" });
    }

    const proxy = existingProxy[0];

    // 模拟测试代理连接（实际项目中应该实现真实的代理测试）
    const isSuccess = Math.random() > 0.3; // 70%成功率
    const newStatus = isSuccess ? 1 : 0; // 1=正常, 0=异常

    // 更新代理状态和最后使用时间
    if (isSuccess) {
      await pool.execute(
        "UPDATE ip SET status = ?, last_used = CURRENT_TIMESTAMP, usage_count = usage_count + 1 WHERE id = ?",
        [newStatus, id]
      );
    } else {
      await pool.execute("UPDATE ip SET status = ? WHERE id = ?", [
        newStatus,
        id,
      ]);
    }

    res.json({
      code: 0,
      msg: isSuccess ? "代理连接正常" : "代理连接失败",
      data: {
        success: isSuccess,
        status: getStatusText(newStatus),
        proxy: proxy.sk5,
      },
    });
  } catch (error) {
    console.error("测试代理失败:", error);
    res.json({ code: -1, msg: "测试代理失败: " + error.message });
  }
});

// 批量测试代理
router.post("/proxies/batch-test", async (req, res) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      // 如果没有指定ID，测试所有非禁用的代理
      const [allProxies] = await pool.execute(
        "SELECT id FROM ip WHERE status != 2"
      );

      if (allProxies.length === 0) {
        return res.json({ code: -1, msg: "没有可测试的代理" });
      }

      ids = allProxies.map((p) => p.id);
    }

    let successCount = 0;
    let failureCount = 0;

    for (const id of ids) {
      try {
        // 模拟测试
        const isSuccess = Math.random() > 0.3; // 70%成功率
        const newStatus = isSuccess ? 1 : 0;

        if (isSuccess) {
          await pool.execute(
            "UPDATE ip SET status = ?, last_used = CURRENT_TIMESTAMP, usage_count = usage_count + 1 WHERE id = ?",
            [newStatus, id]
          );
          successCount++;
        } else {
          await pool.execute("UPDATE ip SET status = ? WHERE id = ?", [
            newStatus,
            id,
          ]);
          failureCount++;
        }
      } catch (error) {
        console.error(`测试代理ID ${id} 失败:`, error);
        failureCount++;
      }
    }

    res.json({
      code: 0,
      msg: `批量测试完成：成功${successCount}个，失败${failureCount}个`,
      data: {
        total: ids.length,
        successCount,
        failureCount,
      },
    });
  } catch (error) {
    console.error("批量测试失败:", error);
    res.json({ code: -1, msg: "批量测试失败: " + error.message });
  }
});

// 外部API：根据省份获取可用IP代理
router.get("/get-proxy-by-province", async (req, res) => {
  try {
    const { province, limit = 1 } = req.query;

    // 验证必填参数
    if (!province) {
      return res.json({
        code: -1,
        msg: "省份参数不能为空",
        data: null,
      });
    }

    // 处理拼音输入，转换为汉字
    const originalProvince = province;
    const chineseProvince = pinyinToHanzi(province);

    console.log(
      `省份查询: 原始输入="${originalProvince}", 转换后="${chineseProvince}"`
    );

    // 查询指定省份的可用代理（状态为1=正常）
    // 同时支持原始输入和转换后的汉字进行模糊匹配
    const [proxies] = await pool.execute(
      `SELECT id, sk5, ip, province, usage_count, last_used, created_at
       FROM ip
       WHERE (province LIKE ? OR province LIKE ?) AND status = 1
       ORDER BY usage_count ASC, last_used ASC
       LIMIT ?`,
      [`%${originalProvince}%`, `%${chineseProvince}%`, parseInt(limit)]
    );

    if (proxies.length === 0) {
      return res.json({
        code: -1,
        msg: "无可用账号",
        data: {
          error_type: "NO_AVAILABLE_PROXY",
          province_searched: originalProvince,
          province_converted: chineseProvince,
          reason: `省份"${originalProvince}"暂无可用代理IP`,
          suggestion: "请稍后重试或联系管理员添加该省份的代理",
          searched_at: new Date().toISOString(),
        },
      });
    }

    // 选择使用次数最少的代理
    const selectedProxy = proxies[0];

    // 先增加使用次数，更新最后使用时间
    await pool.execute(
      "UPDATE ip SET usage_count = usage_count + 1, last_used = CURRENT_TIMESTAMP WHERE id = ?",
      [selectedProxy.id]
    );

    // 检查使用次数是否达到10次，如果是则自动禁用
    const newUsageCount = selectedProxy.usage_count + 1;
    if (newUsageCount >= 10) {
      await pool.execute(
        "UPDATE ip SET status = 2, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        [selectedProxy.id]
      );
      console.log(`代理已自动禁用 - 使用次数达到10次: ${selectedProxy.sk5}`);
    }

    // 返回代理信息（包含更新后的使用次数）
    res.json({
      code: 0,
      msg: "获取代理成功",
      data: {
        id: selectedProxy.id,
        sk5: selectedProxy.sk5,
        ip: selectedProxy.ip,
        province: selectedProxy.province,
        usage_count: newUsageCount, // 返回更新后的使用次数
        status: newUsageCount >= 10 ? "disabled" : "active", // 返回当前状态
      },
    });
  } catch (error) {
    console.error("获取代理失败:", error);
    res.json({
      code: -1,
      msg: "获取代理失败: " + error.message,
      data: null,
    });
  }
});

// 外部API：批量获取指定省份的可用IP代理
router.get("/get-proxies-by-province", async (req, res) => {
  try {
    const { province, limit = 5 } = req.query;

    // 验证必填参数
    if (!province) {
      return res.json({
        code: -1,
        msg: "省份参数不能为空",
        data: [],
      });
    }

    // 处理拼音输入，转换为汉字
    const originalProvince = province;
    const chineseProvince = pinyinToHanzi(province);

    console.log(
      `批量省份查询: 原始输入="${originalProvince}", 转换后="${chineseProvince}"`
    );

    // 查询指定省份的可用代理（状态为1=正常）
    // 同时支持原始输入和转换后的汉字进行模糊匹配
    const [proxies] = await pool.execute(
      `SELECT id, sk5, ip, province, usage_count, last_used, created_at
       FROM ip
       WHERE (province LIKE ? OR province LIKE ?) AND status = 1
       ORDER BY usage_count ASC, last_used ASC
       LIMIT ?`,
      [`%${originalProvince}%`, `%${chineseProvince}%`, parseInt(limit)]
    );

    if (proxies.length === 0) {
      return res.json({
        code: -1,
        msg: "无可用账号",
        data: {
          error_type: "NO_AVAILABLE_PROXY",
          province_searched: originalProvince,
          province_converted: chineseProvince,
          reason: `省份"${originalProvince}"暂无可用代理IP`,
          suggestion: "请稍后重试或联系管理员添加该省份的代理",
          requested_limit: parseInt(limit),
          searched_at: new Date().toISOString(),
        },
      });
    }

    // 批量更新使用次数和最后使用时间
    const proxyIds = proxies.map((p) => p.id);
    await pool.execute(
      `UPDATE ip SET usage_count = usage_count + 1, last_used = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id IN (${proxyIds
        .map(() => "?")
        .join(",")})`,
      proxyIds
    );

    // 检查是否有代理达到使用上限并自动禁用
    for (const proxy of proxies) {
      const newUsageCount = proxy.usage_count + 1;
      if (newUsageCount >= 10) {
        await pool.execute(
          "UPDATE ip SET status = 2, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
          [proxy.id]
        );
        console.log(`代理已自动禁用 - 使用次数达到10次: ${proxy.sk5}`);
      }
    }

    // 返回代理列表（包含更新后的使用次数）
    const result = proxies.map((proxy) => {
      const newUsageCount = proxy.usage_count + 1;
      return {
        id: proxy.id,
        sk5: proxy.sk5,
        ip: proxy.ip,
        province: proxy.province,
        usage_count: newUsageCount, // 返回更新后的使用次数
        status: newUsageCount >= 10 ? "disabled" : "active", // 返回当前状态
      };
    });

    res.json({
      code: 0,
      msg: `获取到${result.length}个可用代理`,
      data: result,
    });
  } catch (error) {
    console.error("批量获取代理失败:", error);
    res.json({
      code: -1,
      msg: "批量获取代理失败: " + error.message,
      data: [],
    });
  }
});

// 外部API：获取所有可用省份列表
router.get("/get-available-provinces", async (req, res) => {
  try {
    // 查询所有有可用代理的省份
    const [provinces] = await pool.execute(
      `SELECT province, COUNT(*) as count
       FROM ip
       WHERE status = 1
       GROUP BY province
       ORDER BY count DESC`
    );

    res.json({
      code: 0,
      msg: "获取省份列表成功",
      data: provinces.map((p) => ({
        province: p.province,
        available_count: p.count,
      })),
    });
  } catch (error) {
    console.error("获取省份列表失败:", error);
    res.json({
      code: -1,
      msg: "获取省份列表失败: " + error.message,
      data: [],
    });
  }
});

// 外部API：报告代理状态（用于外部程序反馈代理是否可用）
router.post("/report-proxy-status", async (req, res) => {
  try {
    const { proxy_id, sk5, status, error_msg } = req.body;

    // 验证参数
    if (!proxy_id && !sk5) {
      return res.json({
        code: -1,
        msg: "必须提供proxy_id或sk5参数",
      });
    }

    if (!["success", "failed"].includes(status)) {
      return res.json({
        code: -1,
        msg: "status参数必须为success或failed",
      });
    }

    // 根据ID或sk5查找代理
    let whereClause = "";
    let params = [];

    if (proxy_id) {
      whereClause = "WHERE id = ?";
      params = [proxy_id];
    } else {
      whereClause = "WHERE sk5 = ?";
      params = [sk5];
    }

    // 检查代理是否存在
    const [existingProxy] = await pool.execute(
      `SELECT id, sk5 FROM ip ${whereClause}`,
      params
    );

    if (existingProxy.length === 0) {
      return res.json({
        code: -1,
        msg: "代理不存在",
      });
    }

    // 更新代理状态和使用统计
    const newStatus = status === "success" ? 1 : 0; // 1=正常, 0=异常

    if (status === "success") {
      // 成功使用：只更新状态为正常，不再增加使用次数（因为获取时已经增加了）
      await pool.execute(
        `UPDATE ip SET status = ?, updated_at = CURRENT_TIMESTAMP ${whereClause}`,
        [newStatus, ...params]
      );
    } else {
      // 使用失败：更新状态为异常，并减少使用次数（因为实际没有成功使用）
      await pool.execute(
        `UPDATE ip SET status = ?, usage_count = GREATEST(usage_count - 1, 0), updated_at = CURRENT_TIMESTAMP ${whereClause}`,
        [newStatus, ...params]
      );
    }

    // 获取更新后的代理信息
    const [updatedProxy] = await pool.execute(
      `SELECT id, sk5, usage_count, status FROM ip ${whereClause}`,
      params
    );

    res.json({
      code: 0,
      msg: `代理状态更新成功: ${status}`,
      data: {
        proxy_id: existingProxy[0].id,
        sk5: existingProxy[0].sk5,
        status: status,
        error_msg: error_msg || null,
        usage_count: updatedProxy[0].usage_count, // 返回更新后的使用次数
        updated_status: updatedProxy[0].status, // 返回更新后的状态
      },
    });
  } catch (error) {
    console.error("报告代理状态失败:", error);
    res.json({
      code: -1,
      msg: "报告代理状态失败: " + error.message,
    });
  }
});

// 外部API：增加用户ckcount保存次数
router.post("/increase-user-ckcount", async (req, res) => {
  try {
    const { username } = req.body;

    // 验证必填参数
    if (!username) {
      return res.json({
        code: -1,
        msg: "用户名参数不能为空",
        data: null,
      });
    }

    // 验证用户名格式（可选：添加基本验证）
    if (typeof username !== "string" || username.trim().length === 0) {
      return res.json({
        code: -1,
        msg: "用户名格式无效",
        data: null,
      });
    }

    const trimmedUsername = username.trim();

    // 检查用户是否存在
    const [existingUser] = await pool.execute(
      "SELECT username, ckcount FROM user WHERE username = ?",
      [trimmedUsername]
    );

    if (existingUser.length === 0) {
      return res.json({
        code: -1,
        msg: `用户"${trimmedUsername}"不存在`,
        data: null,
      });
    }

    const currentCkcount = existingUser[0].ckcount || 0;

    // 增加ckcount保存次数
    await pool.execute(
      "UPDATE user SET ckcount = ckcount + 1 WHERE username = ?",
      [trimmedUsername]
    );

    // 获取更新后的ckcount
    const [updatedUser] = await pool.execute(
      "SELECT username, ckcount FROM user WHERE username = ?",
      [trimmedUsername]
    );

    const newCkcount = updatedUser[0].ckcount;

    console.log(
      `用户ckcount更新: ${trimmedUsername} - ${currentCkcount} -> ${newCkcount}`
    );

    res.json({
      code: 0,
      msg: "用户ckcount增加成功",
      data: {
        username: trimmedUsername,
        previous_ckcount: currentCkcount,
        current_ckcount: newCkcount,
        increased_by: 1,
        updated_at: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("增加用户ckcount失败:", error);
    res.json({
      code: -1,
      msg: "增加用户ckcount失败: " + error.message,
      data: null,
    });
  }
});

// 外部API：获取用户ckcount信息
router.get("/get-user-ckcount", async (req, res) => {
  try {
    const { username } = req.query;

    // 验证必填参数
    if (!username) {
      return res.json({
        code: -1,
        msg: "用户名参数不能为空",
        data: null,
      });
    }

    const trimmedUsername = username.trim();

    // 查询用户信息
    const [user] = await pool.execute(
      "SELECT username, ckcount FROM user WHERE username = ?",
      [trimmedUsername]
    );

    if (user.length === 0) {
      return res.json({
        code: -1,
        msg: `用户"${trimmedUsername}"不存在`,
        data: null,
      });
    }

    res.json({
      code: 0,
      msg: "获取用户ckcount成功",
      data: {
        username: user[0].username,
        ckcount: user[0].ckcount || 0,
        queried_at: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("获取用户ckcount失败:", error);
    res.json({
      code: -1,
      msg: "获取用户ckcount失败: " + error.message,
      data: null,
    });
  }
});

// 外部API：批量增加多个用户的ckcount
router.post("/batch-increase-ckcount", async (req, res) => {
  try {
    const { usernames, increment = 1 } = req.body;

    // 验证必填参数
    if (!usernames || !Array.isArray(usernames) || usernames.length === 0) {
      return res.json({
        code: -1,
        msg: "用户名列表不能为空，必须是数组格式",
        data: null,
      });
    }

    // 验证增加数量
    const incrementValue = parseInt(increment);
    if (isNaN(incrementValue) || incrementValue <= 0) {
      return res.json({
        code: -1,
        msg: "增加数量必须是正整数",
        data: null,
      });
    }

    const results = [];
    let successCount = 0;
    let failCount = 0;

    // 逐个处理用户
    for (const username of usernames) {
      try {
        const trimmedUsername = username.trim();

        // 检查用户是否存在
        const [existingUser] = await pool.execute(
          "SELECT username, ckcount FROM user WHERE username = ?",
          [trimmedUsername]
        );

        if (existingUser.length === 0) {
          results.push({
            username: trimmedUsername,
            success: false,
            error: "用户不存在",
            previous_ckcount: null,
            current_ckcount: null,
          });
          failCount++;
          continue;
        }

        const currentCkcount = existingUser[0].ckcount || 0;

        // 增加ckcount
        await pool.execute(
          "UPDATE user SET ckcount = ckcount + ? WHERE username = ?",
          [incrementValue, trimmedUsername]
        );

        const newCkcount = currentCkcount + incrementValue;

        results.push({
          username: trimmedUsername,
          success: true,
          error: null,
          previous_ckcount: currentCkcount,
          current_ckcount: newCkcount,
          increased_by: incrementValue,
        });
        successCount++;

        console.log(
          `批量更新ckcount: ${trimmedUsername} - ${currentCkcount} -> ${newCkcount}`
        );
      } catch (userError) {
        results.push({
          username: username,
          success: false,
          error: userError.message,
          previous_ckcount: null,
          current_ckcount: null,
        });
        failCount++;
      }
    }

    res.json({
      code: 0,
      msg: `批量处理完成，成功${successCount}个，失败${failCount}个`,
      data: {
        total_processed: usernames.length,
        success_count: successCount,
        fail_count: failCount,
        increment_value: incrementValue,
        results: results,
        processed_at: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("批量增加用户ckcount失败:", error);
    res.json({
      code: -1,
      msg: "批量增加用户ckcount失败: " + error.message,
      data: null,
    });
  }
});

// 测试代理IP检测功能的API
router.post("/test-proxy-detection", async (req, res) => {
  try {
    const { sk5 } = req.body;

    console.log("🧪 收到代理IP检测请求:", {
      sk5: sk5,
      bodyKeys: Object.keys(req.body),
      contentType: req.headers["content-type"],
    });

    if (!sk5) {
      console.log("❌ SOCKS5地址为空");
      return res.json({
        code: -1,
        msg: "SOCKS5地址不能为空",
        data: null,
      });
    }

    console.log("🔍 开始检测代理IP:", sk5);

    const result = await detectProxyIP(sk5);

    console.log("📡 检测结果:", result);

    res.json({
      code: result.success ? 0 : -1,
      msg: result.success ? "检测成功" : `检测失败: ${result.error}`,
      data: result.success
        ? {
            ip: result.ip,
            province: result.province,
            proxy: sk5,
          }
        : null,
    });
  } catch (error) {
    console.error("❌ 测试代理IP检测异常:", error);
    console.error("错误堆栈:", error.stack);
    res.json({
      code: -1,
      msg: "测试失败: " + error.message,
      data: null,
    });
  }
});

module.exports = router;
