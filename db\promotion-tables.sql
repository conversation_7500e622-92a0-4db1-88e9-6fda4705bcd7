-- 推广用户管理系统数据库表结构
-- 1. 推广用户表
CREATE TABLE IF NOT EXISTS promotion_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL UNIQUE COMMENT '推广用户ID，如1001',
    username VARCHAR(100) NOT NULL COMMENT '推广用户名',
    password VARCHAR(255) NOT NULL COMMENT '推广用户密码',
    promotion_link VARCHAR(500) NOT NULL COMMENT '推广链接',
    status TINYINT DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '推广用户表';
-- 2. 推广访问记录表
CREATE TABLE IF NOT EXISTS promotion_visits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    promotion_user_id VARCHAR(50) NOT NULL COMMENT '推广用户ID',
    visitor_ip VARCHAR(45) NOT NULL COMMENT '访问者IP',
    user_agent TEXT COMMENT '用户代理',
    referer VARCHAR(500) COMMENT '来源页面',
    visit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
    INDEX idx_promotion_user_id (promotion_user_id),
    INDEX idx_visitor_ip (visitor_ip),
    INDEX idx_visit_time (visit_time),
    INDEX idx_daily_stats (promotion_user_id, visit_time)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '推广访问记录表';
-- 3. 推广操作记录表
CREATE TABLE IF NOT EXISTS promotion_actions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    promotion_user_id VARCHAR(50) NOT NULL COMMENT '推广用户ID',
    user_id VARCHAR(100) COMMENT '操作用户ID',
    action_type ENUM(
        'scan',
        'login_success',
        'login_fail',
        'request_expire'
    ) NOT NULL COMMENT '操作类型',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
    douyin_name VARCHAR(200) COMMENT '抖音名',
    douyin_id VARCHAR(100) COMMENT '抖音号',
    ck_data TEXT COMMENT 'CK数据（仅管理员可见）',
    user_agent TEXT COMMENT '用户代理',
    action_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    extra_data JSON COMMENT '额外数据',
    INDEX idx_promotion_user_id (promotion_user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_ip_address (ip_address),
    INDEX idx_action_time (action_time),
    INDEX idx_daily_stats (promotion_user_id, action_time)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '推广操作记录表';
-- 4. 推广统计汇总表（按天统计）
CREATE TABLE IF NOT EXISTS promotion_daily_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    promotion_user_id VARCHAR(50) NOT NULL COMMENT '推广用户ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    visit_count INT DEFAULT 0 COMMENT '访问次数',
    unique_ip_count INT DEFAULT 0 COMMENT '独立IP数',
    scan_count INT DEFAULT 0 COMMENT '扫码数量',
    success_count INT DEFAULT 0 COMMENT '成功数量',
    fail_count INT DEFAULT 0 COMMENT '失败数量',
    expire_count INT DEFAULT 0 COMMENT '过期数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_date (promotion_user_id, stat_date),
    INDEX idx_stat_date (stat_date)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '推广每日统计表';
-- 5. 管理员用户表（如果不存在）
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE COMMENT '管理员用户名',
    password VARCHAR(255) NOT NULL COMMENT '管理员密码',
    role ENUM('admin', 'super_admin') DEFAULT 'admin' COMMENT '角色',
    status TINYINT DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '管理员用户表';
-- 插入默认管理员账户
INSERT INTO admin_users (username, password, role)
VALUES ('admin', '123456', 'super_admin') ON DUPLICATE KEY
UPDATE password =
VALUES(password);
-- 插入示例推广用户
INSERT INTO promotion_users (user_id, username, password, promotion_link)
VALUES (
        '1001',
        'promoter1001',
        '123456',
        'http://localhost:15001?id=1001'
    ),
    (
        '1002',
        'promoter1002',
        '123456',
        'http://localhost:15001?id=1002'
    ) ON DUPLICATE KEY
UPDATE username =
VALUES(username),
    password =
VALUES(password),
    promotion_link =
VALUES(promotion_link);