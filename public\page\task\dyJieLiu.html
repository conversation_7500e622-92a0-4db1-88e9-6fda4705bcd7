<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>


<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input
                            id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                




                <fieldset>
                    <legend>【截流】功能</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">上传文件</label>
                        <div class="layui-input-block">
                            <input type="file" id="fileInput" class="layui-btn" accept=".txt" />
                            <div id="filePreview" style="margin-top: 10px;"></div> <!-- 用于显示文件预览或错误信息 -->
                            <textarea id="contentInput" name="评论内容" placeholder="评论内容将显示在这里"
                                class="layui-textarea"></textarea>
                            <tip></tip>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">搜索关键词</label>
                            <div class="layui-input-inline" style="width: 200px;">
                                <input type="text" name="搜索关键词" placeholder="关键词用于搜索话题使用"
                                    class="layui-input" />
                            </div>
                            <tip></tip>

                            <label class="layui-form-label">话题顺序</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" name="话题顺序" value="1" autocomplete="off" class="layui-input"/>
                            </div>
                        </div>
                    </div>
                        
                        
                    

                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="截流任务" lay-filter="tijiao">截流任务</button>
                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止</button>
                </fieldset>


            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong
            tongYong.tongYong1()
        });
        // 监听文件选择
        document.getElementById('fileInput').addEventListener('change', function(event) {
                var file = event.target.files[0];
                if (!file) {
                    console.log('没有选择文件');
                    return; // 没有选择文件，直接返回
                }
            
                // 这里我们假设只处理文本文件，但请注意，前端无法准确判断文件内容类型
                // 通常，我们应该通过文件的MIME类型或扩展名来猜测，但这仍然不是完全可靠的
                // 这里我们只是为了示例而跳过这个检查
                console.log(1);
                var reader = new FileReader();
            
                reader.onload = function(e) {
                    var fileContent = e.target.result;
                    var lines = fileContent.split(/\r\n|\n/); // 按行分割字符串

                    var contentInput = document.getElementById('contentInput');
                    contentInput.value =lines.join(','); // 这将把所有行用换行符连接起来
                    //document.getElementById('filePreview').innerHTML = '文件内容预览（示例）:' + fileContent.substr(0, 100) + '...';
                };
            
                reader.onerror = function() {
                    document.getElementById('filePreview').innerHTML = '文件读取失败。';
                };
            
                // 读取文件内容为文本
                reader.readAsText(file);
            });
    </script>
</body>

</html>