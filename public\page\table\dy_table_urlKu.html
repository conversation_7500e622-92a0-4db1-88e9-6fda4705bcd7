<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">

</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">

            <script type="text/html" id="toolbarDemo">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="add"> 添加链接 </button>
                    <button class="layui-btn layui-btn-sm layui-btn-danger data-delete-btn" lay-event="delList"> 删除勾选 </button>
        
                    <button class="layui-btn layui-btn-sm layui-btn-danger data-delete-btn" lay-event="delAll"> 全部删除 </button>
                </div>
            </script>
            <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
            <script type="text/html" id="currentTableBar">
            <!-- <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="edit">编辑</a> -->
            <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="deleteOne">删除</a>
            </script>

        </div>
    </div>
    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script>
        layui.use(['form', 'table'], function () {
            var $ = layui.jquery,
                form = layui.form,
                table = layui.table;

            let tableIns = table.render({
                elem: '#currentTableId',
                url: '/url_table',
                toolbar: '#toolbarDemo',
                defaultToolbar: ['filter', 'exports', 'print', {
                    title: '提示',
                    layEvent: 'LAYTABLE_TIPS',
                    icon: 'layui-icon-tips'
                }],
                cols: [[
                    { type: "checkbox", width: 50 },
                    { field: 'url', minWidth: 100, title: '链接', sort: true },
                    { field: 'creatDate', minWidth: 100, title: '上传时间', sort: true },
                    { title: '操作', minWidth: 60, toolbar: '#currentTableBar', align: "center" }
                ]],
                limits: [50, 200, 1000, 2000, 5000],
                limit: 50,
                page: true,
                // skin: 'line'
            });


            /**
             * toolbar监听事件
             */
            table.on('toolbar(currentTableFilter)', function (obj) {
                // let data = obj.data;
                if (obj.event === 'add') {  // 监听删除操作
                    layer.prompt({
                        formType: 2,
                        value: "",
                        title: "一行一条",
                        maxlength: 500000,
                        area: ['800px', '300px'] //自定义文本域宽高
                    }, function (value, index, elem) {
                        layer.close(index);
                        if (!value || value == "") {
                            return
                        }
                        const dataArr = value.split("\n").map(item => item.replace(/\s/g, "")).filter(item => item && item.includes("https://"))
                        if (dataArr.length == 0) {
                            return
                        }
                        tableIns.reload({
                            url: '/url_table/add',
                            method: 'post',
                            where: {
                                dataArr: dataArr
                            }
                        })
                    });
                } else if (obj.event === 'delList') {  // 监听批量删除操作
                    const checkStatus = table.checkStatus('currentTableId')
                    const data = checkStatus.data;
                    if (data.length == 0) {
                        return
                    }
                    layer.confirm('真的删除这些数据么?', function (index) {
                        let idArr = []
                        data.forEach(d => {
                            idArr.push(d._id)
                        })
                        tableIns.reload({
                            url: '/url_table/delList',
                            method: 'post',
                            where: {
                                idArr: idArr
                            }
                        })
                        layer.close(index);
                    });
                } else if (obj.event == "delAll") {
                    layer.confirm('真的全部删除么?', function (index) {
                        tableIns.reload({
                            url: '/url_table/delList',
                            method: 'post',
                            where: {
                                type: "all"
                            }
                        })
                        layer.close(index);
                    });
                }
            });

            // //监听表格复选框选择
            // table.on('checkbox(currentTableFilter)', function (obj) {
            // console.log(obj)
            // });

            // form.on('submit(data-search-btn)', function (data) {
            //     let datas = data.field
            //     console.log(datas);
            //     tableIns.reload({
            //         url: '/url_table',
            //         method: 'get',
            //         where: {
            //             guanJianZi: datas.guanJianZi + "",
            //         }
            //     })

            //     return false;
            // })

            table.on('tool(currentTableFilter)', function (obj) {
                const data = obj.data
                // if (obj.event === 'edit') {
                //     layer.prompt({
                //         formType: 2,
                //         value: data.ip,
                //         title: '更改数据:【' + data.ip + "】",
                //         area: ['800px', '300px'] //自定义文本域宽高
                //     }, function (value, index, elem) {
                //         layer.close(index);
                //         if (data.huaShu == value) {
                //             layer.msg("未修改或无效输入")
                //             return
                //         }

                //         tableIns.reload({
                //             url: '/url_table/edit',
                //             method: 'post',
                //             where: {
                //                 _id: data._id,
                //                 ip: value
                //             }
                //         })
                //     });
                //     // return false;
                // } else
                if (obj.event === 'deleteOne') {
                    layer.confirm('真的删除【' + data.url + '】么?', function (index) {
                        obj.del();
                        tableIns.reload({
                            url: '/url_table/delOne',
                            method: 'post',
                            where: {
                                _id: data._id
                            }
                        })
                        layer.close(index);
                    });
                }
            });

        });
    </script>

</body>

</html>