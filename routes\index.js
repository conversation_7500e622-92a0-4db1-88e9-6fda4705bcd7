const userMod = require('../db/userMod');
const express = require('express');
const router = express.Router();
const debug = require('debug')('a-sokio-yun:server');
const crypto = require('crypto');



let multiparty = require("multiparty")
let fs = require("fs")


// const fs = require('fs');
const { publicPath, ppxxxkkk, getServerUrl, serverPort, appName } = require('../serverOpt');

router.get('/getServerUrl', function (req, res, next) {
    res.send(getServerUrl().replace("http://", "") + ":" + serverPort);
});
router.get('/getAppName', function (req, res, next) {
    res.send(appName);
});

// 判断用户是否登录 是则跳转首页 否则跳转登录
router.get('/', function (req, res) {
    // debug("直接去首页");
    // return res.sendFile(publicPath + '/jinzhu.html');
    // res.redirect("/views/user/login.html")
    // res.redirect("/views/index备份.html")
    // res.redirect("index原版.html")
    // debug("来了主页老弟req.session", req.session);
    // debug("来了主页老弟req.headers", req.headers);
    // debug("来了主页t老弟req.query", req.query);
    if (req.session && req.session.userName) {
        res.sendFile(publicPath + '/jinzhu.html');
    } else {
        res.sendFile(publicPath + '/page/login/login.html');
    }
});

router.get('/login', function (req, res) {
    if (req.session && req.session.userName) {
        res.sendFile(publicPath + '/jinzhu.html');
    } else {
        res.sendFile(publicPath + '/page/login/login.html');
    }
});



router.post('/login', async function (req, res) {
    // debug("登录接口session", req.session);
    // debug("登录接口headers", req.headers);
    // debug("登录接口body", req.body);


    // return res.json({
    //     code: 0,
    //     msg: "登录成功"
    // })


    try {
        const { txtUserName, txtUserPwd } = req.body
        const userDoc = await userMod.findOne({ userName: txtUserName });
        if (userDoc) {
            // debug("用户信息", userDoc);
            if (userDoc.userPass.encryptedData == encryptByIv(userDoc.userPass, txtUserPwd)) {
                req.session.userName = txtUserName;
                req.session.userId = userDoc._id;
                req.session.userLevel = userDoc.userLevel;
                return res.json({
                    code: 0,
                    msg: "登录成功",
                    data: {
                        userName: txtUserName,
                        userId: userDoc._id,
                        userLevel: userDoc.userLevel,
                    }
                })
            } else {
                throw new Error("账号或密码验证失败.");
            }
        } else {
            throw new Error("账号或密码验证失败");
        }
    } catch (error) {
        debug("登录错误:", error.message);
        res.json({
            code: -1,
            msg: "登录失败:" + error.message,
            data: null,
        })
    }
});



router.post('/storage/getconf', function (req, res) {
    res.json({

        code: 200,
        data: {
            DownloadTarUrl: "http://************:15001/tars",
            UploadTarUrl: "http://************:15001/uploadTar",
            msg: ""
        }
    })
});

router.post('/trillck/checkdn', function (req, res) {
    res.json({
        code: 200
    })
});


router.post('/trillck/addone', function (req, res) {
    res.json({
        code: 200
    })
});


router.post('/storage/getconf', function (req, res) {
    res.json({
        code: 200,
        data: {
            DownloadTarUrl: "http://************:15001/tars",
            UploadTarUrl: "http://************:15001/uploadTar",
            msg: ""
        }
    })
});



// 通用文件上传接口
router.post('/uploads', function (req, res) {
    const form = new multiparty.Form();
    form.uploadDir = publicPath + "/uploads";
    if (!fs.existsSync(form.uploadDir)) {
        fs.mkdirSync(form.uploadDir, { recursive: true });
    }
    form.parse(req, function (err, fields, files) {
        if (err) {
            return res.status(500).json({ 
                code: -1,
                msg: "上传失败: " + err.message 
            });
        }
        
        const inputFile = files.file[0];
        const newPath = form.uploadDir + "/" + inputFile.originalFilename;
        
        fs.rename(inputFile.path, newPath, (err) => {
            if (err) {
                return res.status(500).json({ 
                    code: -1,
                    msg: "文件保存失败: " + err.message 
                });
            }
            res.json({
                code: 200,
                msg: "上传成功",
                path: "/uploads/" + inputFile.originalFilename
            });
        });
    });
});

// 上传文件并更新version文件版本信息
router.post('/uploadTar', function (req, res) {
    // console.log(req.headers);
    // console.log(req.body);
    let form = new multiparty.Form();
    // 设置文件存储路径，以当前编辑的文件为相对路径
    form.uploadDir = publicPath + "/tars";
    if (!fs.existsSync(publicPath + "/tars")) {
        fs.mkdirSync(publicPath + "/tars");
    }
    // 设置文件大小限制
    form.maxFilesSize = 2000 * 1024 * 1024;

    form.parse(req, function (err, fields, files) {
        // console.log(files);
        let inputFile = files.file[0];
        let newPath = form.uploadDir + "/" + inputFile.originalFilename;
        fs.renameSync(inputFile.path, newPath);
        res.json({
            code: 200,
            msg: "成功,版本"
        })
    });
})



router.post('/logout', function (req, res) {
    debug("退出登录之前req.session", req.session);
    req.session.destroy();
    debug("退出登录之后req.session", req.session);
    res.json({ code: 0, msg: "登出成功" })
});


function encryptByIv(pwdDoc, newPwdStr) {
    const iv = Buffer.from(pwdDoc.iv, 'hex');
    // console.log(iv);
    const cipher = crypto.createCipheriv('aes-256-cbc', ppxxxkkk, iv);
    let encrypted = cipher.update(newPwdStr, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
}




module.exports = router;