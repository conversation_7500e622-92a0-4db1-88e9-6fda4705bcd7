<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>


<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input
                            id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>



                <fieldset>
                    <legend>【DY下单】功能</legend>


                    <div class="layui-form-item">
                        <label class="layui-form-label">订单留言</label>
                        <div class="layui-input-block">
                            <textarea name="订单留言" placeholder="例如:请发韵达,其他不要发"
                                class="layui-textarea">请发韵达,其他不要发</textarea>
                            <tip></tip>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">关键字</label>
                            <div class="layui-input-inline" style="width: 300px;">
                                <input type="text" name="关键字" value="液体|电子" autocomplete="off" class="layui-input" />
                            </div>
                            <div class="layui-form-mid layui-word-aux">至少要有一个,多个以|分割</div>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">价格区间</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" name="最低价格区间" value="0.6" autocomplete="off" class="layui-input" />
                            </div>
                            <div class="layui-form-mid">到</div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" name="最高价格区间" value="3" autocomplete="off" class="layui-input" />
                            </div>
                            <div class="layui-form-mid layui-word-aux">元</div>
                        </div>
                    </div>




                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">付款后超出</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" name="付款后超出" value="3" autocomplete="off" class="layui-input" />
                            </div>
                            <div class="layui-form-mid layui-word-aux">比如这个商品9毛,付款后这个商品超过7毛钱,那么就退款!</div>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">支付密码</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="text" name="支付密码" value="895066" autocomplete="off" class="layui-input" />
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                    </div>



                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="DY下单" lay-filter="tijiao">DY下单</button>
                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止</button>
                </fieldset>


                <fieldset>
                    <legend>【清空店铺记录】功能</legend>

                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="清空店铺记录"
                        lay-filter="tijiao">清空店铺记录</button>
                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止</button>
                </fieldset>



            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong
            tongYong.tongYong1()
        });
    </script>
</body>

</html>