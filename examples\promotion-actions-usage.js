// 推广操作数据保存API使用示例

const axios = require('axios');

// API基础配置
const API_BASE_URL = 'http://localhost:15001';
const API_ENDPOINTS = {
  save: `${API_BASE_URL}/api/promotion-actions/save`,
  batchSave: `${API_BASE_URL}/api/promotion-actions/batch-save`
};

// 1. 单条数据保存示例
async function savePromotionAction() {
  try {
    const data = {
      promotion_user_id: "1001",
      action_type: "scan",
      ip_address: "*************",
      ip_province: "北京",
      douyin_name: "张三",
      douyin_id: "zhangsan123",
      ck_data: "sessionid=abc123456",
      user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
      extra_data: {
        device_type: "mobile",
        browser: "chrome",
        timestamp: Date.now()
      }
    };

    const response = await axios.post(API_ENDPOINTS.save, data, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data.code === 0) {
      console.log('✅ 保存成功:', response.data.data);
      return response.data.data;
    } else {
      console.error('❌ 保存失败:', response.data.msg);
      return null;
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
    return null;
  }
}

// 2. 批量数据保存示例
async function batchSavePromotionActions() {
  try {
    const data = {
      actions: [
        {
          promotion_user_id: "1001",
          action_type: "scan",
          ip_address: "*************"
        },
        {
          promotion_user_id: "1001",
          action_type: "login_success",
          ip_address: "*************",
          douyin_name: "张三",
          douyin_id: "zhangsan123",
          ck_data: "sessionid=abc123456"
        },
        {
          promotion_user_id: "1002",
          action_type: "login_fail",
          ip_address: "*************",
          douyin_name: "李四"
        }
      ]
    };

    const response = await axios.post(API_ENDPOINTS.batchSave, data, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data.code === 0) {
      console.log('✅ 批量保存成功:');
      console.log(`   成功: ${response.data.data.success_count}条`);
      console.log(`   失败: ${response.data.data.error_count}条`);
      
      if (response.data.data.errors.length > 0) {
        console.log('   错误详情:', response.data.data.errors);
      }
      
      return response.data.data;
    } else {
      console.error('❌ 批量保存失败:', response.data.msg);
      return null;
    }
  } catch (error) {
    console.error('❌ 批量保存请求失败:', error.message);
    return null;
  }
}

// 3. 不同操作类型的保存示例
async function saveAllActionTypes() {
  const actionTypes = [
    {
      type: "scan",
      description: "扫码操作",
      data: {
        promotion_user_id: "1001",
        action_type: "scan",
        ip_address: "*************"
      }
    },
    {
      type: "login_success",
      description: "登录成功",
      data: {
        promotion_user_id: "1001",
        action_type: "login_success",
        ip_address: "*************",
        douyin_name: "成功用户",
        douyin_id: "success123",
        ck_data: "sessionid=success123456"
      }
    },
    {
      type: "login_fail",
      description: "登录失败",
      data: {
        promotion_user_id: "1001",
        action_type: "login_fail",
        ip_address: "*************"
      }
    },
    {
      type: "request_expire",
      description: "请求过期",
      data: {
        promotion_user_id: "1001",
        action_type: "request_expire",
        ip_address: "*************"
      }
    }
  ];

  console.log('🔄 保存所有操作类型示例...');
  
  for (const actionType of actionTypes) {
    try {
      const response = await axios.post(API_ENDPOINTS.save, actionType.data);
      
      if (response.data.code === 0) {
        console.log(`✅ ${actionType.description} (${actionType.type}) 保存成功`);
      } else {
        console.log(`❌ ${actionType.description} (${actionType.type}) 保存失败: ${response.data.msg}`);
      }
    } catch (error) {
      console.log(`❌ ${actionType.description} (${actionType.type}) 请求失败: ${error.message}`);
    }
  }
}

// 4. 错误处理示例
async function handleErrors() {
  console.log('🔄 测试错误处理...');
  
  // 测试缺少必填字段
  try {
    const invalidData = {
      promotion_user_id: "1001",
      // 缺少 action_type 和 ip_address
    };
    
    const response = await axios.post(API_ENDPOINTS.save, invalidData);
    console.log('错误处理测试结果:', response.data);
  } catch (error) {
    console.log('请求错误:', error.message);
  }
}

// 5. 实际使用场景示例
class PromotionActionLogger {
  constructor(baseUrl = 'http://localhost:15001') {
    this.baseUrl = baseUrl;
    this.saveEndpoint = `${baseUrl}/api/promotion-actions/save`;
    this.batchSaveEndpoint = `${baseUrl}/api/promotion-actions/batch-save`;
  }

  // 记录扫码操作
  async logScan(promotionUserId, ipAddress, extraData = {}) {
    return await this.saveAction({
      promotion_user_id: promotionUserId,
      action_type: "scan",
      ip_address: ipAddress,
      extra_data: extraData
    });
  }

  // 记录登录成功
  async logLoginSuccess(promotionUserId, ipAddress, douyinName, douyinId, ckData) {
    return await this.saveAction({
      promotion_user_id: promotionUserId,
      action_type: "login_success",
      ip_address: ipAddress,
      douyin_name: douyinName,
      douyin_id: douyinId,
      ck_data: ckData
    });
  }

  // 记录登录失败
  async logLoginFail(promotionUserId, ipAddress, reason = '') {
    return await this.saveAction({
      promotion_user_id: promotionUserId,
      action_type: "login_fail",
      ip_address: ipAddress,
      extra_data: { fail_reason: reason }
    });
  }

  // 记录请求过期
  async logRequestExpire(promotionUserId, ipAddress) {
    return await this.saveAction({
      promotion_user_id: promotionUserId,
      action_type: "request_expire",
      ip_address: ipAddress
    });
  }

  // 通用保存方法
  async saveAction(actionData) {
    try {
      const response = await axios.post(this.saveEndpoint, actionData, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      return {
        success: response.data.code === 0,
        data: response.data.data,
        message: response.data.msg
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }

  // 批量保存方法
  async batchSaveActions(actionsArray) {
    try {
      const response = await axios.post(this.batchSaveEndpoint, {
        actions: actionsArray
      }, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      return {
        success: response.data.code === 0,
        data: response.data.data,
        message: response.data.msg
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message
      };
    }
  }
}

// 使用示例
async function main() {
  console.log('🚀 推广操作数据保存API使用示例');
  
  // 1. 基础使用
  console.log('\n1. 单条数据保存示例:');
  await savePromotionAction();
  
  // 2. 批量保存
  console.log('\n2. 批量数据保存示例:');
  await batchSavePromotionActions();
  
  // 3. 所有操作类型
  console.log('\n3. 所有操作类型示例:');
  await saveAllActionTypes();
  
  // 4. 错误处理
  console.log('\n4. 错误处理示例:');
  await handleErrors();
  
  // 5. 实际使用场景
  console.log('\n5. 实际使用场景示例:');
  const logger = new PromotionActionLogger();
  
  // 记录扫码
  const scanResult = await logger.logScan('1001', '*************', {
    device: 'mobile',
    timestamp: Date.now()
  });
  console.log('扫码记录结果:', scanResult);
  
  // 记录登录成功
  const loginResult = await logger.logLoginSuccess(
    '1001', 
    '*************', 
    '测试用户', 
    'test123', 
    'sessionid=test123456'
  );
  console.log('登录成功记录结果:', loginResult);
}

// 导出供其他模块使用
module.exports = {
  savePromotionAction,
  batchSavePromotionActions,
  saveAllActionTypes,
  PromotionActionLogger,
  API_ENDPOINTS
};

// 如果直接运行此文件，执行示例
if (require.main === module) {
  main().catch(console.error);
}
