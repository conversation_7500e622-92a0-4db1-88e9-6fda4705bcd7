<fieldset style="padding: 20px; border: 2px solid #ddd; border-radius: 10px; background-color: #f9f9f9;">
    <legend style="font-size: 18px; font-weight: bold;">执行统计表</legend>
    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
        <label for="startDate">开始日期：</label>
        <input type="date" id="startDate" style="padding: 5px; border-radius: 5px;">
        <label for="endDate">结束日期：</label>
        <input type="date" id="endDate" style="padding: 5px; border-radius: 5px;">
        <button onclick="fetchChartData()" style="padding: 5px 10px; border-radius: 5px; background-color: #4caf50; color: white; border: none; cursor: pointer;">查询</button>
    </div>

    <table class="layui-table" style="width: 100%; text-align: center; border-collapse: collapse;">
        <thead>
            <tr style="background-color: #e0e0e0;">
                <th>日期</th>
                <th>成功数</th>
                <th>失败数</th>
                <th>总执行数</th>
                <th>成功率</th>
            </tr>
        </thead>
        <tbody id="dataTableBody">
            <!-- 数据将通过 JS 动态插入 -->
        </tbody>
        <tfoot>
            <tr style="background-color: #f0f0f0; font-weight: bold;">
                <td>合计</td>
                <td id="totalSuccess">0</td>
                <td id="totalFailure">0</td>
                <td id="totalCount">0</td>
                <td id="successRate">0%</td>
            </tr>
        </tfoot>
    </table>

    <div id="chartContainer" style="width: 100%; height: 400px; margin-top: 20px;"></div>
</fieldset>

<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>

<script>
function fetchChartData() {
    const startDate = document.getElementById("startDate").value;
    const endDate = document.getElementById("endDate").value;
    if (!startDate || !endDate) {
        alert("请选择起始和结束日期！");
        return;
    }

    axios.get(`http://**************:3101/getExecutionSummaryByRange?startDate=${startDate}&endDate=${endDate}`)
        .then(response => {
            const rawData = response.data.data;
            let successData = [], failureData = [], formattedDates = [], tableRows = "";
            let totalSuccess = 0, totalFailure = 0;

            rawData.forEach(item => {
                let successCount = Number(item.successCount) || 0;
                let failureCount = Number(item.failureCount) || 0;
                let dailyTotal = successCount + failureCount;
                let successRate = dailyTotal ? ((successCount / dailyTotal) * 100).toFixed(2) + "%" : "0%";

                formattedDates.push(item.date.slice(5)); // MM-DD
                successData.push(successCount);
                failureData.push(failureCount);

                totalSuccess += successCount;
                totalFailure += failureCount;

                tableRows += `
                    <tr>
                        <td>${item.date}</td>
                        <td>${successCount}</td>
                        <td>${failureCount}</td>
                        <td>${dailyTotal}</td>
                        <td>${successRate}</td>
                    </tr>`;
            });

            let totalCount = totalSuccess + totalFailure;
            let totalSuccessRate = totalCount ? ((totalSuccess / totalCount) * 100).toFixed(2) + "%" : "0%";

            document.getElementById('dataTableBody').innerHTML = tableRows;
            document.getElementById('totalSuccess').innerText = totalSuccess;
            document.getElementById('totalFailure').innerText = totalFailure;
            document.getElementById('totalCount').innerText = totalCount;
            document.getElementById('successRate').innerText = totalSuccessRate;
            
            renderChart(formattedDates, successData, failureData, totalSuccess, totalFailure);
        })
        .catch(error => {
            console.error('获取数据失败:', error);
        });
}

function renderChart(dates, successData, failureData, totalSuccess, totalFailure) {
    const chartDom = document.getElementById('chartContainer');
    const myChart = echarts.init(chartDom);
    
    const option = {
        title: {
            text: `执行结果统计（总成功：${totalSuccess}，总失败：${totalFailure}）`,
            left: 'center'
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['成功数', '失败数'],
            bottom: 0
        },
        xAxis: {
            type: 'category',
            data: dates
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '成功数',
                type: 'bar',
                data: successData,
                itemStyle: { color: '#4caf50' }
            },
            {
                name: '失败数',
                type: 'bar',
                data: failureData,
                itemStyle: { color: '#f44336' }
            }
        ]
    };

    myChart.setOption(option);
}
</script>
