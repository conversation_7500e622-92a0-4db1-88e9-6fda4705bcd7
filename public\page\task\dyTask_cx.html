<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>layui</title>
    <meta name="renderer" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all" />
    <link rel="stylesheet" href="/css/public.css" media="all" />
  </head>
  <body>
    <div class="layuimini-container">
      <div class="layuimini-main">
        <fieldset style="border-color: blue;  border-width: 3px;  background-color: rgb(255, 252, 194);">
          <legend style="color: blue; font-weight: bold; font-size: 16px">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选)<input id="inputaaa" /></legend>
          <input type="checkbox" lay-filter="quanXuan1" title="全选" /><span style="margin-left: 3px;">全选</span>
          <br />
          <br />
          <div class="layui-form-item" id="groups1"></div>
        </fieldset>
        <button type="button" class="layui-btn" id="addTaskBtn" style="margin-top: 20px;margin-bottom: 20px;">添加任务</button>
        <!-- <button class="layui-btn" value="任务开始" lay-filter="tijiao">任务开始</button>
        <button class="layui-btn" value="任务停止" lay-filter="tijiao">任务结束</button> -->
        <table id="taskTable" lay-filter="taskTable"></table>
        <script type="text/html" id="toolbar">
          <a class="layui-btn layui-btn-xs" lay-event="startTask">开始任务</a>
          <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="endTask">结束任务</a>
          <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="deleteTask">删除任务</a>
        </script>        
      </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
      layui.use("dyTask", function () {
        var dyTask = layui.dyTask;
        dyTask.dyTask1();
      });
    </script>
  </body>
</html>
