const axios = require('axios');

// 简单测试自动检测功能
async function testSimpleDetection() {
  console.log('🧪 测试简化的代理IP检测功能...');
  
  const baseURL = 'http://localhost:15001';
  
  // 测试不同格式的代理地址
  const testCases = [
    {
      name: '无效格式 - 空字符串',
      proxy: '',
      expectError: true
    },
    {
      name: '无效格式 - 只有主机名',
      proxy: 'localhost',
      expectError: true
    },
    {
      name: '无效格式 - 无效端口',
      proxy: 'localhost:abc',
      expectError: true
    },
    {
      name: '本地代理 - 127.0.0.1:1080',
      proxy: '127.0.0.1:1080',
      expectError: false
    },
    {
      name: '本地代理 - localhost:1080',
      proxy: 'localhost:1080',
      expectError: false
    },
    {
      name: '带认证的代理',
      proxy: 'user:<EMAIL>:1080',
      expectError: false
    }
  ];
  
  console.log('\n📋 开始测试各种代理格式...\n');
  
  for (const testCase of testCases) {
    console.log(`🔍 测试: ${testCase.name}`);
    console.log(`   代理地址: ${testCase.proxy || '(空)'}`);
    
    try {
      const response = await axios.post(`${baseURL}/api/douyin/test-proxy-detection`, {
        sk5: testCase.proxy
      }, {
        timeout: 10000 // 10秒超时
      });
      
      if (testCase.expectError) {
        if (response.data.code === 0) {
          console.log('   ❌ 预期失败但成功了');
        } else {
          console.log(`   ✅ 预期失败: ${response.data.msg}`);
        }
      } else {
        if (response.data.code === 0) {
          console.log('   ✅ 检测成功:');
          console.log(`      IP: ${response.data.data.ip}`);
          console.log(`      省份: ${response.data.data.province}`);
        } else {
          console.log(`   ⚠️  检测失败: ${response.data.msg}`);
        }
      }
      
    } catch (error) {
      if (testCase.expectError) {
        console.log(`   ✅ 预期失败: ${error.message}`);
      } else {
        console.log(`   ❌ 意外失败: ${error.message}`);
        if (error.response) {
          console.log(`      状态码: ${error.response.status}`);
          console.log(`      响应: ${JSON.stringify(error.response.data)}`);
        }
      }
    }
    
    console.log(''); // 空行分隔
  }
  
  console.log('🎉 测试完成！');
  
  console.log('\n💡 使用说明:');
  console.log('1. 访问代理管理页面: http://localhost:15001/page/task/douyin514CKIP.html');
  console.log('2. 点击"添加代理"按钮');
  console.log('3. 输入SOCKS5地址（如: 127.0.0.1:1080）');
  console.log('4. 勾选"自动检测IP地址"');
  console.log('5. 点击"测试检测"按钮');
  console.log('6. 系统会自动填充IP地址和省份');
  
  console.log('\n⚠️  注意:');
  console.log('- 本地代理(127.0.0.1:1080)通常会检测到本机的公网IP');
  console.log('- 如果没有运行SOCKS5代理服务，检测会失败');
  console.log('- 检测过程可能需要15-30秒');
  console.log('- 可以使用SSH隧道或其他工具创建本地SOCKS5代理进行测试');
}

// 运行测试
if (require.main === module) {
  testSimpleDetection().catch(console.error);
}

module.exports = testSimpleDetection;
