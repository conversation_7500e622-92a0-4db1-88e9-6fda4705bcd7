const axios = require('axios');

// 测试新的SK5格式解析
async function testSK5Format() {
  console.log('🧪 测试新的SK5格式解析功能...');
  
  const baseURL = 'http://localhost:15001';
  
  // 测试各种SK5格式
  const testCases = [
    {
      name: '标准5段式格式（未过期）',
      sk5: '*************:1080:user:pass:9999999999',
      expectSuccess: true,
      description: '包含完整的代理信息和未来过期时间'
    },
    {
      name: '标准5段式格式（已过期）',
      sk5: '*************:1080:user:pass:1600000000',
      expectSuccess: true,
      description: '包含完整的代理信息但已过期（应该警告但不阻止）'
    },
    {
      name: '标准5段式格式（无认证）',
      sk5: '*************:1080:::1735689600',
      expectSuccess: true,
      description: '无用户名密码的5段式格式'
    },
    {
      name: '简单2段式格式',
      sk5: '127.0.0.1:1080',
      expectSuccess: true,
      description: '兼容旧的简单格式'
    },
    {
      name: '认证@格式',
      sk5: 'user:<EMAIL>:1080',
      expectSuccess: true,
      description: '兼容@符号认证格式'
    },
    {
      name: '域名5段式格式',
      sk5: 'proxy.example.com:8080:username:password:1735689600',
      expectSuccess: true,
      description: '使用域名的5段式格式'
    },
    {
      name: 'localhost 5段式格式',
      sk5: 'localhost:1080:testuser:testpass:1735689600',
      expectSuccess: true,
      description: '使用localhost的5段式格式'
    },
    {
      name: '无效格式（3段）',
      sk5: 'host:port:user',
      expectSuccess: false,
      description: '不支持的3段格式'
    },
    {
      name: '无效格式（4段）',
      sk5: 'host:port:user:pass',
      expectSuccess: false,
      description: '不支持的4段格式'
    },
    {
      name: '无效格式（6段）',
      sk5: 'host:port:user:pass:time:extra',
      expectSuccess: false,
      description: '不支持的6段格式'
    },
    {
      name: '空字符串',
      sk5: '',
      expectSuccess: false,
      description: '空的SK5数据'
    },
    {
      name: '无效端口',
      sk5: 'host:abc:user:pass:1735689600',
      expectSuccess: false,
      description: '端口号不是数字'
    },
    {
      name: '端口超出范围',
      sk5: 'host:99999:user:pass:1735689600',
      expectSuccess: false,
      description: '端口号超出有效范围'
    }
  ];
  
  console.log('\n📋 开始测试各种SK5格式...\n');
  
  let successCount = 0;
  let totalCount = testCases.length;
  
  for (const testCase of testCases) {
    console.log(`🔍 测试: ${testCase.name}`);
    console.log(`   SK5数据: "${testCase.sk5}"`);
    console.log(`   描述: ${testCase.description}`);
    console.log(`   预期结果: ${testCase.expectSuccess ? '成功' : '失败'}`);
    
    try {
      const response = await axios.post(`${baseURL}/api/douyin/test-proxy-detection`, {
        sk5: testCase.sk5
      }, {
        timeout: 15000
      });
      
      // 检查是否是格式错误
      const isFormatError = response.data.msg && (
        response.data.msg.includes('格式错误') ||
        response.data.msg.includes('格式无效') ||
        response.data.msg.includes('不能为空') ||
        response.data.msg.includes('端口号无效') ||
        response.data.msg.includes('无法识别') ||
        response.data.msg.includes('SK5格式错误')
      );
      
      if (testCase.expectSuccess) {
        if (isFormatError) {
          console.log(`   ❌ 预期成功但格式验证失败: ${response.data.msg}`);
        } else {
          console.log(`   ✅ 格式验证通过`);
          if (response.data.code === 0) {
            console.log(`      检测成功: IP=${response.data.data.ip}, 省份=${response.data.data.province}`);
          } else {
            console.log(`      格式正确但连接失败: ${response.data.msg}`);
          }
          successCount++;
        }
      } else {
        if (isFormatError) {
          console.log(`   ✅ 预期失败且正确失败: ${response.data.msg}`);
          successCount++;
        } else {
          console.log(`   ❌ 预期失败但格式验证通过`);
        }
      }
      
    } catch (error) {
      if (testCase.expectSuccess) {
        console.log(`   ❌ 预期成功但请求失败: ${error.message}`);
      } else {
        console.log(`   ✅ 预期失败且请求失败: ${error.message}`);
        successCount++;
      }
    }
    
    console.log(''); // 空行分隔
  }
  
  console.log(`🎉 测试完成！成功率: ${successCount}/${totalCount} (${Math.round(successCount/totalCount*100)}%)`);
  
  if (successCount === totalCount) {
    console.log('✅ 所有测试用例都通过了！SK5格式解析功能正常。');
  } else {
    console.log('⚠️  部分测试用例失败，需要进一步调整。');
  }
  
  console.log('\n💡 SK5格式说明:');
  console.log('   标准格式：代理服务器地址:端口:用户名:密码:过期时间');
  console.log('   示例：*************:1080:user:pass:1735689600');
  console.log('   - 代理服务器地址：IP地址或域名');
  console.log('   - 端口：1-65535之间的数字');
  console.log('   - 用户名：代理认证用户名（可为空）');
  console.log('   - 密码：代理认证密码（可为空）');
  console.log('   - 过期时间：Unix时间戳（秒）');
  
  console.log('\n🔧 兼容格式:');
  console.log('   - 简单格式：代理服务器地址:端口');
  console.log('   - 认证格式：用户名:密码@代理服务器地址:端口');
  
  console.log('\n🌐 现在可以在前端界面测试以下SK5格式:');
  console.log('   - *************:1080:user:pass:1735689600');
  console.log('   - localhost:1080:::1735689600');
  console.log('   - 127.0.0.1:1080');
  console.log('   - user:<EMAIL>:1080');
}

// 运行测试
if (require.main === module) {
  testSK5Format().catch(console.error);
}

module.exports = testSK5Format;
