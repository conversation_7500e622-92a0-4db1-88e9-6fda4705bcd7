const mysql = require("mysql2/promise");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
};

async function insertSampleData() {
  console.log("📊 插入推广操作示例数据...");

  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log("✅ 数据库连接成功");

    // 插入操作记录数据
    const actionData = [
      ["1001", "scan", "*************", "北京", null, null, null],
      [
        "1001",
        "login_success",
        "*************",
        "北京",
        "测试用户1",
        "dy123456",
        "sessionid=abc123",
      ],
      ["1001", "scan", "*************", "上海", null, null, null],
      ["1001", "login_fail", "*************", "上海", null, null, null],
      ["1002", "scan", "*************", "广东", null, null, null],
      [
        "1002",
        "login_success",
        "*************",
        "广东",
        "测试用户2",
        "dy789012",
        "sessionid=ghi789",
      ],
      ["1001", "request_expire", "192.168.1.103", "浙江", null, null, null],
      ["1003", "scan", "*************", "江苏", null, null, null],
      [
        "1003",
        "login_success",
        "*************",
        "江苏",
        "测试用户3",
        "dy345678",
        "sessionid=mno345",
      ],
      ["1002", "scan", "*************", "广东", null, null, null],
      ["1002", "login_fail", "*************", "广东", null, null, null],
    ];

    console.log("📋 插入操作记录数据...");

    for (const data of actionData) {
      try {
        await connection.execute(
          `INSERT INTO promotion_actions (promotion_user_id, action_type, ip_address, ip_province, douyin_name, douyin_id, ck_data)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          data
        );
        console.log(`✅ 插入操作记录: ${data[0]} - ${data[2]}`);
      } catch (error) {
        console.error(`❌ 插入失败:`, error.message);
      }
    }

    // 更新统计数据
    console.log("\n📊 更新用户统计数据...");

    await connection.execute(`
      UPDATE user SET 
        visit_count = (
          SELECT COUNT(*) FROM promotion_visits 
          WHERE promotion_user_id = user.promoter_id
        ),
        unique_ip_count = (
          SELECT COUNT(DISTINCT visitor_ip) FROM promotion_visits 
          WHERE promotion_user_id = user.promoter_id
        ),
        scan_count = (
          SELECT COUNT(*) FROM promotion_actions 
          WHERE promotion_user_id = user.promoter_id AND action_type = 'scan'
        ),
        success_count = (
          SELECT COUNT(*) FROM promotion_actions 
          WHERE promotion_user_id = user.promoter_id AND action_type = 'login_success'
        ),
        fail_count = (
          SELECT COUNT(*) FROM promotion_actions 
          WHERE promotion_user_id = user.promoter_id AND action_type = 'login_fail'
        ),
        expire_count = (
          SELECT COUNT(*) FROM promotion_actions 
          WHERE promotion_user_id = user.promoter_id AND action_type = 'request_expire'
        ),
        last_stat_update = NOW()
      WHERE user_type = 'promoter'
    `);

    console.log("✅ 统计数据更新完成");

    // 验证数据
    console.log("\n🔍 验证插入的数据...");

    const [actions] = await connection.execute(
      `SELECT promotion_user_id, action_type, ip_address, ip_province, douyin_name, douyin_id,
              DATE_FORMAT(action_time, '%Y-%m-%d %H:%i:%s') as action_time
       FROM promotion_actions
       ORDER BY action_time DESC`
    );

    console.log(`✅ 操作记录数据 (${actions.length} 条):`);
    actions.forEach((action, index) => {
      console.log(
        `${index + 1}. 推广用户${action.promotion_user_id} - ${
          action.action_type
        }`
      );
      console.log(
        `   目标用户: ${action.target_user_id || "-"}, IP: ${action.ip_address}`
      );
      console.log(
        `   抖音信息: ${action.douyin_name || "-"} (${action.douyin_id || "-"})`
      );
      console.log(`   时间: ${action.action_time}`);
    });

    // 验证统计数据
    const [stats] = await connection.execute(
      `SELECT promoter_id, username, visit_count, unique_ip_count, scan_count, success_count, fail_count, expire_count
       FROM user WHERE user_type = 'promoter' ORDER BY promoter_id`
    );

    console.log(`\n📊 更新后的统计数据:`);
    stats.forEach((stat) => {
      console.log(`${stat.promoter_id} - ${stat.username}:`);
      console.log(
        `  访问: ${stat.visit_count}, 独立IP: ${stat.unique_ip_count}, 扫码: ${stat.scan_count}`
      );
      console.log(
        `  成功: ${stat.success_count}, 失败: ${stat.fail_count}, 过期: ${stat.expire_count}`
      );
    });

    await connection.end();

    console.log("\n🎉 示例数据插入完成！");
    console.log("\n🔗 现在可以测试推广统计功能:");
    console.log(
      "- 推广用户管理: http://localhost:15001/page/promotion/admin-promotion-simple.html"
    );
  } catch (error) {
    console.error("💥 插入数据失败:", error.message);
    process.exit(1);
  }
}

insertSampleData();
