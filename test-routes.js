const express = require('express');
const app = express();

// 添加JSON解析中间件
app.use(express.json());

// 测试抖音账号路由
try {
  const douyinAccountRouter = require('./routes/douyinAccount');
  app.use('/api/douyin', douyinAccountRouter);
  console.log('✓ 抖音账号路由加载成功');
} catch (err) {
  console.error('✗ 抖音账号路由加载失败:', err.message);
}

// 启动测试服务器
const PORT = 3000;
app.listen(PORT, () => {
  console.log(`测试服务器启动在端口 ${PORT}`);
  console.log('可以测试的路由:');
  console.log('- GET http://localhost:3000/api/douyin/accounts');
  console.log('- POST http://localhost:3000/api/douyin/accounts');
  
  // 测试路由
  setTimeout(async () => {
    try {
      const axios = require('axios');
      const response = await axios.get('http://localhost:3000/api/douyin/accounts');
      console.log('✓ 路由测试成功:', response.data);
    } catch (err) {
      console.error('✗ 路由测试失败:', err.response?.data || err.message);
    }
    process.exit(0);
  }, 2000);
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ error: err.message });
});
