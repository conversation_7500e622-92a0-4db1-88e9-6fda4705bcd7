
    <!DOCTYPE html>
    <html>
    <head>
      <title>验证码: ZXCV</title>
      <style>
        body { 
          margin: 0; 
          padding: 20px; 
          font-family: Arial, sans-serif; 
          background: #f5f5f5;
        }
        .captcha-container {
          text-align: center;
          background: white;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          max-width: 300px;
          margin: 0 auto;
        }
        .captcha-image {
          border: 2px solid #ddd;
          border-radius: 4px;
          margin: 10px 0;
        }
        .info {
          color: #666;
          font-size: 14px;
          margin-top: 10px;
        }
      </style>
    </head>
    <body>
      <div class="captcha-container">
        <h2>验证码图片</h2>
        <div class="captcha-image">
          
    <svg width="100" height="40" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#F8F9FA"/>
      <line x1="39.23215944014877" y1="2.2853755963362232" x2="68.11687670093163" y2="33.095285651383826" stroke="#98D8C8" stroke-width="1" opacity="0.3"/><line x1="10.266519784434758" y1="26.889268873396908" x2="29.52533950060996" y2="34.12891980133071" stroke="#FF6B6B" stroke-width="1" opacity="0.3"/><line x1="90.6662005266529" y1="5.370554302459913" x2="26.691789089957574" y2="0.9793728647535538" stroke="#96CEB4" stroke-width="1" opacity="0.3"/>
      <circle cx="97.75912169654629" cy="1.5874149911547075" r="1" fill="#F7DC6F" opacity="0.5"/><circle cx="88.1602294887336" cy="35.617041468286395" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="79.77484816127718" cy="34.830591499512806" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="77.44989971118635" cy="0.13487170495231737" r="1" fill="#98D8C8" opacity="0.5"/><circle cx="33.96512918190575" cy="37.65436401737196" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="1.029187469451176" cy="35.36005292410827" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="12.884732609879235" cy="23.181585147069114" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="82.79872215329715" cy="37.49932410870599" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="31.3925119805994" cy="32.91053757340297" r="1" fill="#DDA0DD" opacity="0.5"/><circle cx="30.43222684440432" cy="5.486492040323414" r="1" fill="#F7DC6F" opacity="0.5"/><circle cx="19.696569214298123" cy="31.325168916819965" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="8.654077825908125" cy="28.641366158196533" r="1" fill="#FF6B6B" opacity="0.5"/><circle cx="93.32944477318075" cy="2.347445433102049" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="3.967242565178397" cy="32.337520954815695" r="1" fill="#DDA0DD" opacity="0.5"/><circle cx="12.100964325936436" cy="29.857154037312135" r="1" fill="#FF6B6B" opacity="0.5"/><circle cx="47.71222897369856" cy="5.456148510618402" r="1" fill="#98D8C8" opacity="0.5"/><circle cx="37.924963864228346" cy="28.403859629551622" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="60.82556693098966" cy="21.220872062094777" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="31.197543527722193" cy="32.28369192397605" r="1" fill="#DDA0DD" opacity="0.5"/><circle cx="44.24579154833883" cy="4.325155455031133" r="1" fill="#DDA0DD" opacity="0.5"/>
      
      <text x="20" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#45B7D1" 
            text-anchor="middle"
            transform="rotate(10.015374433818174 20 25.333333333333332)">
        Z
      </text>
      <text x="40" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#FFEAA7" 
            text-anchor="middle"
            transform="rotate(-12.450340346738727 40 25.333333333333332)">
        X
      </text>
      <text x="60" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#FFEAA7" 
            text-anchor="middle"
            transform="rotate(-4.8355049657083615 60 25.333333333333332)">
        C
      </text>
      <text x="80" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#F7DC6F" 
            text-anchor="middle"
            transform="rotate(0.8878157404097964 80 25.333333333333332)">
        V
      </text>
    </svg>
  
        </div>
        <div class="info">
          <strong>文件名:</strong> captcha5.jpg<br>
          <strong>验证码:</strong> ZXCV<br>
          <strong>说明:</strong> 请将此图片保存为 captcha5.jpg
        </div>
      </div>
    </body>
    </html>
  