# 抖音代理IP外部API文档

## 概述
本API提供给外部程序使用，用于获取和管理SOCKS5代理IP。所有API都连接到MySQL数据库 `douyin` 中的 `ip` 表。

## 基础信息
- **基础URL**: `http://localhost:15001/api/douyin`
- **数据格式**: JSON
- **字符编码**: UTF-8

## API接口列表

### 1. 根据省份获取单个可用代理
**接口地址**: `GET /get-proxy-by-province`

**功能**: 根据指定省份返回一个可用的SOCKS5代理IP，优先返回使用次数最少的代理。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| province | string | 是 | 省份名称，支持模糊匹配 |
| limit | int | 否 | 返回数量限制，默认为1 |

**请求示例**:
```
GET /api/douyin/get-proxy-by-province?province=北京市
GET /api/douyin/get-proxy-by-province?province=北京&limit=1
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "获取代理成功",
  "data": {
    "id": 2,
    "sk5": "************:1080",
    "ip": "************",
    "province": "北京市",
    "usage_count": 1
  }
}
```

**说明**:
- 自动选择使用次数最少的代理
- 调用后会自动增加该代理的使用次数
- 更新最后使用时间
- 只返回状态为正常(status=1)的代理

---

### 2. 批量获取指定省份的可用代理
**接口地址**: `GET /get-proxies-by-province`

**功能**: 批量获取指定省份的多个可用代理IP。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| province | string | 是 | 省份名称，支持模糊匹配 |
| limit | int | 否 | 返回数量限制，默认为5 |

**请求示例**:
```
GET /api/douyin/get-proxies-by-province?province=北京市&limit=3
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "获取到2个可用代理",
  "data": [
    {
      "id": 2,
      "sk5": "************:1080",
      "ip": "************",
      "province": "北京市",
      "usage_count": 2
    },
    {
      "id": 3,
      "sk5": "************:1080",
      "ip": "************",
      "province": "北京市",
      "usage_count": 3
    }
  ]
}
```

---

### 3. 获取所有可用省份列表
**接口地址**: `GET /get-available-provinces`

**功能**: 获取所有有可用代理的省份列表及其代理数量。

**请求参数**: 无

**请求示例**:
```
GET /api/douyin/get-available-provinces
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "获取省份列表成功",
  "data": [
    {
      "province": "北京市",
      "available_count": 2
    },
    {
      "province": "上海市",
      "available_count": 1
    },
    {
      "province": "广东省",
      "available_count": 1
    }
  ]
}
```

---

### 4. 报告代理状态
**接口地址**: `POST /report-proxy-status`

**功能**: 外部程序使用代理后，反馈代理的可用状态。

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| proxy_id | int | 否 | 代理ID（与sk5二选一） |
| sk5 | string | 否 | SOCKS5地址（与proxy_id二选一） |
| status | string | 是 | 状态：success(成功) 或 failed(失败) |
| error_msg | string | 否 | 错误信息（失败时提供） |

**请求示例**:
```json
POST /api/douyin/report-proxy-status
Content-Type: application/json

{
  "sk5": "************:1080",
  "status": "failed",
  "error_msg": "连接超时"
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "代理状态更新成功: failed",
  "data": {
    "proxy_id": 2,
    "sk5": "************:1080",
    "status": "failed",
    "error_msg": "连接超时"
  }
}
```

## 响应状态码说明

| code | 说明 |
|------|------|
| 0 | 成功 |
| -1 | 失败（参数错误、数据库错误等） |

## 数据库表结构

**表名**: `ip`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键，自增 |
| sk5 | VARCHAR(255) | SOCKS5代理地址 |
| ip | VARCHAR(255) | IP地址 |
| province | VARCHAR(100) | 所在省份 |
| usage_count | INT | 使用次数 |
| status | TINYINT | 状态：1=正常, 0=异常, 2=禁用 |
| last_used | TIMESTAMP | 最后使用时间 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 使用示例

### Python示例
```python
import requests

# 获取北京市的代理
response = requests.get('http://localhost:15001/api/douyin/get-proxy-by-province?province=北京市')
data = response.json()

if data['code'] == 0:
    proxy = data['data']
    print(f"获取到代理: {proxy['sk5']}")
    
    # 使用代理进行网络请求...
    
    # 报告代理状态
    status_data = {
        'proxy_id': proxy['id'],
        'status': 'success'  # 或 'failed'
    }
    requests.post('http://localhost:15001/api/douyin/report-proxy-status', json=status_data)
```

### JavaScript示例
```javascript
const axios = require('axios');

async function getProxy() {
    try {
        // 获取上海市的代理
        const response = await axios.get('http://localhost:15001/api/douyin/get-proxy-by-province?province=上海市');
        
        if (response.data.code === 0) {
            const proxy = response.data.data;
            console.log(`获取到代理: ${proxy.sk5}`);
            
            // 使用代理进行网络请求...
            
            // 报告代理状态
            await axios.post('http://localhost:15001/api/douyin/report-proxy-status', {
                sk5: proxy.sk5,
                status: 'success'
            });
        }
    } catch (error) {
        console.error('获取代理失败:', error.response?.data || error.message);
    }
}
```

## 注意事项

1. **负载均衡**: API会自动选择使用次数最少的代理，实现负载均衡
2. **状态管理**: 建议外部程序使用代理后及时报告状态，以便系统维护代理池的健康状态
3. **错误处理**: 请妥善处理API返回的错误信息
4. **并发安全**: API支持并发调用，数据库操作已做并发保护
5. **性能优化**: 建议根据实际需求设置合适的limit参数，避免一次获取过多代理
