const axios = require('axios');

// 创建测试CK数据
async function createTestCKData() {
  console.log('🧪 创建测试CK数据...');
  
  const baseURL = 'http://localhost:15001';
  
  // 不同格式的测试CK数据
  const testCKDataSets = [
    {
      promotion_user_id: '1001',
      action_type: 'scan',
      ip_address: '*************',
      ip_province: '北京市',
      douyin_name: '测试用户1',
      douyin_id: 'test001',
      ck_data: '', // 空CK数据
      user_agent: 'Mozilla/5.0 (Test Browser)',
      extra_data: { session_id: 'session_empty_ck', test: true }
    },
    {
      promotion_user_id: '1002',
      action_type: 'login_success',
      ip_address: '*************',
      ip_province: '上海市',
      douyin_name: '测试用户2',
      douyin_id: 'test002',
      ck_data: 'sessionid=abc123def456; uid=user789; token=xyz123',
      user_agent: 'Mozilla/5.0 (Test Browser)',
      extra_data: { session_id: 'session_simple_ck', test: true }
    },
    {
      promotion_user_id: '1003',
      action_type: 'login_success',
      ip_address: '*************',
      ip_province: '广东省',
      douyin_name: '测试用户3',
      douyin_id: 'test003',
      ck_data: '[{"name":"sessionid","value":"abc123def456","domain":".douyin.com","path":"/","expires":1755586224}]',
      user_agent: 'Mozilla/5.0 (Test Browser)',
      extra_data: { session_id: 'session_json_ck', test: true }
    },
    {
      promotion_user_id: '1004',
      action_type: 'login_success',
      ip_address: '*************',
      ip_province: '浙江省',
      douyin_name: '测试用户4',
      douyin_id: 'test004',
      ck_data: '[{"name":"bd_ticket_guard_client_data","value":"eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRmdwL05PZ0VIV1VFemdrN1Z4aXZXY21SS0Ura3E4bCtpaENXMlJacXFCcTU1ZDAzVVRaQ3pUNFlpZlRSOG1UUks2aktTVEhpUzZqSklVcC9HekNuOUk9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D","domain":".douyin.com","path":"/","expires":1755586224.760081,"size":331,"httpOnly":false,"secure":false,"session":false,"sameParty":false,"sourceScheme":"Secure","sourcePort":443},{"name":"sessionid","value":"a269e868778c3c32bf99a7ee3ec687c2","domain":".douyin.com","path":"/","expires":1755586223.033117,"size":41,"httpOnly":true,"secure":true,"session":false,"sameParty":false,"sourceScheme":"Secure","sourcePort":443}]',
      user_agent: 'Mozilla/5.0 (Test Browser)',
      extra_data: { session_id: 'session_complex_ck', test: true }
    },
    {
      promotion_user_id: '1005',
      action_type: 'login_fail',
      ip_address: '*************',
      ip_province: '江苏省',
      douyin_name: '测试用户5',
      douyin_id: 'test005',
      ck_data: 'very_long_cookie_data_that_should_be_truncated_in_preview_but_fully_copyable_when_clicked_or_quick_copied_using_the_copy_button_functionality_test_data_with_many_characters_to_test_the_truncation_and_copy_functionality_in_the_user_interface_component_for_ck_data_display_and_management_system_123456789_abcdefghijklmnopqrstuvwxyz',
      user_agent: 'Mozilla/5.0 (Test Browser)',
      extra_data: { session_id: 'session_long_ck', test: true }
    }
  ];
  
  try {
    console.log('📤 发送测试数据到API...');
    
    for (let i = 0; i < testCKDataSets.length; i++) {
      const testData = testCKDataSets[i];
      
      console.log(`\n${i + 1}. 创建测试数据: ${testData.promotion_user_id} - ${testData.action_type}`);
      console.log(`   CK数据长度: ${testData.ck_data.length}字符`);
      
      const response = await axios.post(`${baseURL}/api/promotion-actions/save`, testData);
      
      if (response.data.code === 0) {
        console.log(`   ✅ 保存成功，操作ID: ${response.data.data.action_id}`);
      } else {
        console.log(`   ❌ 保存失败: ${response.data.msg}`);
      }
      
      // 短暂延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    console.log('\n🎉 测试CK数据创建完成！');
    console.log('\n📋 现在可以访问推广用户管理页面测试CK复制功能:');
    console.log('   http://localhost:15001/page/promotion/admin-promotion-simple.html');
    console.log('\n🔧 测试步骤:');
    console.log('   1. 在"推广操作记录"部分选择今天的日期');
    console.log('   2. 点击"🔍 查询记录"按钮');
    console.log('   3. 在CK列中测试以下功能:');
    console.log('      - 鼠标悬停查看复制按钮');
    console.log('      - 点击📋按钮快速复制');
    console.log('      - 点击CK预览框查看详情');
    console.log('      - 在详情框中测试格式化和下载功能');
    
  } catch (error) {
    console.error('💥 创建测试数据失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
createTestCKData();
