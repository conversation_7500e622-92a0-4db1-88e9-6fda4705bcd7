const mysql = require('mysql2/promise');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4"
};

async function testDeleteFunction() {
  console.log('🧪 测试删除推广用户功能...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 查看当前推广用户
    console.log('\n📋 1. 当前推广用户列表:');
    const [currentUsers] = await connection.execute(
      `SELECT promoter_id, username, status FROM user WHERE user_type = 'promoter' ORDER BY promoter_id`
    );
    
    console.log(`找到 ${currentUsers.length} 个推广用户:`);
    currentUsers.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.promoter_id}, 用户名: ${user.username}, 状态: ${user.status ? '启用' : '禁用'}`);
    });
    
    // 2. 创建一个测试用户用于删除
    const testUserId = 'test_delete_' + Date.now();
    console.log(`\n➕ 2. 创建测试用户: ${testUserId}`);
    
    try {
      await connection.execute(
        `INSERT INTO user (username, password, user_type, promoter_id, promotion_link, status, ckcount)
         VALUES (?, ?, 'promoter', ?, ?, 1, 0)`,
        [`test_user_${testUserId}`, '123456', testUserId, `http://localhost:15001?id=${testUserId}`]
      );
      console.log(`✅ 测试用户创建成功: ${testUserId}`);
    } catch (error) {
      console.error(`❌ 创建测试用户失败:`, error.message);
      await connection.end();
      return;
    }
    
    // 3. 验证用户已创建
    const [createdUser] = await connection.execute(
      `SELECT promoter_id, username FROM user WHERE promoter_id = ? AND user_type = 'promoter'`,
      [testUserId]
    );
    
    if (createdUser.length > 0) {
      console.log(`✅ 验证用户已创建: ${createdUser[0].username}`);
    } else {
      console.log(`❌ 用户创建验证失败`);
      await connection.end();
      return;
    }
    
    // 4. 测试删除功能
    console.log(`\n🗑️ 3. 测试删除用户: ${testUserId}`);
    
    const [deleteResult] = await connection.execute(
      "DELETE FROM user WHERE promoter_id = ? AND user_type = 'promoter'",
      [testUserId]
    );
    
    if (deleteResult.affectedRows > 0) {
      console.log(`✅ 用户删除成功，影响行数: ${deleteResult.affectedRows}`);
    } else {
      console.log(`❌ 用户删除失败，没有影响任何行`);
    }
    
    // 5. 验证用户已删除
    const [deletedUser] = await connection.execute(
      `SELECT promoter_id FROM user WHERE promoter_id = ? AND user_type = 'promoter'`,
      [testUserId]
    );
    
    if (deletedUser.length === 0) {
      console.log(`✅ 验证用户已删除`);
    } else {
      console.log(`❌ 用户删除验证失败，用户仍然存在`);
    }
    
    // 6. 测试统计数据API
    console.log(`\n📊 4. 测试统计数据API:`);
    
    const [statsData] = await connection.execute(
      `SELECT 
        promoter_id as user_id,
        username,
        COALESCE(visit_count, 0) as visit_count,
        COALESCE(unique_ip_count, 0) as unique_ip_count,
        COALESCE(scan_count, 0) as scan_count,
        COALESCE(success_count, 0) as success_count,
        COALESCE(fail_count, 0) as fail_count,
        COALESCE(expire_count, 0) as expire_count
       FROM user
       WHERE user_type = 'promoter' AND status = 1
       ORDER BY promoter_id`
    );
    
    console.log(`✅ 统计数据查询成功，返回 ${statsData.length} 条记录:`);
    statsData.forEach((stat, index) => {
      console.log(`${index + 1}. ${stat.user_id} - ${stat.username}: 访问${stat.visit_count} | IP${stat.unique_ip_count} | 扫码${stat.scan_count} | 成功${stat.success_count} | 失败${stat.fail_count} | 过期${stat.expire_count}`);
    });
    
    await connection.end();
    
    console.log('\n🎉 删除功能测试完成！');
    console.log('\n📋 测试结果总结:');
    console.log('✅ 数据库连接正常');
    console.log('✅ 创建测试用户成功');
    console.log('✅ 删除用户功能正常');
    console.log('✅ 删除验证成功');
    console.log('✅ 统计数据API正常');
    
    console.log('\n🔗 现在可以测试以下功能:');
    console.log('- 推广用户管理: http://localhost:15001/page/promotion/admin-promotion-simple.html');
    console.log('  * 查看推广用户列表');
    console.log('  * 添加推广用户');
    console.log('  * 启用/禁用推广用户');
    console.log('  * 删除推广用户 (新功能)');
    console.log('  * 查看推广统计数据 (已修复登录问题)');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    process.exit(1);
  }
}

testDeleteFunction();
