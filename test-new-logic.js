const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

async function testNewUsageLogic() {
    console.log(colors.bold('🧪 测试新的使用计数逻辑'));
    console.log('='.repeat(60));
    console.log(colors.cyan('测试原理: 只有在实际使用代理成功后才增加使用次数\n'));

    try {
        // 1. 获取初始状态
        console.log(colors.blue('📋 步骤1: 获取山东省代理的初始状态'));
        const initialResponse = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
            params: { province: '山东省' }
        });

        if (initialResponse.data.code !== 0 || !initialResponse.data.data) {
            console.log(colors.red('❌ 未找到山东省代理'));
            return;
        }

        const initialProxy = initialResponse.data.data;
        console.log(colors.green('✅ 初始状态:'));
        console.log(`   🆔 代理ID: ${initialProxy.id}`);
        console.log(`   🔗 SOCKS5: ${initialProxy.sk5.substring(0, 30)}...`);
        console.log(`   📊 初始使用次数: ${colors.bold(initialProxy.usage_count)}`);

        // 2. 多次获取代理，验证使用次数不变
        console.log(colors.blue('\n🔄 步骤2: 多次获取代理，验证使用次数不增加'));
        const getResults = [];
        
        for (let i = 1; i <= 3; i++) {
            const response = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
                params: { province: '山东省' }
            });
            
            if (response.data.code === 0 && response.data.data) {
                const proxy = response.data.data;
                getResults.push({
                    attempt: i,
                    usage_count: proxy.usage_count
                });
                console.log(`   第${i}次获取: 使用次数 = ${colors.bold(proxy.usage_count)}`);
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // 验证使用次数是否保持不变
        const allSame = getResults.every(result => result.usage_count === initialProxy.usage_count);
        if (allSame) {
            console.log(colors.green('✅ 验证通过: 多次获取代理，使用次数保持不变'));
        } else {
            console.log(colors.red('❌ 验证失败: 使用次数发生了变化'));
        }

        // 3. 模拟成功使用代理
        console.log(colors.blue('\n✅ 步骤3: 模拟成功使用代理'));
        const successReport = {
            sk5: initialProxy.sk5,
            status: 'success'
        };

        console.log('发送成功报告:', JSON.stringify(successReport, null, 2));
        const successResponse = await axios.post(`${BASE_URL}/report-proxy-status`, successReport);
        
        console.log('响应结果:');
        console.log(JSON.stringify(successResponse.data, null, 2));

        if (successResponse.data.code === 0) {
            console.log(colors.green('✅ 成功报告已发送'));
            console.log(`   📊 更新后使用次数: ${colors.bold(successResponse.data.data.usage_count)}`);
        }

        // 4. 验证使用次数是否增加
        console.log(colors.blue('\n🔍 步骤4: 验证使用次数是否正确增加'));
        const afterSuccessResponse = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
            params: { province: '山东省' }
        });

        if (afterSuccessResponse.data.code === 0 && afterSuccessResponse.data.data) {
            const afterSuccessProxy = afterSuccessResponse.data.data;
            const expectedCount = initialProxy.usage_count + 1;
            
            console.log(`   初始使用次数: ${colors.bold(initialProxy.usage_count)}`);
            console.log(`   当前使用次数: ${colors.bold(afterSuccessProxy.usage_count)}`);
            console.log(`   期望使用次数: ${colors.bold(expectedCount)}`);

            if (afterSuccessProxy.usage_count === expectedCount) {
                console.log(colors.green('✅ 验证通过: 成功使用后，使用次数正确增加了1'));
            } else {
                console.log(colors.red('❌ 验证失败: 使用次数增加不正确'));
            }
        }

        // 5. 模拟失败使用代理
        console.log(colors.blue('\n❌ 步骤5: 模拟失败使用代理'));
        const currentCount = afterSuccessResponse.data.data.usage_count;
        
        const failReport = {
            sk5: initialProxy.sk5,
            status: 'failed',
            error_msg: '连接超时'
        };

        console.log('发送失败报告:', JSON.stringify(failReport, null, 2));
        const failResponse = await axios.post(`${BASE_URL}/report-proxy-status`, failReport);
        
        console.log('响应结果:');
        console.log(JSON.stringify(failResponse.data, null, 2));

        if (failResponse.data.code === 0) {
            console.log(colors.yellow('⚠️  失败报告已发送'));
            console.log(`   📊 使用次数: ${colors.bold(failResponse.data.data.usage_count)} (应该保持不变)`);
        }

        // 6. 验证失败时使用次数不变
        console.log(colors.blue('\n🔍 步骤6: 验证失败时使用次数不变'));
        const afterFailResponse = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
            params: { province: '山东省' }
        });

        if (afterFailResponse.data.code === 0 && afterFailResponse.data.data) {
            const afterFailProxy = afterFailResponse.data.data;
            
            console.log(`   失败前使用次数: ${colors.bold(currentCount)}`);
            console.log(`   失败后使用次数: ${colors.bold(afterFailProxy.usage_count)}`);

            if (afterFailProxy.usage_count === currentCount) {
                console.log(colors.green('✅ 验证通过: 失败使用后，使用次数保持不变'));
            } else {
                console.log(colors.red('❌ 验证失败: 失败后使用次数发生了变化'));
            }
        }

        // 7. 再次成功使用，验证计数继续增加
        console.log(colors.blue('\n✅ 步骤7: 再次成功使用，验证计数继续增加'));
        const secondSuccessReport = {
            sk5: initialProxy.sk5,
            status: 'success'
        };

        const secondSuccessResponse = await axios.post(`${BASE_URL}/report-proxy-status`, secondSuccessReport);
        
        if (secondSuccessResponse.data.code === 0) {
            const finalCount = secondSuccessResponse.data.data.usage_count;
            const expectedFinalCount = currentCount + 1;
            
            console.log(`   第二次成功前: ${colors.bold(currentCount)}`);
            console.log(`   第二次成功后: ${colors.bold(finalCount)}`);
            console.log(`   期望次数: ${colors.bold(expectedFinalCount)}`);

            if (finalCount === expectedFinalCount) {
                console.log(colors.green('✅ 验证通过: 第二次成功使用后，使用次数正确增加'));
            } else {
                console.log(colors.red('❌ 验证失败: 第二次成功使用后计数不正确'));
            }
        }

        console.log(colors.bold('\n🎉 新使用计数逻辑测试完成!'));
        console.log('='.repeat(60));
        
        // 总结
        console.log(colors.bold('\n📊 测试总结:'));
        console.log('✅ 获取代理时不增加使用次数');
        console.log('✅ 成功使用代理时增加使用次数');
        console.log('✅ 失败使用代理时不增加使用次数');
        console.log('✅ 多次成功使用时累计增加使用次数');

    } catch (error) {
        console.error(colors.red('❌ 测试过程中发生错误:'));
        console.error('错误信息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
console.log(colors.bold('🚀 新使用计数逻辑测试脚本'));
console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
console.log('\n' + colors.yellow('⏳ 3秒后开始测试...'));

setTimeout(() => {
    testNewUsageLogic().catch(error => {
        console.error(colors.red('💥 测试脚本执行失败:'), error.message);
        process.exit(1);
    });
}, 3000);
