const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

async function simpleCkcountTest() {
    console.log('🧪 用户ckcount API简单测试\n');

    try {
        // 1. 获取用户当前ckcount
        console.log('1. 获取用户cjroot的当前ckcount:');
        const getResponse = await axios.get(`${BASE_URL}/get-user-ckcount?username=cjroot`);
        console.log('响应:', JSON.stringify(getResponse.data, null, 2));

        // 2. 增加用户ckcount
        console.log('\n2. 增加用户cjroot的ckcount:');
        const increaseResponse = await axios.post(`${BASE_URL}/increase-user-ckcount`, {
            username: 'cjroot'
        });
        console.log('响应:', JSON.stringify(increaseResponse.data, null, 2));

        // 3. 再次获取验证
        console.log('\n3. 验证增加后的ckcount:');
        const verifyResponse = await axios.get(`${BASE_URL}/get-user-ckcount?username=cjroot`);
        console.log('响应:', JSON.stringify(verifyResponse.data, null, 2));

        // 4. 批量增加测试
        console.log('\n4. 批量增加多个用户的ckcount:');
        const batchResponse = await axios.post(`${BASE_URL}/batch-increase-ckcount`, {
            usernames: ['cjroot', 'testuser1'],
            increment: 3
        });
        console.log('响应:', JSON.stringify(batchResponse.data, null, 2));

        console.log('\n✅ 测试完成！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('响应数据:', error.response.data);
        }
    }
}

simpleCkcountTest();
