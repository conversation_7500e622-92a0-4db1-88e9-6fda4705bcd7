// const express = require('express');
// const bodyParser = require('body-parser');
// const cors = require('cors');
// const moment = require('moment');

// const router = express.Router();
// const db = require('../db/mysql'); // ✅ 确保 db 是 mysql2/promise 连接池

// router.use(cors());
// router.use(bodyParser.json());

// // 🔹 保存商品和关键词
// router.post('/saveData', async (req, res) => {
//     const { productName, keyword } = req.body;

//     if (!productName || !keyword) {
//         return res.status(400).json({ message: '商品名称和关键词不能为空' });
//     }

//     const products = productName.split('|').map(p => p.trim()).filter(p => p);
//     const keywords = keyword.split('|').map(k => k.trim()).filter(k => k);

//     if (products.length === 0 || keywords.length === 0) {
//         return res.status(400).json({ message: '商品名称和关键词不能为空' });
//     }

//     const values = products.flatMap(product => 
//         keywords.map(kw => [product, kw, new Date()])
//     );

//     const sql = 'INSERT INTO shopping (productName, keyword, created_at) VALUES ?';

//     try {
//         const connection = await db.getConnection(); // ✅ 确保是 db 连接池
//         await connection.beginTransaction();
//         await connection.query(sql, [values]);
//         await connection.commit();
//         connection.release();
//         res.json({ message: '数据存储成功', insertedRows: values.length });
//     } catch (err) {
//         console.error('数据插入失败:', err);
//         res.status(500).json({ message: '数据存储失败' });
//     }
// });

// // 🔹 获取商品数据
// router.get('/getData', async (req, res) => {
//     console.log('获取商品数据');
//     const sql = 'SELECT id, productName, keyword, created_at FROM shopping ORDER BY created_at DESC';

//     try {
//         const [results] = await db.query(sql);
//         res.json({ message: '数据查询成功', data: results });
//     } catch (err) {
//         console.error('数据查询失败:', err);
//         res.status(500).json({ message: '数据查询失败' });
//     }
// });

// // 🔹 手机端上报执行数据
// router.post('/reportExecution', async (req, res) => {
//     const { deviceId, successCount, failureCount } = req.body;

//     if (!deviceId || successCount === undefined || failureCount === undefined) {
//         return res.status(400).json({ message: '参数不能为空' });
//     }

//     const sql = `
//         INSERT INTO execution_results (deviceId, successCount, failureCount, created_at)
//         VALUES (?, ?, ?, NOW())
//         ON DUPLICATE KEY UPDATE 
//             successCount = successCount + VALUES(successCount), 
//             failureCount = failureCount + VALUES(failureCount),
//             created_at = NOW()
//     `;

//     try {
//         const connection = await db.getConnection();
//         await connection.beginTransaction();
//         await connection.query(sql, [deviceId, successCount, failureCount]);
//         await connection.commit();
//         connection.release();
//         res.json({ message: '数据上报成功' });
//     } catch (error) {
//         console.error('数据上报失败:', error);
//         res.status(500).json({ message: '服务器错误' });
//     }
// });

// // 🔹 获取手机执行数据
// router.get('/getExecutionSummary', async (req, res) => {
//     try {
//         const sql = `
//             SELECT id, deviceId, successCount, failureCount, 
//             DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') AS created_at
//             FROM execution_results ORDER BY created_at DESC
//         `;

//         const [results] = await db.query(sql);
//         res.json({ message: '查询成功', data: results });
//     } catch (error) {
//         console.error('查询失败:', error);
//         res.status(500).json({ message: '服务器错误' });
//     }
// });

// // 🔹 查询每天每台设备的执行结果
// router.get('/getDailyExecutionResults', async (req, res) => {
//     console.log('查询每天每台设备的执行结果');
//     const { date } = req.query;

//     if (!date) {
//         return res.status(400).json({ message: '请提供查询日期' });
//     }

//     try {
//         const startDate = `${date} 00:00:00`;
//         const endDate = `${date} 23:59:59`;

//         const sql = `
//             SELECT 
//                 DATE(created_at) AS date, 
//                 deviceId, 
//                 SUM(successCount) AS totalSuccess, 
//                 SUM(failureCount) AS totalFailure
//             FROM execution_results
//             WHERE created_at BETWEEN ? AND ?
//             GROUP BY DATE(created_at), deviceId
//             ORDER BY date DESC, deviceId;
//         `;

//         const [results] = await db.query(sql, [startDate, endDate]);
//         res.json({ message: '查询成功', data: results });

//     } catch (error) {
//         console.error('查询失败:', error);
//         res.status(500).json({ message: '服务器错误' });
//     }
// });

// // 🔹 任务执行统计（周/月）
// router.get('/getWeeklyOrMonthlyExecutionSummary', async (req, res) => {
//     let { startDate, endDate } = req.query;

//     if (!startDate || !endDate) {
//         startDate = moment().startOf('isoWeek').format('YYYY-MM-DD 00:00:00');  // 本周一
//         endDate = moment().endOf('isoWeek').format('YYYY-MM-DD 23:59:59');  // 本周日
//     }

//     const sql = `
//         SELECT DATE_FORMAT(created_at, '%Y-%m-%d') AS date,
//                IFNULL(SUM(successCount), 0) AS successCount,
//                IFNULL(SUM(failureCount), 0) AS failureCount
//         FROM execution_results
//         WHERE created_at BETWEEN ? AND ?
//         GROUP BY date ORDER BY date;
//     `;

//     try {
//         const [results] = await db.query(sql, [startDate, endDate]);
//         res.json({ success: true, data: results });
//     } catch (error) {
//         console.error('查询失败:', error);
//         res.status(500).json({ message: '数据库查询失败' });
//     }
// });

// module.exports = router;
