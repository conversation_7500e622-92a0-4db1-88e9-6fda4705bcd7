<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户登录</title>
    <link rel="stylesheet" href="../../lib/layui/css/layui.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 400px;
            backdrop-filter: blur(10px);
        }

        .login-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 24px;
            font-weight: bold;
        }

        .login-form .layui-form-item {
            margin-bottom: 20px;
        }

        .login-btn {
            width: 100%;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            margin-top: 20px;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .promotion-info {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-title">推广用户登录</div>

        <form class="layui-form login-form" lay-filter="loginForm">
            <div class="layui-form-item">
                <label class="layui-form-label">用户ID</label>
                <div class="layui-input-block">
                    <input type="text" name="user_id" required lay-verify="required" placeholder="请输入推广用户ID"
                        autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">密码</label>
                <div class="layui-input-block">
                    <input type="password" name="password" required lay-verify="required" placeholder="请输入密码"
                        autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn login-btn" lay-submit lay-filter="login">登录</button>
                </div>
            </div>
        </form>

        <div class="promotion-info">
            <p>推广用户专用登录入口</p>
            <p>登录后可查看推广数据统计</p>
        </div>
    </div>

    <script src="../../lib/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function () {
            var form = layui.form;
            var layer = layui.layer;

            // 监听登录提交
            form.on('submit(login)', function (data) {
                var loadIndex = layer.load(2, { shade: 0.3 });

                // 发送登录请求
                fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include', // 确保包含cookies
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data.field)
                })
                    .then(response => response.json())
                    .then(result => {
                        layer.close(loadIndex);
                        console.log('登录API响应:', result);

                        if (result.code === 0) {
                            console.log('登录成功，准备跳转到仪表板');
                            layer.msg('登录成功！', { icon: 1 }, function () {
                                // 跳转到推广用户仪表板
                                window.location.href = '/page/promotion/promoter-dashboard.html';
                            });
                        } else {
                            console.error('登录失败:', result.msg);
                            layer.msg(result.msg || '登录失败', { icon: 2 });
                        }
                    })
                    .catch(error => {
                        layer.close(loadIndex);
                        console.error('登录错误:', error);
                        layer.msg('网络错误，请重试', { icon: 2 });
                    });

                return false; // 阻止表单跳转
            });
        });
    </script>
</body>

</html>