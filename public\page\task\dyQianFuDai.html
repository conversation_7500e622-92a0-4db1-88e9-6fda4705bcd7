<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>


<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input
                            id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>

                <br>

                <fieldset>
                    <legend>【抖音抢福袋】功能</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">话术</label>
                        <div class="layui-input-block">
                            <textarea name="主页链接" placeholder="" class="layui-textarea"></textarea>
                            <tip>底部提示</tip>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">关注数量</label>
                            <div class="layui-input-inline" style="width: 200px;">
                                <input type="number" name="抖音关注多少个停止" value="200" autocomplete="off"
                                    class="layui-input" />
                            </div>
                            <div class="layui-form-mid layui-word-aux">个(关注多少个后停止)</div>
                        </div>
                    </div>



                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">关前延迟</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" name="抖音关前最小延迟" value="3" autocomplete="off" class="layui-input"/>
                            </div>
                            <div class="layui-form-mid">到</div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" name="抖音关前最大延迟" value="5" autocomplete="off" class="layui-input"/>
                            </div>
                            <div class="layui-form-mid layui-word-aux">秒,随机延迟(范围内随机)</div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">去主页模式</label>
                        <div class="layui-input-block">
                            <input type="radio" name="扫一扫" lay-filter="GZmoShi" id="扫一扫" value="扫一扫" title="二维码" checked="" />
                            <input type="radio" name="扫一扫" lay-filter="GZmoShi" id="主页链接" value="主页链接" title="主页链接" />
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <label class="layui-form-label">选择开关</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="选择综合" title="选择综合" checked=""/>
                            <input type="checkbox" name="选择用户" title="选择用户"/>
                            <input type="checkbox" name="选择话题" title="选择话题"/>
                        </div>
                    </div>

                    



                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="一键抢福袋"
                        lay-filter="tijiao">一键抢福袋</button>

                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止抢福袋</button>


                </fieldset>


            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong
            tongYong.tongYong1()
        });
    </script>
</body>

</html>