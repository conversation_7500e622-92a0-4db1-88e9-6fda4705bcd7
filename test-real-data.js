const axios = require('axios');

// 测试您提供的实际数据格式
async function testRealData() {
  console.log('🧪 测试实际数据格式...');
  
  const baseURL = 'http://localhost:15001';
  
  try {
    // 您提供的实际数据
    const realData = {
      promotion_user_id: '1001',
      action_type: ['scan', 'login_success'], // 数组格式
      ip_address: '**************',
      ip_province: '浙江省',
      douyin_name: '恢复恢复',
      douyin_id: '78184614511',
      ck_data: '[{"name":"bd_ticket_guard_client_data","value":"eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRmdwL05PZ0VIV1VFemdrN1Z4aXZXY21SS0Ura3E4bCtpaENXMlJacXFCcTU1ZDAzVVRaQ3pUNFlpZlRSOG1UUks2aktTVEhpUzZqSklVcC9HekNuOUk9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D","domain":".douyin.com","path":"/","expires":1755586224.760081,"size":331,"httpOnly":false,"secure":false,"session":false,"sameParty":false,"sourceScheme":"Secure","sourcePort":443}]',
      user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      extra_data: '{"session_id":"session_1750401732380","login_count":1,"ip_source":"API_PROXY","timestamp":"2025-06-20T06:50:29.067Z","login_time":"2025-06-20T06:50:29.066Z","login_method":"qr_code_scan","user_info_extracted":true}'
    };
    
    console.log('📤 发送实际数据...');
    console.log('   推广用户ID:', realData.promotion_user_id);
    console.log('   操作类型:', realData.action_type);
    console.log('   IP地址:', realData.ip_address);
    console.log('   IP省份:', realData.ip_province);
    console.log('   抖音名:', realData.douyin_name);
    console.log('   抖音ID:', realData.douyin_id);
    console.log('   会话ID:', JSON.parse(realData.extra_data).session_id);
    
    const response = await axios.post(`${baseURL}/api/promotion-actions/save`, realData);
    
    console.log('\n📥 API响应:');
    console.log('   状态码:', response.data.code);
    console.log('   消息:', response.data.msg);
    
    if (response.data.code === 0) {
      console.log('✅ 实际数据保存成功！');
      console.log('   访问记录ID:', response.data.data.visit_id);
      console.log('   操作记录ID:', response.data.data.action_id);
      console.log('   最终操作类型:', response.data.data.action_type);
      console.log('   原始操作类型:', response.data.data.original_action_type);
      console.log('   会话ID:', response.data.data.session_id);
      console.log('   更新的表:', response.data.data.tables_updated);
      
      console.log('\n📊 数据保存详情:');
      console.log('   - promotion_visits表: 保存了访问记录');
      console.log('   - promotion_actions表: 保存了操作记录');
      console.log('   - promotion_daily_stats表: 更新了每日统计');
      
    } else {
      console.log('❌ 实际数据保存失败:', response.data.msg);
    }
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testRealData();
