const mysql = require('mysql2/promise');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4"
};

async function fixPromotionLinks() {
  console.log('🔧 开始修复推广链接...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 查询现有的推广用户
    const [users] = await connection.execute(
      'SELECT user_id, username, promotion_link FROM promotion_users'
    );
    
    console.log(`📋 找到 ${users.length} 个推广用户`);
    
    // 更新推广链接
    for (const user of users) {
      const oldLink = user.promotion_link;
      const newLink = `http://localhost:15001?id=${user.user_id}`;
      
      if (oldLink !== newLink) {
        await connection.execute(
          'UPDATE promotion_users SET promotion_link = ? WHERE user_id = ?',
          [newLink, user.user_id]
        );
        
        console.log(`✅ 更新用户 ${user.user_id} (${user.username})`);
        console.log(`   旧链接: ${oldLink}`);
        console.log(`   新链接: ${newLink}`);
      } else {
        console.log(`⚪ 用户 ${user.user_id} 链接已正确`);
      }
    }
    
    // 验证更新结果
    console.log('\n🔍 验证更新结果...');
    const [updatedUsers] = await connection.execute(
      'SELECT user_id, username, promotion_link FROM promotion_users'
    );
    
    updatedUsers.forEach(user => {
      console.log(`✅ ${user.user_id} - ${user.username}: ${user.promotion_link}`);
    });
    
    await connection.end();
    
    console.log('\n🎉 推广链接修复完成！');
    console.log('\n📋 现在可以使用的推广链接:');
    updatedUsers.forEach(user => {
      console.log(`- 用户 ${user.user_id}: ${user.promotion_link}`);
    });
    
    console.log('\n💡 使用说明:');
    console.log('1. 推广用户分享推广链接给目标用户');
    console.log('2. 目标用户点击链接访问系统');
    console.log('3. 系统自动记录访问统计');
    console.log('4. 推广用户可在仪表板查看数据');
    
  } catch (error) {
    console.error('💥 修复失败:', error.message);
    process.exit(1);
  }
}

// 运行修复
if (require.main === module) {
  fixPromotionLinks();
}

module.exports = fixPromotionLinks;
