const axios = require('axios');

// 调试API响应
async function debugAPIResponse() {
  console.log('🔍 调试推广用户API响应...');
  
  const baseURL = 'http://localhost:15001';
  const testUser = { user_id: '1001', password: '123456' };
  
  try {
    // 创建axios实例，模拟浏览器行为
    const client = axios.create({
      baseURL: baseURL,
      withCredentials: true,
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('\n1️⃣ 测试登录...');
    const loginResponse = await client.post('/api/promotion/promoter/login', testUser);
    
    console.log('登录响应:', {
      status: loginResponse.status,
      data: loginResponse.data,
      headers: loginResponse.headers['set-cookie']
    });
    
    if (loginResponse.data.code === 0) {
      console.log('\n2️⃣ 测试统计数据API...');
      
      try {
        const statsResponse = await client.get('/api/promotion/promoter/today-stats');
        
        console.log('统计数据响应:', {
          status: statsResponse.status,
          data: statsResponse.data
        });
        
        if (statsResponse.data.code === 0) {
          const stats = statsResponse.data.data;
          console.log('\n📊 详细统计数据:');
          console.log(`   visit_count: ${stats.visit_count} (类型: ${typeof stats.visit_count})`);
          console.log(`   unique_ip_count: ${stats.unique_ip_count} (类型: ${typeof stats.unique_ip_count})`);
          console.log(`   scan_count: ${stats.scan_count} (类型: ${typeof stats.scan_count})`);
          console.log(`   success_count: ${stats.success_count} (类型: ${typeof stats.success_count})`);
          console.log(`   fail_count: ${stats.fail_count} (类型: ${typeof stats.fail_count})`);
          console.log(`   expire_count: ${stats.expire_count} (类型: ${typeof stats.expire_count})`);
          
          // 检查是否所有值都为0
          const allZero = Object.values(stats).every(val => val === 0 || val === '0');
          console.log(`\n❓ 所有数据都为0: ${allZero}`);
          
        } else {
          console.log('❌ 统计数据API失败:', statsResponse.data);
        }
        
      } catch (statsError) {
        console.log('❌ 统计数据API错误:', {
          message: statsError.message,
          status: statsError.response?.status,
          data: statsError.response?.data
        });
      }
      
      console.log('\n3️⃣ 测试用户信息API...');
      try {
        const userResponse = await client.get('/api/promotion/promoter/user-info');
        console.log('用户信息响应:', {
          status: userResponse.status,
          data: userResponse.data
        });
      } catch (userError) {
        console.log('❌ 用户信息API错误:', userError.response?.data);
      }
      
    } else {
      console.log('❌ 登录失败:', loginResponse.data);
    }
    
  } catch (error) {
    console.log('❌ 请求失败:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
  }
}

// 运行调试
debugAPIResponse().catch(console.error);
