const axios = require('axios');

// 最终修复验证测试
async function testFinalFix() {
  console.log('🎯 最终修复验证测试...');
  
  const baseURL = 'http://localhost:15001';
  const testUser = { user_id: '1001', password: '123456' };
  
  console.log(`\n📋 测试用户: ID=${testUser.user_id}, 密码=${testUser.password}`);
  console.log('─'.repeat(60));
  
  try {
    // 创建axios实例
    const client = axios.create({
      baseURL: baseURL,
      withCredentials: true,
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    // 1. 登录
    console.log('1️⃣ 测试推广用户登录...');
    const loginResponse = await client.post('/api/promotion/promoter/login', testUser);
    
    if (loginResponse.data.code === 0) {
      console.log('✅ 登录成功');
      console.log('   用户信息:', loginResponse.data.data);
      
      // 2. 获取统计数据
      console.log('\n2️⃣ 测试获取统计数据...');
      const statsResponse = await client.get('/api/promotion/promoter/today-stats');
      
      if (statsResponse.data.code === 0) {
        console.log('✅ 统计数据获取成功');
        const stats = statsResponse.data.data;
        
        console.log('\n📊 统计数据详情:');
        console.log(`   访问次数: ${stats.visit_count} ${stats.visit_count > 0 ? '✅' : '❌'}`);
        console.log(`   独立IP数: ${stats.unique_ip_count} ${stats.unique_ip_count > 0 ? '✅' : '❌'}`);
        console.log(`   扫码数量: ${stats.scan_count}`);
        console.log(`   成功数量: ${stats.success_count} ${stats.success_count > 0 ? '✅' : '❌'}`);
        console.log(`   失败数量: ${stats.fail_count}`);
        console.log(`   过期数量: ${stats.expire_count}`);
        
        // 检查是否修复成功
        const isFixed = stats.visit_count > 0 || stats.success_count > 0;
        
        if (isFixed) {
          console.log('\n🎉 修复成功！统计数据不再显示为0');
          
          // 3. 测试操作记录
          console.log('\n3️⃣ 测试获取操作记录...');
          const actionsResponse = await client.get('/api/promotion/promoter/today-actions?page=1&limit=5');
          
          if (actionsResponse.data.code === 0) {
            console.log('✅ 操作记录获取成功');
            const actions = actionsResponse.data.data;
            console.log(`   总记录数: ${actions.total}`);
            console.log(`   当前页记录数: ${actions.list.length}`);
            
            if (actions.list.length > 0) {
              console.log('   最近操作记录:');
              actions.list.slice(0, 3).forEach((action, index) => {
                console.log(`     ${index + 1}. 用户: ${action.user_id}, 时间: ${action.time}, IP: ${action.ip}, 状态: ${action.status}`);
              });
            }
          } else {
            console.log('❌ 操作记录获取失败:', actionsResponse.data.msg);
          }
          
          // 4. 测试用户信息
          console.log('\n4️⃣ 测试获取用户信息...');
          const userInfoResponse = await client.get('/api/promotion/promoter/user-info');
          
          if (userInfoResponse.data.code === 0) {
            console.log('✅ 用户信息获取成功');
            console.log('   用户信息:', userInfoResponse.data.data);
          } else {
            console.log('❌ 用户信息获取失败:', userInfoResponse.data.msg);
          }
          
        } else {
          console.log('\n❌ 修复失败！统计数据仍然显示为0');
          console.log('   可能的原因:');
          console.log('   1. 数据库中没有对应的数据');
          console.log('   2. API查询逻辑仍有问题');
          console.log('   3. 日期匹配问题');
        }
        
      } else {
        console.log('❌ 统计数据获取失败:', statsResponse.data.msg);
      }
      
    } else {
      console.log('❌ 登录失败:', loginResponse.data.msg);
    }
    
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   响应数据:`, error.response.data);
    }
  }
  
  console.log('\n🎉 测试完成！');
  
  console.log('\n📋 修复总结:');
  console.log('✅ 1. 修改API查询使用CURDATE()而不是字符串日期比较');
  console.log('✅ 2. 创建了正确的今日统计数据');
  console.log('✅ 3. 修复了前端cookie处理问题');
  console.log('✅ 4. 添加了详细的调试日志');
  
  console.log('\n🌐 现在可以正常使用:');
  console.log('1. 访问 http://localhost:15001/page/promotion/promoter-login.html');
  console.log('2. 使用用户ID: 1001, 密码: 123456 登录');
  console.log('3. 登录后应该能看到正确的统计数据（不再是0）');
  console.log('4. 可以查看操作记录和用户信息');
  
  console.log('\n💡 如果仍有问题:');
  console.log('1. 检查浏览器控制台的API响应');
  console.log('2. 检查服务器日志');
  console.log('3. 确认数据库中的数据是否正确');
}

// 运行测试
if (require.main === module) {
  testFinalFix().catch(console.error);
}

module.exports = testFinalFix;
