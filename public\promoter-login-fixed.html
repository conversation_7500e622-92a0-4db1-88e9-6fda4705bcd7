<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户登录（修复版）</title>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .login-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .form-item {
            margin-bottom: 20px;
        }
        
        .form-item label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-item input {
            width: 100%;
            height: 45px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 0 15px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-item input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .login-btn {
            width: 100%;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        
        .auto-login-info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: center;
            display: none;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
        }
        
        .footer-text {
            color: #999;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-title">推广用户登录</div>
            <div class="login-subtitle">修复版 - 按照测试页面逻辑</div>
        </div>

        <div class="auto-login-info" id="autoLoginInfo">
            检测到URL参数，将在 <span id="countdown">3</span> 秒后自动登录...
        </div>

        <form id="loginForm">
            <div class="form-item">
                <label for="user_id">用户ID</label>
                <input type="text" id="user_id" name="user_id" required>
                <div class="error-message" id="userIdError"></div>
            </div>

            <div class="form-item">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
                <div class="error-message" id="passwordError"></div>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <span id="btnText">登录</span>
            </button>
        </form>

        <div class="footer">
            <div class="footer-text">© 2024 推广用户系统</div>
        </div>
    </div>

    <script>
        // 完全按照测试页面的成功逻辑
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 推广用户登录页面DOM加载完成');
            initLoginPage();
        });

        function initLoginPage() {
            console.log('🎯 初始化登录页面 - 使用测试页面的成功逻辑');

            // 表单提交处理 - 完全按照测试页面逻辑
            document.getElementById('loginForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const loginData = {
                    user_id: formData.get('user_id').trim(),
                    password: formData.get('password').trim()
                };

                console.log('🔄 发送登录请求:', { user_id: loginData.user_id, password: '***' });

                // 验证输入
                if (!validateInput(loginData)) {
                    return;
                }

                // 显示加载状态
                setLoadingState(true);

                try {
                    const response = await fetch('/api/promotion/promoter/login', {
                        method: 'POST',
                        credentials: 'include',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(loginData)
                    });

                    console.log(`📡 收到响应: 状态码=${response.status}`);

                    const result = await response.json();
                    console.log('📥 响应数据:', result);
                    console.log('🍪 更新后的cookies:', document.cookie);

                    if (result.code === 0) {
                        // 登录成功 - 按照测试页面逻辑
                        console.log('✅ 登录成功！');
                        console.log(`👤 用户信息: ${result.data.username} (ID: ${result.data.user_id})`);
                        
                        console.log('🔄 准备跳转到干净版仪表板...');
                        console.log('执行: window.location.href = "/clean-promoter-dashboard.html"');
                        
                        // 立即跳转 - 按照测试页面逻辑
                        window.location.href = '/clean-promoter-dashboard.html';

                    } else {
                        // 登录失败
                        console.error('❌ 登录失败:', result.msg);
                        alert(result.msg || '登录失败');

                        // 显示具体错误信息
                        if (result.msg && result.msg.includes('用户ID')) {
                            showFieldError('userIdError', result.msg);
                        } else if (result.msg && result.msg.includes('密码')) {
                            showFieldError('passwordError', result.msg);
                        }
                    }

                } catch (error) {
                    console.error('❌ 登录请求失败:', error);
                    alert('网络错误，请稍后重试');
                } finally {
                    setLoadingState(false);
                }
            });

            // 输入验证
            function validateInput(data) {
                clearErrors();

                let isValid = true;

                if (!data.user_id) {
                    showFieldError('userIdError', '请输入用户ID');
                    isValid = false;
                }

                if (!data.password) {
                    showFieldError('passwordError', '请输入密码');
                    isValid = false;
                }

                return isValid;
            }

            // 显示字段错误
            function showFieldError(elementId, message) {
                const errorElement = document.getElementById(elementId);
                if (errorElement) {
                    errorElement.textContent = message;
                    errorElement.style.display = 'block';
                }
            }

            // 清除错误信息
            function clearErrors() {
                const errorElements = document.querySelectorAll('.error-message');
                errorElements.forEach(element => {
                    element.style.display = 'none';
                    element.textContent = '';
                });
            }

            // 设置加载状态
            function setLoadingState(loading) {
                const loginBtn = document.getElementById('loginBtn');
                const btnText = document.getElementById('btnText');

                if (loading) {
                    loginBtn.disabled = true;
                    btnText.textContent = '登录中...';
                } else {
                    loginBtn.disabled = false;
                    btnText.textContent = '登录';
                }
            }

            // 页面初始化 - 按照测试页面逻辑
            console.log('🚀 推广用户登录页面已加载');

            // 检查URL参数 - 按照测试页面逻辑
            const urlParams = new URLSearchParams(window.location.search);

            // 如果有用户ID参数，预填到表单中
            const userId = urlParams.get('user_id');
            if (userId) {
                document.getElementById('user_id').value = userId;
                console.log('从URL参数预填用户ID:', userId);
            }

            // 如果有密码参数，预填到表单中并自动登录
            const password = urlParams.get('password');
            if (password) {
                document.getElementById('password').value = password;
                console.log('从URL参数预填密码');

                // 如果同时有用户ID和密码，自动触发登录
                if (userId) {
                    console.log('检测到完整登录参数，准备自动登录...');
                    
                    // 显示自动登录提示
                    const autoLoginInfo = document.getElementById('autoLoginInfo');
                    const countdownSpan = document.getElementById('countdown');
                    autoLoginInfo.style.display = 'block';
                    
                    let countdown = 3;
                    const countdownInterval = setInterval(() => {
                        countdownSpan.textContent = countdown;
                        countdown--;
                        
                        if (countdown < 0) {
                            clearInterval(countdownInterval);
                            console.log('执行自动登录');
                            document.getElementById('loginForm').dispatchEvent(new Event('submit'));
                        }
                    }, 1000);
                }
            }

            // 如果有错误信息则显示
            const error = urlParams.get('error');
            if (error) {
                alert(decodeURIComponent(error));
            }
        }

        // 监听页面卸载 - 按照测试页面逻辑
        window.addEventListener('beforeunload', function(e) {
            console.log('⚠️ 页面即将卸载/跳转');
        });
    </script>
</body>
</html>
