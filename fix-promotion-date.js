const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 修复推广用户数据日期问题
async function fixPromotionDate() {
  console.log('🔧 修复推广用户数据日期问题...');
  
  const testUserId = '1001';
  const today = new Date().toISOString().split('T')[0];
  const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  
  console.log(`\n📋 用户ID: ${testUserId}`);
  console.log(`📅 今日日期: ${today}`);
  console.log(`📅 昨日日期: ${yesterday}`);
  console.log('─'.repeat(60));
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查现有的promotion_daily_stats数据
    console.log('\n1️⃣ 检查现有的promotion_daily_stats数据...');
    const [existingStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats 
       WHERE promotion_user_id = ? 
       ORDER BY stat_date DESC`,
      [testUserId]
    );
    
    console.log('现有统计数据:');
    existingStats.forEach((stat, index) => {
      console.log(`   ${index + 1}. 日期: ${stat.stat_date.toISOString().split('T')[0]}, 访问: ${stat.visit_count}, 独立IP: ${stat.unique_ip_count}, 成功: ${stat.success_count}`);
    });
    
    // 2. 检查今日是否已有数据
    const [todayStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats 
       WHERE promotion_user_id = ? AND stat_date = ?`,
      [testUserId, today]
    );
    
    if (todayStats.length > 0) {
      console.log('\n✅ 今日已有统计数据，无需修复');
      console.log('   今日数据:', JSON.stringify(todayStats[0], null, 2));
    } else {
      console.log('\n⚠️  今日没有统计数据，开始修复...');
      
      // 方案1：将昨天的数据复制到今天
      if (existingStats.length > 0) {
        const latestStat = existingStats[0];
        console.log('\n🔄 将最新数据复制到今日...');
        
        await connection.execute(
          `INSERT INTO promotion_daily_stats 
           (promotion_user_id, stat_date, visit_count, unique_ip_count, 
            scan_count, success_count, fail_count, expire_count)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            testUserId,
            today,
            latestStat.visit_count,
            latestStat.unique_ip_count,
            latestStat.scan_count,
            latestStat.success_count,
            latestStat.fail_count,
            latestStat.expire_count
          ]
        );
        
        console.log('✅ 今日统计数据已创建');
      } else {
        // 方案2：从原始数据重新计算今日统计
        console.log('\n📊 从原始数据重新计算今日统计...');
        
        // 计算今日访问统计
        const [visitStats] = await connection.execute(
          `SELECT
            COUNT(*) as visit_count,
            COUNT(DISTINCT visitor_ip) as unique_ip_count
           FROM promotion_visits
           WHERE promotion_user_id = ? AND DATE(visit_time) = ?`,
          [testUserId, today]
        );
        
        // 计算今日操作统计
        const [actionStats] = await connection.execute(
          `SELECT
            COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
            COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
            COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count,
            COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as expire_count
           FROM promotion_actions
           WHERE promotion_user_id = ? AND DATE(action_time) = ?`,
          [testUserId, today]
        );
        
        const statsData = {
          visit_count: visitStats[0]?.visit_count || 0,
          unique_ip_count: visitStats[0]?.unique_ip_count || 0,
          scan_count: actionStats[0]?.scan_count || 0,
          success_count: actionStats[0]?.success_count || 0,
          fail_count: actionStats[0]?.fail_count || 0,
          expire_count: actionStats[0]?.expire_count || 0,
        };
        
        console.log('计算出的今日统计:', statsData);
        
        // 如果今日没有任何数据，使用历史总数据
        if (statsData.visit_count === 0 && statsData.success_count === 0) {
          console.log('今日无数据，使用历史总数据...');
          
          // 获取历史总数据
          const [totalVisitStats] = await connection.execute(
            `SELECT
              COUNT(*) as visit_count,
              COUNT(DISTINCT visitor_ip) as unique_ip_count
             FROM promotion_visits
             WHERE promotion_user_id = ?`,
            [testUserId]
          );
          
          const [totalActionStats] = await connection.execute(
            `SELECT
              COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
              COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
              COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count,
              COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as expire_count
             FROM promotion_actions
             WHERE promotion_user_id = ?`,
            [testUserId]
          );
          
          statsData.visit_count = totalVisitStats[0]?.visit_count || 0;
          statsData.unique_ip_count = totalVisitStats[0]?.unique_ip_count || 0;
          statsData.scan_count = totalActionStats[0]?.scan_count || 0;
          statsData.success_count = totalActionStats[0]?.success_count || 0;
          statsData.fail_count = totalActionStats[0]?.fail_count || 0;
          statsData.expire_count = totalActionStats[0]?.expire_count || 0;
          
          console.log('使用的历史总数据:', statsData);
        }
        
        // 插入今日统计数据
        await connection.execute(
          `INSERT INTO promotion_daily_stats 
           (promotion_user_id, stat_date, visit_count, unique_ip_count, 
            scan_count, success_count, fail_count, expire_count)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            testUserId,
            today,
            statsData.visit_count,
            statsData.unique_ip_count,
            statsData.scan_count,
            statsData.success_count,
            statsData.fail_count,
            statsData.expire_count
          ]
        );
        
        console.log('✅ 今日统计数据已创建');
      }
    }
    
    // 3. 验证修复结果
    console.log('\n3️⃣ 验证修复结果...');
    const [finalStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats 
       WHERE promotion_user_id = ? AND stat_date = ?`,
      [testUserId, today]
    );
    
    if (finalStats.length > 0) {
      console.log('✅ 修复成功！今日统计数据:');
      console.log('   数据:', JSON.stringify(finalStats[0], null, 2));
      
      console.log('\n📊 统计摘要:');
      console.log(`   访问次数: ${finalStats[0].visit_count}`);
      console.log(`   独立IP数: ${finalStats[0].unique_ip_count}`);
      console.log(`   扫码数量: ${finalStats[0].scan_count}`);
      console.log(`   成功数量: ${finalStats[0].success_count}`);
      console.log(`   失败数量: ${finalStats[0].fail_count}`);
      console.log(`   过期数量: ${finalStats[0].expire_count}`);
    } else {
      console.log('❌ 修复失败，今日统计数据仍然不存在');
    }
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
  
  console.log('\n🎉 数据修复完成！');
  
  console.log('\n🌐 现在可以测试:');
  console.log('1. 访问推广用户登录页面');
  console.log('2. 使用用户ID: 1001, 密码: 123456 登录');
  console.log('3. 应该能看到正确的统计数据');
}

// 运行修复
if (require.main === module) {
  fixPromotionDate().catch(console.error);
}

module.exports = fixPromotionDate;
