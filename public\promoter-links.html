<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户链接管理</title>
    <link rel="stylesheet" href="/layui/css/layui.css">
    <style>
        body {
            background-color: #f2f2f2;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            margin: 0;
            color: #333;
        }

        .link-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-group input {
            width: 100%;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 0 15px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .link-result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
        }

        .link-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }

        .link-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }

        .link-url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 3px;
            word-break: break-all;
            border: 1px solid #e9ecef;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            right: 5px;
            top: 5px;
            padding: 2px 8px;
            font-size: 12px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .copy-btn:hover {
            background: #005a87;
        }

        .user-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .error-message {
            background: #ffebee;
            border: 1px solid #ffcdd2;
            color: #c62828;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .success-message {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .link-card {
                padding: 20px 15px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>推广用户链接管理</h1>
            <p>为推广用户生成专属登录链接</p>
        </div>

        <div class="link-card">
            <h3>生成推广用户链接</h3>

            <div class="form-group">
                <label for="userId">推广用户ID:</label>
                <input type="text" id="userId" placeholder="请输入推广用户ID" />
            </div>

            <button class="layui-btn" onclick="generateLinks()">生成链接</button>
            <button class="layui-btn layui-btn-primary" onclick="clearResults()">清空结果</button>

            <div id="results" style="display: none;">
                <div id="userInfo" class="user-info"></div>

                <div class="link-result">
                    <h4>生成的推广链接：</h4>

                    <div class="link-item">
                        <div class="link-label">1. 推广用户登录链接（预填用户ID）:</div>
                        <div class="link-url" id="loginLink">
                            <button class="copy-btn" onclick="copyToClipboard('loginLink')">复制</button>
                        </div>
                    </div>

                    <div class="link-item">
                        <div class="link-label">2. 推广用户登录链接（空白表单）:</div>
                        <div class="link-url" id="loginLinkBlank">
                            <button class="copy-btn" onclick="copyToClipboard('loginLinkBlank')">复制</button>
                        </div>
                    </div>

                    <div class="link-item">
                        <div class="link-label">3. 推广用户仪表板链接:</div>
                        <div class="link-url" id="dashboardLink">
                            <button class="copy-btn" onclick="copyToClipboard('dashboardLink')">复制</button>
                        </div>
                    </div>

                    <div class="link-item">
                        <div class="link-label">4. 推广用户信息API:</div>
                        <div class="link-url" id="apiLink">
                            <button class="copy-btn" onclick="copyToClipboard('apiLink')">复制</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="errorMessage" class="error-message" style="display: none;"></div>
        </div>
    </div>

    <script src="/layui/layui.js"></script>
    <script>
        layui.use(['layer'], function () {
            const layer = layui.layer;

            // 生成链接
            window.generateLinks = async function () {
                const userId = document.getElementById('userId').value.trim();

                if (!userId) {
                    layer.msg('请输入推广用户ID', { icon: 2 });
                    return;
                }

                try {
                    // 获取推广用户信息
                    const response = await fetch(`/api/promotion/promoter/link-info/${userId}`);
                    const result = await response.json();

                    if (result.code === 0) {
                        const data = result.data;
                        const baseUrl = window.location.origin;

                        // 显示用户信息
                        document.getElementById('userInfo').innerHTML = `
                            <strong>推广用户信息:</strong><br>
                            用户ID: ${data.user_id}<br>
                            用户名: ${data.username}<br>
                            状态: 正常
                        `;

                        // 生成链接
                        document.getElementById('loginLink').innerHTML = `
                            ${baseUrl}/promoter-login?user_id=${data.user_id}
                            <button class="copy-btn" onclick="copyToClipboard('loginLink')">复制</button>
                        `;

                        document.getElementById('loginLinkBlank').innerHTML = `
                            ${baseUrl}/promoter-login
                            <button class="copy-btn" onclick="copyToClipboard('loginLinkBlank')">复制</button>
                        `;

                        document.getElementById('dashboardLink').innerHTML = `
                            ${baseUrl}/promoter-dashboard
                            <button class="copy-btn" onclick="copyToClipboard('dashboardLink')">复制</button>
                        `;

                        document.getElementById('apiLink').innerHTML = `
                            ${baseUrl}/api/promotion/promoter/link-info/${data.user_id}
                            <button class="copy-btn" onclick="copyToClipboard('apiLink')">复制</button>
                        `;

                        // 显示结果
                        document.getElementById('results').style.display = 'block';
                        document.getElementById('errorMessage').style.display = 'none';

                        layer.msg('链接生成成功！', { icon: 1 });

                    } else {
                        showError(result.msg);
                    }

                } catch (error) {
                    console.error('生成链接失败:', error);
                    showError('网络错误，请稍后重试');
                }
            };

            // 显示错误信息
            function showError(message) {
                document.getElementById('errorMessage').textContent = message;
                document.getElementById('errorMessage').style.display = 'block';
                document.getElementById('results').style.display = 'none';
                layer.msg(message, { icon: 2 });
            }

            // 清空结果
            window.clearResults = function () {
                document.getElementById('userId').value = '';
                document.getElementById('results').style.display = 'none';
                document.getElementById('errorMessage').style.display = 'none';
            };

            // 复制到剪贴板
            window.copyToClipboard = function (elementId) {
                const element = document.getElementById(elementId);
                const text = element.textContent.replace('复制', '').trim();

                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(() => {
                        layer.msg('链接已复制到剪贴板', { icon: 1 });
                    }).catch(err => {
                        console.error('复制失败:', err);
                        fallbackCopyTextToClipboard(text);
                    });
                } else {
                    fallbackCopyTextToClipboard(text);
                }
            };

            // 备用复制方法
            function fallbackCopyTextToClipboard(text) {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.top = "0";
                textArea.style.left = "0";
                textArea.style.position = "fixed";

                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        layer.msg('链接已复制到剪贴板', { icon: 1 });
                    } else {
                        layer.msg('复制失败，请手动复制', { icon: 2 });
                    }
                } catch (err) {
                    console.error('复制失败:', err);
                    layer.msg('复制失败，请手动复制', { icon: 2 });
                }

                document.body.removeChild(textArea);
            }

            // 回车键触发生成
            document.getElementById('userId').addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    generateLinks();
                }
            });

            console.log('🔗 推广用户链接管理页面已加载');
        });
    </script>
</body>

</html>