const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出函数
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

async function testShandongProxy() {
    console.log(colors.bold('🧪 山东省代理IP接口测试'));
    console.log('='.repeat(50));

    try {
        // 1. 首先检查山东省是否有可用代理
        console.log(colors.blue('\n📋 步骤1: 检查山东省代理可用性'));
        const provincesResponse = await axios.get(`${BASE_URL}/get-available-provinces`);
        
        if (provincesResponse.data.code === 0) {
            const provinces = provincesResponse.data.data;
            const shandongInfo = provinces.find(p => p.province.includes('山东'));
            
            if (shandongInfo) {
                console.log(colors.green(`✅ 山东省有 ${shandongInfo.available_count} 个可用代理`));
            } else {
                console.log(colors.yellow('⚠️  山东省暂无可用代理，将添加测试数据...'));
                
                // 添加山东省测试代理
                await addShandongTestData();
            }
        }

        // 2. 测试获取单个山东省代理
        console.log(colors.blue('\n🎯 步骤2: 获取单个山东省代理'));
        const singleProxyResponse = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
            params: { province: '山东省' }
        });

        console.log('请求URL:', colors.cyan(`${BASE_URL}/get-proxy-by-province?province=山东省`));
        console.log('响应数据:');
        console.log(JSON.stringify(singleProxyResponse.data, null, 2));

        if (singleProxyResponse.data.code === 0 && singleProxyResponse.data.data) {
            const proxy = singleProxyResponse.data.data;
            console.log(colors.green('\n✅ 成功获取山东省代理:'));
            console.log(`   🔗 SOCKS5地址: ${colors.bold(proxy.sk5)}`);
            console.log(`   🌐 IP地址: ${colors.bold(proxy.ip)}`);
            console.log(`   📍 省份: ${colors.bold(proxy.province)}`);
            console.log(`   📊 使用次数: ${colors.bold(proxy.usage_count)}`);
            console.log(`   🆔 代理ID: ${colors.bold(proxy.id)}`);

            // 3. 测试批量获取山东省代理
            console.log(colors.blue('\n📦 步骤3: 批量获取山东省代理'));
            const multiProxyResponse = await axios.get(`${BASE_URL}/get-proxies-by-province`, {
                params: { 
                    province: '山东省',
                    limit: 3
                }
            });

            console.log('请求URL:', colors.cyan(`${BASE_URL}/get-proxies-by-province?province=山东省&limit=3`));
            console.log('响应数据:');
            console.log(JSON.stringify(multiProxyResponse.data, null, 2));

            if (multiProxyResponse.data.code === 0) {
                const proxies = multiProxyResponse.data.data;
                console.log(colors.green(`\n✅ 成功获取 ${proxies.length} 个山东省代理:`));
                proxies.forEach((proxy, index) => {
                    console.log(`   ${index + 1}. ${colors.bold(proxy.sk5)} (使用次数: ${proxy.usage_count})`);
                });
            }

            // 4. 测试代理状态报告
            console.log(colors.blue('\n📊 步骤4: 测试代理状态报告'));
            const reportData = {
                sk5: proxy.sk5,
                status: 'success',
                error_msg: null
            };

            console.log('报告数据:', JSON.stringify(reportData, null, 2));
            
            const reportResponse = await axios.post(`${BASE_URL}/report-proxy-status`, reportData);
            console.log('响应数据:');
            console.log(JSON.stringify(reportResponse.data, null, 2));

            if (reportResponse.data.code === 0) {
                console.log(colors.green('✅ 代理状态报告成功'));
            }

            // 5. 再次获取代理，验证使用次数是否增加
            console.log(colors.blue('\n🔄 步骤5: 验证使用次数更新'));
            const verifyResponse = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
                params: { province: '山东省' }
            });

            if (verifyResponse.data.code === 0 && verifyResponse.data.data) {
                const updatedProxy = verifyResponse.data.data;
                console.log(colors.green('✅ 验证结果:'));
                console.log(`   🔗 代理地址: ${colors.bold(updatedProxy.sk5)}`);
                console.log(`   📊 更新后使用次数: ${colors.bold(updatedProxy.usage_count)}`);
                
                if (updatedProxy.usage_count > proxy.usage_count) {
                    console.log(colors.green('✅ 使用次数已正确更新'));
                } else {
                    console.log(colors.yellow('⚠️  使用次数未发生变化'));
                }
            }

        } else {
            console.log(colors.red('❌ 未找到山东省的可用代理'));
            console.log('响应信息:', singleProxyResponse.data.msg);
        }

        // 6. 测试模糊匹配
        console.log(colors.blue('\n🔍 步骤6: 测试省份模糊匹配'));
        const fuzzyResponse = await axios.get(`${BASE_URL}/get-proxy-by-province`, {
            params: { province: '山东' }
        });

        console.log('请求URL:', colors.cyan(`${BASE_URL}/get-proxy-by-province?province=山东`));
        
        if (fuzzyResponse.data.code === 0 && fuzzyResponse.data.data) {
            console.log(colors.green('✅ 模糊匹配成功，找到山东省代理'));
        } else {
            console.log(colors.yellow('⚠️  模糊匹配未找到结果'));
        }

        console.log(colors.green('\n🎉 山东省代理IP接口测试完成!'));

    } catch (error) {
        console.error(colors.red('❌ 测试过程中发生错误:'));
        console.error('错误信息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 添加山东省测试数据
async function addShandongTestData() {
    console.log(colors.yellow('📝 添加山东省测试代理数据...'));
    
    const testProxies = [
        {
            sk5: '**************:1080',
            ip: '**************',
            province: '山东省',
            usage_count: 0,
            status: 'active'
        },
        {
            sk5: '**************:1080',
            ip: '**************',
            province: '山东省济南市',
            usage_count: 2,
            status: 'active'
        },
        {
            sk5: '**************:1080',
            ip: '**************',
            province: '山东省青岛市',
            usage_count: 1,
            status: 'active'
        }
    ];

    for (const proxy of testProxies) {
        try {
            const response = await axios.post(`${BASE_URL}/proxies`, proxy);
            if (response.data.code === 0) {
                console.log(colors.green(`✅ 添加代理: ${proxy.sk5} (${proxy.province})`));
            } else {
                console.log(colors.yellow(`⚠️  代理已存在: ${proxy.sk5}`));
            }
        } catch (error) {
            console.log(colors.yellow(`⚠️  添加代理失败: ${proxy.sk5} - ${error.response?.data?.msg || error.message}`));
        }
    }
}

// 显示使用说明
function showUsage() {
    console.log(colors.bold('\n📖 测试脚本使用说明:'));
    console.log('1. 确保服务器运行在 http://localhost:15001');
    console.log('2. 运行命令: node test-shandong-proxy.js');
    console.log('3. 观察测试结果和返回的IP信息');
    console.log('\n测试内容包括:');
    console.log('- 检查山东省代理可用性');
    console.log('- 获取单个山东省代理IP');
    console.log('- 批量获取山东省代理IP');
    console.log('- 测试代理状态报告');
    console.log('- 验证使用次数更新');
    console.log('- 测试省份模糊匹配');
}

// 主函数
async function main() {
    console.log(colors.bold('🚀 山东省代理IP接口测试脚本'));
    console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
    
    showUsage();
    
    // 等待2秒后开始测试
    console.log(colors.yellow('\n⏳ 2秒后开始测试...'));
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await testShandongProxy();
}

// 运行测试
main().catch(error => {
    console.error(colors.red('💥 测试脚本执行失败:'), error.message);
    process.exit(1);
});
