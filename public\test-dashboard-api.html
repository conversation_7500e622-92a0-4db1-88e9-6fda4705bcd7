<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 15px 0; padding: 15px; border-radius: 5px; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
        .stats-display { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007cba; }
        .stat-label { font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 仪表板API测试</h1>
        
        <div class="test-section">
            <h3>步骤1: 登录状态检查</h3>
            <button onclick="checkLoginStatus()">检查登录状态</button>
            <div id="loginStatusResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>步骤2: 用户信息API测试</h3>
            <button onclick="testUserInfo()">测试用户信息API</button>
            <div id="userInfoResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>步骤3: 统计数据API测试</h3>
            <button onclick="testStatsAPI()">测试统计数据API</button>
            <div id="statsResult" class="result"></div>
            
            <div class="stats-display" id="statsDisplay" style="display: none;">
                <div class="stat-card">
                    <div class="stat-number" id="visitCount">0</div>
                    <div class="stat-label">访问次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="uniqueIpCount">0</div>
                    <div class="stat-label">独立IP数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="scanCount">0</div>
                    <div class="stat-label">扫码数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successCount">0</div>
                    <div class="stat-label">成功数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failCount">0</div>
                    <div class="stat-label">失败数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="expireCount">0</div>
                    <div class="stat-label">过期数量</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>步骤4: 操作记录API测试</h3>
            <button onclick="testActionsAPI()">测试操作记录API</button>
            <div id="actionsResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>一键完整测试</h3>
            <button onclick="runFullAPITest()">运行完整API测试</button>
            <div id="fullTestResult" class="result"></div>
        </div>
    </div>

    <script>
        // 通用请求函数
        async function makeRequest(url, options = {}) {
            const defaultOptions = {
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            try {
                console.log(`🔄 请求: ${url}`, finalOptions);
                const response = await fetch(url, finalOptions);
                const data = await response.json();
                
                console.log(`✅ 响应: ${url}`, data);
                return { success: true, status: response.status, data: data };
            } catch (error) {
                console.error(`❌ 请求失败: ${url}`, error);
                return { success: false, error: error.message };
            }
        }
        
        // 检查登录状态
        async function checkLoginStatus() {
            const resultDiv = document.getElementById('loginStatusResult');
            resultDiv.textContent = '正在检查登录状态...';
            resultDiv.className = 'result info';
            
            const result = await makeRequest('/api/promotion/promoter/session-test');
            
            const resultText = `登录状态检查:
状态码: ${result.status}
响应: ${JSON.stringify(result.data, null, 2)}
当前cookies: ${document.cookie}`;
            
            resultDiv.textContent = resultText;
            
            if (result.success && result.data.code === 0 && result.data.data.hasPromotionUser) {
                resultDiv.className = 'result success';
                resultDiv.textContent += '\n\n✅ 用户已登录！';
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent += '\n\n❌ 用户未登录或session无效！';
            }
        }
        
        // 测试用户信息API
        async function testUserInfo() {
            const resultDiv = document.getElementById('userInfoResult');
            resultDiv.textContent = '正在测试用户信息API...';
            resultDiv.className = 'result info';
            
            const result = await makeRequest('/api/promotion/promoter/user-info');
            
            const resultText = `用户信息API测试:
状态码: ${result.status}
响应: ${JSON.stringify(result.data, null, 2)}`;
            
            resultDiv.textContent = resultText;
            
            if (result.success && result.data.code === 0) {
                resultDiv.className = 'result success';
                resultDiv.textContent += '\n\n✅ 用户信息获取成功！';
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent += '\n\n❌ 用户信息获取失败！';
            }
        }
        
        // 测试统计数据API
        async function testStatsAPI() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.textContent = '正在测试统计数据API...';
            resultDiv.className = 'result info';
            
            const result = await makeRequest('/api/promotion/promoter/today-stats');
            
            const resultText = `统计数据API测试:
状态码: ${result.status}
响应: ${JSON.stringify(result.data, null, 2)}`;
            
            resultDiv.textContent = resultText;
            
            if (result.success && result.data.code === 0) {
                const stats = result.data.data;
                
                // 更新显示卡片
                document.getElementById('visitCount').textContent = stats.visit_count;
                document.getElementById('uniqueIpCount').textContent = stats.unique_ip_count;
                document.getElementById('scanCount').textContent = stats.scan_count;
                document.getElementById('successCount').textContent = stats.success_count;
                document.getElementById('failCount').textContent = stats.fail_count;
                document.getElementById('expireCount').textContent = stats.expire_count;
                document.getElementById('statsDisplay').style.display = 'grid';
                
                const hasData = Object.values(stats).some(val => val > 0);
                
                resultDiv.textContent += `

数据详情:
访问次数: ${stats.visit_count}
独立IP数: ${stats.unique_ip_count}
扫码数量: ${stats.scan_count}
成功数量: ${stats.success_count}
失败数量: ${stats.fail_count}
过期数量: ${stats.expire_count}
有非零数据: ${hasData}`;
                
                if (hasData) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent += '\n\n✅ 统计数据获取成功，有非零数据！';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent += '\n\n⚠️ 统计数据全部为0！可能是数据库查询问题。';
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent += '\n\n❌ 统计数据获取失败！';
            }
        }
        
        // 测试操作记录API
        async function testActionsAPI() {
            const resultDiv = document.getElementById('actionsResult');
            resultDiv.textContent = '正在测试操作记录API...';
            resultDiv.className = 'result info';
            
            const result = await makeRequest('/api/promotion/promoter/today-actions?page=1&limit=5');
            
            const resultText = `操作记录API测试:
状态码: ${result.status}
响应: ${JSON.stringify(result.data, null, 2)}`;
            
            resultDiv.textContent = resultText;
            
            if (result.success && result.data.code === 0) {
                const actions = result.data.data;
                
                resultDiv.textContent += `

数据详情:
记录总数: ${actions.total}
当前页记录数: ${actions.list.length}`;
                
                if (actions.list.length > 0) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent += '\n\n✅ 操作记录获取成功！';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent += '\n\n⚠️ 没有操作记录！';
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent += '\n\n❌ 操作记录获取失败！';
            }
        }
        
        // 完整API测试
        async function runFullAPITest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.textContent = '🚀 开始完整API测试流程...\n';
            resultDiv.className = 'result info';
            
            try {
                // 1. 检查登录状态
                resultDiv.textContent += '1️⃣ 检查登录状态...\n';
                await checkLoginStatus();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 2. 测试用户信息
                resultDiv.textContent += '2️⃣ 测试用户信息API...\n';
                await testUserInfo();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 3. 测试统计数据
                resultDiv.textContent += '3️⃣ 测试统计数据API...\n';
                await testStatsAPI();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 4. 测试操作记录
                resultDiv.textContent += '4️⃣ 测试操作记录API...\n';
                await testActionsAPI();
                
                resultDiv.textContent += '\n🎉 完整API测试完成！请查看上方各个测试结果。';
                resultDiv.className = 'result success';
                
            } catch (error) {
                resultDiv.textContent += `\n❌ 完整API测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 页面加载时显示初始状态
        window.onload = function() {
            console.log('📋 仪表板API测试页面已加载');
            console.log('当前cookies:', document.cookie);
        };
    </script>
</body>
</html>
