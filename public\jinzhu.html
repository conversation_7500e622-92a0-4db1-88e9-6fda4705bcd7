<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="云控,云控学习,云控交流,云控测试,后台模板">
    <meta name="description" content="本后台仅供学习测试交流,进制用于违法或商业用途,否则后果自负">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="icon" href="images/favicon.ico">
    <link rel="stylesheet" href="lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="css/layuimini.css?v=*******" media="all">
    <link rel="stylesheet" href="css/themes/default.css" media="all">
    <link rel="stylesheet" href="lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <style id="layuimini-bg-color">
    </style>
</head>

<body class="layui-layout-body layuimini-all">
    <div class="layui-layout layui-layout-admin">

        <div class="layui-header header">
            <div class="layui-logo layuimini-logo"></div>

            <div class="layuimini-header-content">
                <a>
                    <div class="layuimini-tool"><i title="展开" class="fa fa-outdent" data-side-fold="1"></i></div>
                </a>

                <!--电脑端头部菜单-->
                <ul
                    class="layui-nav layui-layout-left layuimini-header-menu layuimini-menu-header-pc layuimini-pc-show">
                </ul>

                <!--手机端头部菜单-->
                <!-- <ul class="layui-nav layui-layout-left layuimini-header-menu layuimini-mobile-show">
                    <li class="layui-nav-item">
                        <a href="javascript:;"><i class="fa fa-list-ul"></i> 选择模块</a>
                        <dl class="layui-nav-child layuimini-menu-header-mobile">
                        </dl>
                    </li>
                </ul> -->

                <ul class="layui-nav layui-layout-right">

                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;" data-refresh="刷新"><i class="fa fa-refresh"></i></a>
                    </li>
                    <!-- <li class="layui-nav-item" lay-unselect>
                    <a href="javascript:;" data-clear="清理" class="layuimini-clear"><i class="fa fa-trash-o"></i></a>
                </li> -->
                    <li class="layui-nav-item mobile layui-hide-xs" lay-unselect>
                        <a href="javascript:;" data-check-screen="full"><i class="fa fa-arrows-alt"></i></a>
                    </li>
                    <li class="layui-nav-item layuimini-setting">
                        <a id="userName" href="javascript:;">未登录</a>
                        <dl class="layui-nav-child">
                            <!-- <dd>
                                <a href="javascript:;" layuimini-content-href="page/user-setting.html" data-title="基本资料"
                                    data-icon="fa fa-gears">基本资料<span class="layui-badge-dot"></span></a>
                            </dd> -->
                            <!-- <dd>
                                <a href="javascript:;" layuimini-content-href="page/user/user-password.html"
                                    data-title="修改密码" data-icon="fa fa-gears">修改密码</a>
                            </dd> -->
                            <dd>
                                <a href="javascript:;" class="change-pass">修改密码</a>
                            </dd>
                            <dd>
                                <hr>
                            </dd>
                            <dd>
                                <a href="javascript:;" class="login-out">退出登录</a>
                            </dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item layuimini-select-bgcolor" lay-unselect>
                        <a href="javascript:;" data-bgcolor="配色方案"><i class="fa fa-ellipsis-v"></i></a>
                    </li>
                </ul>
            </div>
        </div>

        <!--无限极左侧菜单-->
        <div class="layui-side layui-bg-black layuimini-menu-left">
        </div>

        <!--初始化加载层-->
        <div class="layuimini-loader">
            <div class="layuimini-loader-inner"></div>
        </div>

        <!--手机端遮罩层-->
        <div class="layuimini-make"></div>

        <!-- 移动导航 -->
        <div class="layuimini-site-mobile"><i class="layui-icon"></i></div>

        <div class="layui-body">

            <div class="layuimini-tab layui-tab-rollTool layui-tab" lay-filter="layuiminiTab" lay-allowclose="true">
                <ul class="layui-tab-title">
                    <li class="layui-this" id="layuiminiHomeTabId" lay-id=""></li>
                </ul>
                <div class="layui-tab-control">
                    <li class="layuimini-tab-roll-left layui-icon layui-icon-left"></li>
                    <li class="layuimini-tab-roll-right layui-icon layui-icon-right"></li>
                    <li class="layui-tab-tool layui-icon layui-icon-down">
                        <ul class="layui-nav close-box">
                            <li class="layui-nav-item">
                                <a href="javascript:;"><span class="layui-nav-more"></span></a>
                                <dl class="layui-nav-child">
                                    <dd><a href="javascript:;" layuimini-tab-close="current">关 闭 当 前</a></dd>
                                    <dd><a href="javascript:;" layuimini-tab-close="other">关 闭 其 他</a></dd>
                                    <dd><a href="javascript:;" layuimini-tab-close="all">关 闭 全 部</a></dd>
                                </dl>
                            </li>
                        </ul>
                    </li>
                </div>
                <div class="layui-tab-content">
                    <div id="layuiminiHomeTabIframe" class="layui-tab-item layui-show"></div>
                </div>
            </div>

        </div>
    </div>
    <script src="lib/layui/layui.js" charset="utf-8"></script>
    <script src="lib/axios/axios.js" charset="utf-8"></script>
    <script src="js/lay-config.js?v=2.0.0" charset="utf-8"></script>
    <script>
        layui.use(['jquery', 'layer', 'miniAdmin'], function () {
            const $ = layui.jquery
            const layer = layui.layer
            const miniAdmin = layui.miniAdmin
            $("#userName").text(window.localStorage.getItem("userName"));
            axios.get("getAppName").then(res => {
                document.title = res.data;
            })


            var options = {
                initUrl: "/user/getOneUserMenu",//"api/init.json",    // 初始化接口
                clearUrl: "api/clear.json", // 缓存清理接口
                urlHashLocation: false,      // 是否打开hash定位
                bgColorDefault: false,      // 主题默认配置
                multiModule: true,          // 是否开启多模块
                menuChildOpen: false,       // 是否默认展开菜单
                loadingTime: 0,             // 初始化加载时间
                pageAnim: true,             // iframe窗口动画
                maxTabNum: 20,              // 最大的tab打开数量
            };
            miniAdmin.render(options);


            $('.login-out').on("click", function () {
                //请求登出接口
                axios.post('/logout').then(res => {
                    console.log("退出成功", res.data);
                    location.href = '/'; //后台主页
                }).catch(err => {
                    layer.msg("退出失败:" + err.message)
                })
            });

            $('.change-pass').on("click", function () {
                layer.msg("改密码")
                //请求改密接口
                layer.prompt({
                    formType: 0,
                    value: '',
                    title: '请输入新密码',
                    // area: ['800px', '350px'] // 自定义文本域宽高
                }, function (value, index, elem) {
                    if (!value || value == "") {
                        layer.msg("请输入新密码后再提交")
                        return false
                    }
                    axios.post('/user/changePass', { userPass: value }).then(res => {
                        if (res.data.code == 0) {
                            layer.msg("修改成功")
                            location.href = '/'; //后台主页
                        } else {
                            layer.alert(res.data.msg)
                        }
                    }).catch(err => {
                        layer.alert(err.message)
                    })
                    layer.close(index); // 关闭层
                });
            });


        });
    </script>
</body>

</html>