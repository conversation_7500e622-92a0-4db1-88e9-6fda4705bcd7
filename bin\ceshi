#!/usr/bin/env node


const { getServerUrl, setServerUrl, serverPort } = require('../serverOpt');
const app = require('../app');


// var debug = require('debug')('a-sokio-yun:server');
var http = require('http');
// app.set('port', serverPort);

// const server = http.createServer(app);
// server.listen(serverPort);
const server = app.listen(serverPort);




server.on('error', onError);

server.on('listening', async () => {
  const IP = await getIPAddress()
  // console.log("IP", IP);
  setServerUrl(IP)
  console.log(`addr:${JSON.stringify(server.address())}地址http://${getServerUrl()}:${serverPort}`)
});


let retryCount = 0;
const MAX_RETRY = 5;

function onError(error) {
  if (error.syscall !== 'listen') {
    throw error;
  }
  
  switch (error.code) {
    case 'EACCES':
      console.error(`端口 ${serverPort} 需要管理员权限`);
      process.exit(1);
      break;
    case 'EADDRINUSE':
      retryCount++;
      if (retryCount <= MAX_RETRY) {
        const newPort = serverPort + retryCount;
        console.warn(`端口 ${serverPort} 被占用，尝试端口 ${newPort}`);
        server.listen(newPort);
      } else {
        console.error(`无法找到可用端口，已尝试 ${MAX_RETRY} 次`);
        process.exit(1);
      }
      break;
    default:
      throw error;
  }
}


async function getIPAddress() {
  if (process.env.NODE_ENV === 'development') {
    console.log("开发环境");
    return getLocalIPAddress();
  } else {
    console.log("生产环境");
    return await getPublicIPAddress();
  }
}


function getLocalIPAddress() {
  const os = require('os');
  const interfaces = os.networkInterfaces();
  for (const devName in interfaces) {
    const iface = interfaces[devName];
    for (let i = 0; i < iface.length; i++) {
      const alias = iface[i];
      if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
        return alias.address;
      }
    }
  }
  return '0.0.0.0';
}

function getPublicIPAddress() {
  return new Promise((resolve, reject) => {
    http.get('http://ipinfo.io/ip', (res) => {
      let ip = '';
      res.on('data', chunk => {
        ip += chunk;
      });
      res.on('end', () => {
        resolve(ip);
      });
    }).on('error', reject);
  });
}