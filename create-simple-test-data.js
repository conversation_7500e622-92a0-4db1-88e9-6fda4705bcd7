const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 创建简单的测试数据
async function createSimpleTestData() {
  console.log('🔧 创建简单的测试数据...');
  
  const testUserId = '1001';
  
  console.log(`\n📋 用户ID: ${testUserId}`);
  console.log('─'.repeat(60));
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 清理旧数据
    console.log('\n1️⃣ 清理旧数据...');
    await connection.execute(`DELETE FROM promotion_daily_stats WHERE promotion_user_id = ?`, [testUserId]);
    console.log('清理了promotion_daily_stats表');
    
    // 2. 直接插入今日数据
    console.log('\n2️⃣ 直接插入今日数据...');
    const [insertResult] = await connection.execute(
      `INSERT INTO promotion_daily_stats 
       (promotion_user_id, stat_date, visit_count, unique_ip_count, 
        scan_count, success_count, fail_count, expire_count, created_at, updated_at)
       VALUES (?, CURDATE(), 5, 1, 0, 5, 0, 0, NOW(), NOW())`,
      [testUserId]
    );
    
    console.log(`✅ 插入成功，ID: ${insertResult.insertId}`);
    
    // 3. 验证数据
    console.log('\n3️⃣ 验证数据...');
    const [verifyResult] = await connection.execute(
      `SELECT *, DATE(stat_date) as date_str FROM promotion_daily_stats 
       WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    if (verifyResult.length > 0) {
      console.log('✅ 验证成功:');
      const data = verifyResult[0];
      console.log(`   日期: ${data.date_str}`);
      console.log(`   访问次数: ${data.visit_count}`);
      console.log(`   独立IP数: ${data.unique_ip_count}`);
      console.log(`   扫码数量: ${data.scan_count}`);
      console.log(`   成功数量: ${data.success_count}`);
      console.log(`   失败数量: ${data.fail_count}`);
      console.log(`   过期数量: ${data.expire_count}`);
    } else {
      console.log('❌ 验证失败');
    }
    
    // 4. 测试API查询
    console.log('\n4️⃣ 测试API查询...');
    const [apiResult] = await connection.execute(
      `SELECT
        visit_count,
        unique_ip_count,
        scan_count,
        success_count,
        fail_count,
        expire_count
       FROM promotion_daily_stats
       WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    if (apiResult.length > 0) {
      console.log('✅ API查询成功:');
      const apiData = apiResult[0];
      console.log('   API返回数据:', {
        visit_count: parseInt(apiData.visit_count) || 0,
        unique_ip_count: parseInt(apiData.unique_ip_count) || 0,
        scan_count: parseInt(apiData.scan_count) || 0,
        success_count: parseInt(apiData.success_count) || 0,
        fail_count: parseInt(apiData.fail_count) || 0,
        expire_count: parseInt(apiData.expire_count) || 0,
      });
    } else {
      console.log('❌ API查询失败');
    }
    
  } catch (error) {
    console.error('❌ 创建失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
  
  console.log('\n🎉 测试数据创建完成！');
  
  console.log('\n🌐 现在可以测试:');
  console.log('1. 重启服务器');
  console.log('2. 访问推广用户登录页面: http://localhost:15001/page/promotion/promoter-login.html');
  console.log('3. 使用用户ID: 1001, 密码: 123456 登录');
  console.log('4. 应该能看到: 访问次数=5, 独立IP数=1, 成功数量=5');
}

// 运行创建
if (require.main === module) {
  createSimpleTestData().catch(console.error);
}

module.exports = createSimpleTestData;
