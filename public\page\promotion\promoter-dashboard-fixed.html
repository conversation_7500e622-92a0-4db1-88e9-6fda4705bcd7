<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户仪表板</title>
    <link rel="stylesheet" href="/layui/css/layui.css">
    <style>
        body { background-color: #f2f2f2; }
        .dashboard-container { padding: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 32px; font-weight: bold; color: #1890ff; margin-bottom: 8px; }
        .stat-label { color: #666; font-size: 14px; }
        .header-info { background: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center; }
        .actions-section { background: white; padding: 20px; border-radius: 8px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 头部信息 -->
        <div class="header-info">
            <div>
                <h2>推广用户仪表板</h2>
                <div id="userInfo">加载中...</div>
                <div>今日日期: <span id="todayDate"></span></div>
            </div>
            <div>
                <button class="layui-btn layui-btn-primary" onclick="logout()">退出登录</button>
            </div>
        </div>

        <!-- 统计数据卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="visitCount">0</div>
                <div class="stat-label">访问次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uniqueIpCount">0</div>
                <div class="stat-label">独立IP数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="scanCount">0</div>
                <div class="stat-label">扫码数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successCount">0</div>
                <div class="stat-label">成功数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failCount">0</div>
                <div class="stat-label">失败数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="expireCount">0</div>
                <div class="stat-label">过期数量</div>
            </div>
        </div>

        <!-- 操作记录 -->
        <div class="actions-section">
            <h3>今日操作记录</h3>
            <table class="layui-table">
                <thead>
                    <tr>
                        <th>用户ID</th>
                        <th>时间</th>
                        <th>IP地址</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody id="actionTableBody">
                    <tr><td colspan="4" style="text-align: center; padding: 40px; color: #999;">加载中...</td></tr>
                </tbody>
            </table>
            <div id="pagination"></div>
        </div>
    </div>

    <script src="/layui/layui.js"></script>
    <script>
        layui.use(['layer', 'laypage'], function() {
            const layer = layui.layer;
            const laypage = layui.laypage;
            
            let currentPage = 1;
            const pageSize = 10;

            // 通用请求函数
            async function makeRequest(url, options = {}) {
                const defaultOptions = {
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                };
                
                const finalOptions = { ...defaultOptions, ...options };
                
                try {
                    console.log(`🔄 请求: ${url}`, finalOptions);
                    const response = await fetch(url, finalOptions);
                    const data = await response.json();
                    
                    console.log(`✅ 响应: ${url}`, data);
                    return { success: true, status: response.status, data: data };
                } catch (error) {
                    console.error(`❌ 请求失败: ${url}`, error);
                    return { success: false, error: error.message };
                }
            }

            // 加载用户信息
            async function loadUserInfo() {
                console.log('📋 开始加载用户信息...');
                
                const result = await makeRequest('/api/promotion/promoter/user-info');
                
                if (result.success && result.data.code === 0) {
                    const userInfo = result.data.data;
                    document.getElementById('userInfo').textContent = 
                        `推广用户: ${userInfo.username} (ID: ${userInfo.user_id})`;
                    console.log('✅ 用户信息加载成功:', userInfo);
                } else if (result.data?.code === -1 && result.data?.msg?.includes('登录')) {
                    console.log('❌ 用户未登录，跳转到登录页');
                    window.location.href = '/page/promotion/promoter-login.html';
                } else {
                    console.error('❌ 获取用户信息失败:', result.data?.msg || result.error);
                    document.getElementById('userInfo').textContent = '获取用户信息失败';
                }
            }

            // 加载统计数据
            async function loadStats() {
                console.log('📊 开始加载统计数据...');
                
                const result = await makeRequest('/api/promotion/promoter/today-stats');
                
                if (result.success && result.data.code === 0) {
                    const stats = result.data.data;
                    
                    // 更新显示
                    document.getElementById('visitCount').textContent = stats.visit_count || 0;
                    document.getElementById('uniqueIpCount').textContent = stats.unique_ip_count || 0;
                    document.getElementById('scanCount').textContent = stats.scan_count || 0;
                    document.getElementById('successCount').textContent = stats.success_count || 0;
                    document.getElementById('failCount').textContent = stats.fail_count || 0;
                    document.getElementById('expireCount').textContent = stats.expire_count || 0;
                    
                    console.log('✅ 统计数据加载成功:', stats);
                    
                    // 检查数据是否为0
                    const hasData = Object.values(stats).some(val => val > 0);
                    if (!hasData) {
                        console.warn('⚠️ 所有统计数据都为0');
                    }
                    
                } else if (result.data?.code === -1 && result.data?.msg?.includes('登录')) {
                    console.log('❌ 用户未登录，跳转到登录页');
                    window.location.href = '/page/promotion/promoter-login.html';
                } else {
                    console.error('❌ 获取统计数据失败:', result.data?.msg || result.error);
                }
            }

            // 加载操作记录
            async function loadActions(page = 1) {
                console.log(`📝 开始加载操作记录 (页码: ${page})...`);
                
                const result = await makeRequest(`/api/promotion/promoter/today-actions?page=${page}&limit=${pageSize}`);
                
                if (result.success && result.data.code === 0) {
                    const data = result.data.data;
                    renderActionTable(data.list);
                    renderPagination(data.total, page);
                    console.log('✅ 操作记录加载成功，记录数:', data.list.length);
                } else if (result.data?.code === -1 && result.data?.msg?.includes('登录')) {
                    console.log('❌ 用户未登录，跳转到登录页');
                    window.location.href = '/page/promotion/promoter-login.html';
                } else {
                    console.error('❌ 获取操作记录失败:', result.data?.msg || result.error);
                    document.getElementById('actionTableBody').innerHTML = 
                        '<tr><td colspan="4" style="text-align: center; padding: 40px; color: #999;">暂无数据</td></tr>';
                }
            }

            // 渲染操作记录表格
            function renderActionTable(list) {
                const tbody = document.getElementById('actionTableBody');

                if (list.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 40px; color: #999;">暂无数据</td></tr>';
                    return;
                }

                const html = list.map(item => `
                    <tr>
                        <td>${item.user_id || '-'}</td>
                        <td>${item.time}</td>
                        <td>${item.ip}</td>
                        <td>
                            <span class="layui-badge ${getStatusClass(item.status)}">${item.status}</span>
                        </td>
                    </tr>
                `).join('');

                tbody.innerHTML = html;
            }

            // 获取状态样式类
            function getStatusClass(status) {
                switch (status) {
                    case '登录成功': return 'layui-bg-green';
                    case '登录失败': return 'layui-bg-red';
                    case '请求过期': return 'layui-bg-orange';
                    case '扫码': return 'layui-bg-blue';
                    default: return '';
                }
            }

            // 渲染分页
            function renderPagination(total, current) {
                if (total <= pageSize) {
                    document.getElementById('pagination').innerHTML = '';
                    return;
                }

                laypage.render({
                    elem: 'pagination',
                    count: total,
                    limit: pageSize,
                    curr: current,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            loadActions(obj.curr);
                        }
                    }
                });
            }

            // 退出登录
            window.logout = async function () {
                const confirmed = await new Promise(resolve => {
                    layer.confirm('确定要退出登录吗？', { icon: 3, title: '提示' }, function (index) {
                        layer.close(index);
                        resolve(true);
                    }, function() {
                        resolve(false);
                    });
                });
                
                if (!confirmed) return;
                
                const result = await makeRequest('/api/promotion/promoter/logout', { method: 'POST' });
                
                if (result.success) {
                    layer.msg('已退出登录', { icon: 1 }, function () {
                        window.location.href = '/page/promotion/promoter-login.html';
                    });
                } else {
                    console.error('退出登录失败:', result.error);
                    window.location.href = '/page/promotion/promoter-login.html';
                }
            }

            // 页面初始化
            document.addEventListener('DOMContentLoaded', function () {
                console.log('🚀 推广用户仪表板页面加载完成，开始初始化数据...');
                
                // 显示今日日期
                const today = new Date();
                document.getElementById('todayDate').textContent = today.toLocaleDateString('zh-CN');

                // 加载数据
                loadUserInfo();
                loadStats();
                loadActions();
            });
        });
    </script>
</body>
</html>
