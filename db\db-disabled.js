// MongoDB功能已完全禁用
// 这个文件替代原来的db.js，避免MongoDB连接

console.log('🚫 MongoDB数据库连接已禁用');
console.log('✅ 系统将仅使用MySQL数据库');

// 创建一个模拟的mongoose对象以保持兼容性
const mockMongoose = {
    connection: {
        readyState: 0, // 0 = disconnected
        on: () => {},
        once: () => {},
        db: {
            admin: () => ({
                ping: () => Promise.reject(new Error('MongoDB已禁用'))
            })
        }
    },
    connect: () => Promise.reject(new Error('MongoDB已禁用')),
    set: () => {},
    model: () => {
        throw new Error('MongoDB模型功能已禁用，请使用MySQL');
    },
    Schema: function() {
        throw new Error('MongoDB Schema功能已禁用，请使用MySQL');
    }
};

// 模拟测试连接函数
const testConnection = async () => {
    console.log('MongoDB连接测试跳过 - 功能已禁用');
    return false;
};

// 模拟获取连接状态函数
const getConnectionStatus = () => {
    return 0; // 0 = disconnected
};

// 导出模拟对象以保持兼容性
module.exports = mockMongoose;
module.exports.testConnection = testConnection;
module.exports.getConnectionStatus = getConnectionStatus;

console.log('💡 提示: 如需数据库功能，请使用MySQL相关API');
