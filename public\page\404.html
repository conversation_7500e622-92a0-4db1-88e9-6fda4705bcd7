<!DOCTYPE html>
<html>
<head>
    
    <meta charset="UTF-8">
    <title>404</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <style>
        .error .clip .shadow {height:180px;}
        .error .clip:nth-of-type(2) .shadow {width:130px;}
        .error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:250px;}
        .error .digit {width:150px;height:150px;line-height:150px;font-size:120px;font-weight:bold;}
        .error h2 {font-size:32px;}
        .error .msg {top:-190px;left:30%;width:80px;height:80px;line-height:80px;font-size:32px;}
        .error span.triangle {top:70%;right:0%;border-left:20px solid #535353;border-top:15px solid transparent;border-bottom:15px solid transparent;}
        .error .container-error-404 {top: 50%;margin-top: 250px;position:relative;height:250px;padding-top:40px;}
        .error .container-error-404 .clip {display:inline-block;transform:skew(-45deg);}
        .error .clip .shadow {overflow:hidden;}
        .error .clip:nth-of-type(2) .shadow {overflow:hidden;position:relative;box-shadow:inset 20px 0px 20px -15px rgba(150,150,150,0.8),20px 0px 20px -15px rgba(150,150,150,0.8);}
        .error .clip:nth-of-type(3) .shadow:after,.error .clip:nth-of-type(1) .shadow:after {content:"";position:absolute;right:-8px;bottom:0px;z-index:9999;height:100%;width:10px;background:linear-gradient(90deg,transparent,rgba(173,173,173,0.8),transparent);border-radius:50%;}
        .error .clip:nth-of-type(3) .shadow:after {left:-8px;}
        .error .digit {position:relative;top:8%;color:white;background:#1E9FFF;border-radius:50%;display:inline-block;transform:skew(45deg);}
        .error .clip:nth-of-type(2) .digit {left:-10%;}
        .error .clip:nth-of-type(1) .digit {right:-20%;}
        .error .clip:nth-of-type(3) .digit {left:-20%;}
        .error h2 {font-size:24px;color:#A2A2A2;font-weight:bold;padding-bottom:20px;}
        .error .tohome {font-size:16px;color:#07B3F9;}
        .error .msg {position:relative;z-index:9999;display:block;background:#535353;color:#A2A2A2;border-radius:50%;font-style:italic;}
        .error .triangle {position:absolute;z-index:999;transform:rotate(45deg);content:"";width:0;height:0;}
        @media(max-width:767px) {.error .clip .shadow {height:100px;}
            .error .clip:nth-of-type(2) .shadow {width:80px;}
            .error .clip:nth-of-type(1) .shadow,.error .clip:nth-of-type(3) .shadow {width:100px;}
            .error .digit {width:80px;height:80px;line-height:80px;font-size:52px;}
            .error h2 {font-size:18px;}
            .error .msg {top:-110px;left:15%;width:40px;height:40px;line-height:40px;font-size:18px;}
            .error span.triangle {top:70%;right:-3%;border-left:10px solid #535353;border-top:8px solid transparent;border-bottom:8px solid transparent;}
            .error .container-error-404 {height:150px;}
        }
    </style>
</head>
<body>
<div class="error">
    <div class="container-floud">
        <div style="text-align: center">
            <div class="container-error-404">
                <div class="clip">
                    <div class="shadow">
                        <span class="digit thirdDigit"></span>
                    </div>
                </div>
                <div class="clip">
                    <div class="shadow">
                        <span class="digit secondDigit"></span>
                    </div>
                </div>
                <div class="clip">
                    <div class="shadow">
                        <span class="digit firstDigit"></span>
                    </div>
                </div>
                <div class="msg">OH!
                    <span class="triangle"></span>
                </div>
            </div>
            <h2 class="h1">很抱歉，你访问的页面找不到了</h2>
        </div>
    </div>
</div>
<script src="/lib/layui/layui.js" charset="utf-8"></script>
<script>
    function randomNum() {
        return Math.floor(Math.random() * 9) + 1;
    }

    var loop1, loop2, loop3, time = 30, i = 0, number;
    loop3 = setInterval(function () {
        if (i > 40) {
            clearInterval(loop3);
            document.querySelector('.thirdDigit').textContent = 4;
        } else {
            document.querySelector('.thirdDigit').textContent = randomNum();
            i++;
        }
    }, time);
    loop2 = setInterval(function () {
        if (i > 80) {
            clearInterval(loop2);
            document.querySelector('.secondDigit').textContent = 0;
        } else {
            document.querySelector('.secondDigit').textContent = randomNum();
            i++;
        }
    }, time);
    loop1 = setInterval(function () {
        if (i > 100) {
            clearInterval(loop1);
            document.querySelector('.firstDigit').textContent = 4;
        } else {
            document.querySelector('.firstDigit').textContent = randomNum();
            i++;
        }
    }, time);
</script>
</body>
</html>