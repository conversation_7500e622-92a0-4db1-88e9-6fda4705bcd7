<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户登录</title>
    <link rel="stylesheet" href="/layui/css/layui.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: #666;
            font-size: 14px;
        }

        .login-form {
            margin-top: 20px;
        }

        .form-item {
            margin-bottom: 20px;
        }

        .form-item label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-item input {
            width: 100%;
            height: 45px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 0 15px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-item input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-btn {
            width: 100%;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .footer-text {
            color: #999;
            font-size: 12px;
        }

        .error-message {
            color: #ff4757;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .icon-user,
        .icon-lock {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .input-wrapper {
            position: relative;
        }

        .input-wrapper input {
            padding-left: 45px;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 20px;
            }
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-title">推广用户登录</div>
            <div class="login-subtitle">请输入您的推广用户凭据</div>
        </div>

        <form class="login-form" id="loginForm">
            <div class="form-item">
                <label for="user_id">用户ID</label>
                <div class="input-wrapper">
                    <i class="layui-icon layui-icon-username icon-user"></i>
                    <input type="text" id="user_id" name="user_id" placeholder="请输入用户ID" required>
                </div>
                <div class="error-message" id="userIdError"></div>
            </div>

            <div class="form-item">
                <label for="password">密码</label>
                <div class="input-wrapper">
                    <i class="layui-icon layui-icon-password icon-lock"></i>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                </div>
                <div class="error-message" id="passwordError"></div>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <span id="btnText">登录</span>
                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" id="loadingIcon"
                    style="display: none;"></i>
            </button>
        </form>

        <div class="login-footer">
            <div class="footer-text">© 2024 推广用户系统</div>
        </div>
    </div>

    <script src="/layui/layui.js"></script>
    <script>
        // 直接使用，不依赖layui.use异步加载
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🚀 推广用户登录页面DOM加载完成');

            // 等待layui加载完成
            const waitForLayui = setInterval(() => {
                if (window.layui && window.layui.layer) {
                    clearInterval(waitForLayui);
                    initLoginPage();
                }
            }, 100);
        });

        function initLoginPage() {
            console.log('🎯 初始化登录页面');
            const layer = layui.layer;

            // 表单提交处理
            document.getElementById('loginForm').addEventListener('submit', async function (e) {
                e.preventDefault();

                const formData = new FormData(this);
                const loginData = {
                    user_id: formData.get('user_id').trim(),
                    password: formData.get('password').trim()
                };

                // 验证输入
                if (!validateInput(loginData)) {
                    return;
                }

                // 显示加载状态
                setLoadingState(true);

                try {
                    console.log('🔄 发送登录请求:', loginData);

                    const response = await fetch('/api/promotion/promoter/login', {
                        method: 'POST',
                        credentials: 'include',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(loginData)
                    });

                    const result = await response.json();

                    console.log('📥 登录API响应:', {
                        status: response.status,
                        data: result,
                        cookies: document.cookie
                    });

                    if (result.code === 0) {
                        // 登录成功
                        console.log('✅ 登录成功，准备跳转');
                        console.log('登录响应数据:', result);

                        // 立即跳转到干净版仪表板
                        console.log('🔄 立即跳转到干净版仪表板');
                        window.location.href = '/clean-promoter-dashboard.html';

                    } else {
                        // 登录失败
                        console.error('❌ 登录失败:', result.msg);
                        layer.msg(result.msg || '登录失败', {
                            icon: 2,
                            time: 3000
                        });

                        // 显示具体错误信息
                        if (result.msg && result.msg.includes('用户ID')) {
                            showFieldError('userIdError', result.msg);
                        } else if (result.msg && result.msg.includes('密码')) {
                            showFieldError('passwordError', result.msg);
                        }
                    }

                } catch (error) {
                    console.error('登录请求失败:', error);
                    layer.msg('网络错误，请稍后重试', {
                        icon: 2,
                        time: 3000
                    });
                } finally {
                    setLoadingState(false);
                }
            });

            // 输入验证
            function validateInput(data) {
                clearErrors();

                let isValid = true;

                if (!data.user_id) {
                    showFieldError('userIdError', '请输入用户ID');
                    isValid = false;
                }

                if (!data.password) {
                    showFieldError('passwordError', '请输入密码');
                    isValid = false;
                }

                return isValid;
            }

            // 显示字段错误
            function showFieldError(elementId, message) {
                const errorElement = document.getElementById(elementId);
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }

            // 清除错误信息
            function clearErrors() {
                const errorElements = document.querySelectorAll('.error-message');
                errorElements.forEach(element => {
                    element.style.display = 'none';
                    element.textContent = '';
                });
            }

            // 设置加载状态
            function setLoadingState(loading) {
                const loginBtn = document.getElementById('loginBtn');
                const btnText = document.getElementById('btnText');
                const loadingIcon = document.getElementById('loadingIcon');
                const form = document.getElementById('loginForm');

                if (loading) {
                    loginBtn.classList.add('loading');
                    btnText.textContent = '登录中...';
                    loadingIcon.style.display = 'inline-block';
                    form.classList.add('loading');
                } else {
                    loginBtn.classList.remove('loading');
                    btnText.textContent = '登录';
                    loadingIcon.style.display = 'none';
                    form.classList.remove('loading');
                }
            }

            // 输入框焦点事件 - 清除错误信息
            document.querySelectorAll('input').forEach(input => {
                input.addEventListener('focus', function () {
                    const errorElement = this.parentNode.parentNode.querySelector('.error-message');
                    if (errorElement) {
                        errorElement.style.display = 'none';
                    }
                });
            });

            // 页面加载完成后的初始化
            console.log('🚀 推广用户登录页面已加载');

            // 检查URL参数
            const urlParams = new URLSearchParams(window.location.search);

            // 如果有用户ID参数，预填到表单中
            const userId = urlParams.get('user_id');
            if (userId) {
                document.getElementById('user_id').value = userId;
                console.log('从URL参数预填用户ID:', userId);
            }

            // 如果有错误信息则显示
            const error = urlParams.get('error');
            if (error) {
                layer.msg(decodeURIComponent(error), {
                    icon: 2,
                    time: 3000
                });
            }
        }
    </script>
</body>

</html>