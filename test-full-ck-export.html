<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整CK数据导出测试</title>
    <!-- SheetJS库用于Excel导出 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .test-section h3 {
            color: #764ba2;
            margin-top: 0;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .ck-data {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }

        .ck-data h4 {
            margin-top: 0;
            color: #495057;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background: #e9ecef;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .comparison-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }

        .comparison-item h4 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 完整CK数据导出测试</h1>

        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>此测试用于验证Excel导出功能是否正确保存完整的CK数据，而不是截断的预览数据。</p>
            <p><strong>测试重点：</strong></p>
            <ul>
                <li>✅ 导出的Excel文件中CK数据应该是完整的</li>
                <li>✅ 不应该有"..."截断标记</li>
                <li>✅ 长CK数据应该完整保存</li>
                <li>✅ 特殊字符应该正确处理</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔬 测试数据</h3>
            <p>以下是包含长CK数据的测试记录：</p>
            
            <div class="ck-data">
                <h4>测试CK数据1 - JSON格式 (长度: <span id="ck1-length"></span>)</h4>
                <div id="ck1-content"></div>
            </div>

            <div class="ck-data">
                <h4>测试CK数据2 - 传统Cookie格式 (长度: <span id="ck2-length"></span>)</h4>
                <div id="ck2-content"></div>
            </div>

            <div class="ck-data">
                <h4>测试CK数据3 - 包含特殊字符 (长度: <span id="ck3-length"></span>)</h4>
                <div id="ck3-content"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 测试操作</h3>
            <button class="btn" onclick="generateLongCKTestData()">🔄 生成长CK测试数据</button>
            <button class="btn btn-success" onclick="exportFullCKDataToExcel()">📊 导出完整CK数据</button>
            <button class="btn" onclick="showCKComparison()">🔍 对比CK数据</button>
        </div>

        <div class="comparison" id="ckComparison" style="display: none;">
            <div class="comparison-item">
                <h4>🖥️ 页面显示的CK预览</h4>
                <div id="ckPreview" class="ck-data"></div>
            </div>
            <div class="comparison-item">
                <h4>📊 Excel中保存的完整CK</h4>
                <div id="ckFull" class="ck-data"></div>
            </div>
        </div>

        <div id="statusContainer" class="status" style="display: none;">
            <h4>测试状态</h4>
            <div id="statusContent"></div>
        </div>
    </div>

    <script>
        // 测试数据
        let longCKTestData = [];

        // 生成长CK测试数据
        function generateLongCKTestData() {
            console.log('🔄 生成长CK测试数据...');

            // 生成超长的JSON格式CK数据
            const longJsonCK = JSON.stringify([
                {"name":"sessionid","value":"abc123def456ghi789jkl012mno345pqr678stu901vwx234yz567","domain":".douyin.com","path":"/","expires":"2024-12-31T23:59:59.999Z","httpOnly":true,"secure":true},
                {"name":"bd_ticket_guard_client_data","value":"eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxfQ%3D%3D","domain":".douyin.com","path":"/","expires":"2024-12-31T23:59:59.999Z","httpOnly":false,"secure":true},
                {"name":"uid","value":"user123456789012345678901234567890","domain":".douyin.com","path":"/","expires":"2024-12-31T23:59:59.999Z","httpOnly":true,"secure":true},
                {"name":"token","value":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c","domain":".douyin.com","path":"/","expires":"2024-12-31T23:59:59.999Z","httpOnly":true,"secure":true}
            ]);

            // 生成超长的传统Cookie格式数据
            const longTraditionalCK = 'sessionid=abc123def456ghi789jkl012mno345pqr678stu901vwx234yz567; uid=user123456789012345678901234567890; token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxfQ%3D%3D; device_id=1234567890123456789; install_id=9876543210987654321; ttreq=1$abc123def456; passport_csrf_token=xyz789abc123; passport_csrf_token_default=xyz789abc123';

            // 生成包含特殊字符的CK数据
            const specialCharsCK = 'special_cookie="value with spaces & symbols < > \' \" \\ / \n \r \t"; chinese_cookie="中文测试数据包含特殊字符！@#$%^&*()"; emoji_cookie="🎉🚀📊💾🔧"; json_in_cookie=\'{"nested": {"data": "with \\"quotes\\" and \\\'apostrophes\\\'", "array": [1, 2, 3]}, "special": "chars & symbols"}\'; url_encoded="https%3A%2F%2Fexample.com%2Fpath%3Fparam%3Dvalue%26other%3Ddata"';

            longCKTestData = [
                {
                    promotion_user_id: '1001',
                    user_id: 'user_long_ck_1',
                    time: '2024-01-20 10:30:15',
                    ip: '**************',
                    ip_province: '河南',
                    status: '登录成功',
                    douyin_name: '长CK测试用户1',
                    douyin_id: 'dy_long_ck_1',
                    ck: longJsonCK
                },
                {
                    promotion_user_id: '1002',
                    user_id: 'user_long_ck_2',
                    time: '2024-01-20 11:15:30',
                    ip: '*************',
                    ip_province: '广东',
                    status: '登录成功',
                    douyin_name: '长CK测试用户2',
                    douyin_id: 'dy_long_ck_2',
                    ck: longTraditionalCK
                },
                {
                    promotion_user_id: '1003',
                    user_id: 'user_special_ck',
                    time: '2024-01-20 12:45:20',
                    ip: '***************',
                    ip_province: '北京',
                    status: '登录成功',
                    douyin_name: '特殊字符测试用户',
                    douyin_id: 'dy_special_ck',
                    ck: specialCharsCK
                }
            ];

            // 显示CK数据
            document.getElementById('ck1-content').textContent = longJsonCK;
            document.getElementById('ck1-length').textContent = longJsonCK.length;

            document.getElementById('ck2-content').textContent = longTraditionalCK;
            document.getElementById('ck2-length').textContent = longTraditionalCK.length;

            document.getElementById('ck3-content').textContent = specialCharsCK;
            document.getElementById('ck3-length').textContent = specialCharsCK.length;

            updateStatus('长CK测试数据已生成，共 ' + longCKTestData.length + ' 条记录', 'success');
        }

        // 导出完整CK数据为Excel
        function exportFullCKDataToExcel() {
            console.log('📊 开始导出完整CK数据为Excel...');

            if (!longCKTestData || longCKTestData.length === 0) {
                updateStatus('没有可导出的数据，请先生成长CK测试数据', 'error');
                return;
            }

            try {
                // 准备Excel数据（使用完整CK数据）
                const excelData = prepareFullCKExcelData(longCKTestData);
                
                // 创建工作簿
                const workbook = createWorkbook(excelData);
                
                // 生成文件名
                const fileName = `推广操作记录_完整CK_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
                
                // 导出Excel文件
                exportWorkbook(workbook, fileName);
                
                updateStatus(`完整CK数据Excel文件导出成功：${fileName}`, 'success');
                console.log('✅ 完整CK数据Excel导出完成');

            } catch (error) {
                console.error('❌ Excel导出失败:', error);
                updateStatus('Excel导出失败: ' + error.message, 'error');
            }
        }

        // 准备完整CK数据的Excel数据
        function prepareFullCKExcelData(actions) {
            const headers = [
                '推广用户ID',
                '用户ID', 
                '操作时间',
                'IP地址',
                'IP省份',
                '操作状态',
                '抖音名称',
                '抖音号',
                'CK数据长度',
                'CK完整数据'
            ];

            const rows = actions.map(action => {
                return [
                    action.promotion_user_id || '',
                    action.user_id || '',
                    action.time || '',
                    action.ip || '',
                    action.ip_province || '',
                    action.status || '',
                    action.douyin_name || '',
                    action.douyin_id || '',
                    action.ck ? action.ck.length : 0,
                    action.ck || '' // 完整的CK数据
                ];
            });

            return { headers, rows };
        }

        // 创建工作簿
        function createWorkbook(data) {
            const wsData = [data.headers, ...data.rows];
            const ws = XLSX.utils.aoa_to_sheet(wsData);

            const colWidths = [
                { wch: 12 }, { wch: 15 }, { wch: 20 }, { wch: 15 }, { wch: 10 },
                { wch: 12 }, { wch: 20 }, { wch: 15 }, { wch: 12 }, { wch: 100 }
            ];
            ws['!cols'] = colWidths;

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "完整CK数据");
            return wb;
        }

        // 导出工作簿
        function exportWorkbook(workbook, fileName) {
            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
            const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // 显示CK数据对比
        function showCKComparison() {
            if (!longCKTestData || longCKTestData.length === 0) {
                updateStatus('请先生成测试数据', 'error');
                return;
            }

            const firstCK = longCKTestData[0].ck;
            const preview = firstCK.length > 50 ? firstCK.substring(0, 50) + '...' : firstCK;

            document.getElementById('ckPreview').textContent = preview;
            document.getElementById('ckFull').textContent = firstCK;
            document.getElementById('ckComparison').style.display = 'grid';

            updateStatus('CK数据对比已显示，可以看到页面预览和Excel完整数据的区别', 'success');
        }

        // 更新状态显示
        function updateStatus(message, type = 'success') {
            const container = document.getElementById('statusContainer');
            const content = document.getElementById('statusContent');
            
            container.className = `status ${type}`;
            content.innerHTML = message;
            container.style.display = 'block';
        }

        // 页面加载时自动生成测试数据
        document.addEventListener('DOMContentLoaded', function() {
            generateLongCKTestData();
            console.log('🧪 完整CK数据导出测试页面已加载');
        });
    </script>
</body>
</html>
