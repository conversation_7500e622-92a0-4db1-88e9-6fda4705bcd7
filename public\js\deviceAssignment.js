/**
 * 设备分配功能处理
 */

function handleDeviceAssignment() {
    layui.use(['form', 'table'], function() {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table,
            layer = layui.layer;

        // 监听分配提交事件
        form.on('submit(assignDeviceSubmit)', function(data) {
            var checkStatus = table.checkStatus('currentTableId');
            var selectedDevices = checkStatus.data;
            var userNames = data.field.userNames;
            
            // 验证输入
            if (!userNames || userNames.length === 0) {
                layer.msg('请选择至少一个用户');
                return false;
            }
            
            if (selectedDevices.length === 0) {
                layer.msg('请选择至少一个设备');
                return false;
            }

            // 确保userNames是数组格式
            userNames = Array.isArray(userNames) ? userNames : [userNames];
            
            console.log('设备分配请求参数:', {
                deviceName: selectedDevices[0].deviceName,
                userNames: userNames
            });

            // 获取所有选中设备的名称
            const deviceNames = selectedDevices.map(device => device.deviceName);
            
            // 发送分配请求
            layui.$.ajax({
                url: '/indexDevice/assignDevice',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    deviceNames: deviceNames,
                    userNames: userNames
                }),
                success: function(res) {
                    if (res.code === 0) {
                        const modifiedCount = res.data.operationStats?.mainCollection || 0;
                        
                        console.log('分配响应数据:', res);
                        if (modifiedCount > 0) {
                            layer.msg(`成功分配 ${modifiedCount} 个设备给 ${res.data.userNames.join(',')}`, {time: 2000}, function(){
                                // 双重确保表格刷新
                                table.reload('currentTableId', {
                                    url: '/indexDevice',
                                    where: {
                                        page: 1,
                                        limit: 10,
                                        _: new Date().getTime() // 防止缓存
                                    },
                                    done: function(res, curr, count){
                                        console.log('表格刷新完成', res);
                                        layer.closeAll('page');
                                    }
                                });
                            });
                        } else {
                            layer.msg('设备分配已完成，但未修改任何设备状态');
                        }
                    } else {
                        layer.msg('分配失败: ' + res.msg);
                    }
                },
                error: function(xhr) {
                    layer.msg('请求错误: ' + xhr.statusText);
                }
            });
            
            return false;
        });
    });
}

// 使用layui的$初始化
layui.use(['jquery'], function() {
    var $ = layui.jquery;
    $(document).ready(function() {
        handleDeviceAssignment();
    });
});
