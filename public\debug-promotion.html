<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广管理调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>推广用户管理调试工具</h1>
        
        <div style="margin: 20px 0;">
            <h3>基础功能测试</h3>
            <button class="btn" onclick="testBasicJS()">测试JavaScript基础功能</button>
            <button class="btn" onclick="testFetch()">测试Fetch API</button>
            <button class="btn" onclick="testPromotionAPI()">测试推广API</button>
            <button class="btn" onclick="loadPromotionUsersDebug()">加载推广用户数据</button>
            <div id="basicResult" class="result"></div>
        </div>

        <div style="margin: 20px 0;">
            <h3>推广用户列表</h3>
            <div id="userTableContainer">
                <p>点击"加载推广用户数据"按钮加载数据</p>
            </div>
        </div>

        <div style="margin: 20px 0;">
            <h3>添加推广用户测试</h3>
            <input type="text" id="testUserId" placeholder="用户ID" value="test001">
            <input type="text" id="testUsername" placeholder="用户名" value="test_user">
            <input type="password" id="testPassword" placeholder="密码" value="123456">
            <button class="btn" onclick="addPromotionUserDebug()">添加推广用户</button>
            <div id="addResult" class="result"></div>
        </div>

        <div style="margin: 20px 0;">
            <h3>控制台日志</h3>
            <div id="consoleLog" class="result" style="height: 200px;"></div>
        </div>
    </div>

    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        
        function logToPage(message, type = 'log') {
            const logDiv = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            // 也输出到真实控制台
            if (type === 'error') {
                originalError(message);
            } else {
                originalLog(message);
            }
        }
        
        console.log = function(message) {
            logToPage(message, 'log');
        };
        
        console.error = function(message) {
            logToPage(message, 'error');
        };

        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `result ${isError ? 'error' : 'success'}`;
            }
        }

        function testBasicJS() {
            console.log('开始测试JavaScript基础功能...');
            
            try {
                // 测试基本JavaScript功能
                const testObj = { test: 'value' };
                const testArray = [1, 2, 3];
                const testString = JSON.stringify(testObj);
                
                console.log('✅ 对象创建: ' + JSON.stringify(testObj));
                console.log('✅ 数组创建: ' + JSON.stringify(testArray));
                console.log('✅ JSON序列化: ' + testString);
                
                // 测试DOM操作
                const testDiv = document.createElement('div');
                testDiv.textContent = 'DOM测试';
                console.log('✅ DOM操作: 成功');
                
                showResult('basicResult', '✅ JavaScript基础功能测试通过');
                
            } catch (error) {
                console.error('❌ JavaScript基础功能测试失败: ' + error.message);
                showResult('basicResult', '❌ JavaScript基础功能测试失败: ' + error.message, true);
            }
        }

        function testFetch() {
            console.log('开始测试Fetch API...');
            
            fetch('/api/promotion/admin/promotion-users')
                .then(response => {
                    console.log('✅ Fetch响应状态: ' + response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('✅ Fetch响应数据: ' + JSON.stringify(data, null, 2));
                    showResult('basicResult', '✅ Fetch API测试通过\n响应: ' + JSON.stringify(data, null, 2));
                })
                .catch(error => {
                    console.error('❌ Fetch API测试失败: ' + error.message);
                    showResult('basicResult', '❌ Fetch API测试失败: ' + error.message, true);
                });
        }

        function testPromotionAPI() {
            console.log('开始测试推广API...');
            
            const apiTests = [
                { name: '获取推广用户列表', url: '/api/promotion/admin/promotion-users', method: 'GET' },
                { name: '推广用户登录测试', url: '/api/promotion/promoter/login', method: 'POST', body: { user_id: '1001', password: '123456' } }
            ];
            
            let testResults = [];
            
            Promise.all(apiTests.map(test => {
                const options = {
                    method: test.method,
                    headers: { 'Content-Type': 'application/json' }
                };
                
                if (test.body) {
                    options.body = JSON.stringify(test.body);
                }
                
                return fetch(test.url, options)
                    .then(response => response.json())
                    .then(data => {
                        const result = `✅ ${test.name}: ${JSON.stringify(data)}`;
                        console.log(result);
                        testResults.push(result);
                        return result;
                    })
                    .catch(error => {
                        const result = `❌ ${test.name}: ${error.message}`;
                        console.error(result);
                        testResults.push(result);
                        return result;
                    });
            }))
            .then(() => {
                showResult('basicResult', testResults.join('\n\n'));
            });
        }

        function loadPromotionUsersDebug() {
            console.log('开始加载推广用户数据...');
            
            fetch('/api/promotion/admin/promotion-users')
                .then(response => {
                    console.log('API响应状态: ' + response.status);
                    return response.json();
                })
                .then(result => {
                    console.log('API响应数据: ' + JSON.stringify(result, null, 2));
                    
                    if (result.code === 0 && result.data && result.data.list) {
                        renderUserTable(result.data.list);
                        console.log('✅ 推广用户数据加载成功，共 ' + result.data.list.length + ' 条记录');
                    } else {
                        console.error('❌ API返回错误: ' + (result.msg || '未知错误'));
                        document.getElementById('userTableContainer').innerHTML = 
                            '<p style="color: red;">API返回错误: ' + (result.msg || '未知错误') + '</p>';
                    }
                })
                .catch(error => {
                    console.error('❌ 加载推广用户数据失败: ' + error.message);
                    document.getElementById('userTableContainer').innerHTML = 
                        '<p style="color: red;">网络错误: ' + error.message + '</p>';
                });
        }

        function renderUserTable(users) {
            console.log('开始渲染用户表格，用户数量: ' + users.length);
            
            if (users.length === 0) {
                document.getElementById('userTableContainer').innerHTML = '<p>没有找到推广用户数据</p>';
                return;
            }
            
            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>用户ID</th>
                            <th>用户名</th>
                            <th>推广链接</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            users.forEach(user => {
                tableHTML += `
                    <tr>
                        <td>${user.user_id || '-'}</td>
                        <td>${user.username || '-'}</td>
                        <td style="max-width: 300px; word-break: break-all;">${user.promotion_link || '-'}</td>
                        <td>${user.status == 1 ? '启用' : '禁用'}</td>
                        <td>${user.created_at ? new Date(user.created_at).toLocaleString() : '-'}</td>
                        <td>
                            <button class="btn" onclick="toggleUserStatus('${user.user_id}', ${user.status})" style="padding: 5px 10px; font-size: 12px;">
                                ${user.status == 1 ? '禁用' : '启用'}
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableHTML += '</tbody></table>';
            document.getElementById('userTableContainer').innerHTML = tableHTML;
            console.log('✅ 用户表格渲染完成');
        }

        function addPromotionUserDebug() {
            const userId = document.getElementById('testUserId').value;
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            
            if (!userId || !username || !password) {
                showResult('addResult', '❌ 请填写完整信息', true);
                return;
            }
            
            console.log('开始添加推广用户: ' + userId);
            
            fetch('/api/promotion/admin/add-promotion-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId,
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(result => {
                console.log('添加推广用户响应: ' + JSON.stringify(result, null, 2));
                
                if (result.code === 0) {
                    showResult('addResult', '✅ 推广用户添加成功\n' + JSON.stringify(result.data, null, 2));
                    // 重新加载用户列表
                    loadPromotionUsersDebug();
                } else {
                    showResult('addResult', '❌ 添加失败: ' + (result.msg || '未知错误'), true);
                }
            })
            .catch(error => {
                console.error('添加推广用户失败: ' + error.message);
                showResult('addResult', '❌ 网络错误: ' + error.message, true);
            });
        }

        function toggleUserStatus(userId, currentStatus) {
            const newStatus = currentStatus == 1 ? 0 : 1;
            const action = newStatus == 1 ? '启用' : '禁用';
            
            console.log(`开始${action}用户 ${userId}`);
            
            fetch('/api/promotion/admin/update-promotion-user-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId,
                    status: newStatus
                })
            })
            .then(response => response.json())
            .then(result => {
                console.log(`${action}用户响应: ` + JSON.stringify(result, null, 2));
                
                if (result.code === 0) {
                    console.log(`✅ 用户${userId}${action}成功`);
                    // 重新加载用户列表
                    loadPromotionUsersDebug();
                } else {
                    console.error(`❌ ${action}失败: ` + (result.msg || '未知错误'));
                }
            })
            .catch(error => {
                console.error(`${action}用户失败: ` + error.message);
            });
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            console.log('🚀 推广管理调试页面已加载');
            console.log('📋 可用功能: 基础测试、API测试、数据加载、用户管理');
            
            // 自动运行基础测试
            setTimeout(() => {
                testBasicJS();
            }, 500);
        });
    </script>
</body>
</html>
