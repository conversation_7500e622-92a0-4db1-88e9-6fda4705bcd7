<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CK复制功能修复测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background: #f5f5f5;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        th, td {
            border: 1px solid #dee2e6;
            padding: 12px 8px;
            text-align: left;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background: #f9f9f9;
        }

        /* CK数据相关样式 */
        .ck-cell {
            max-width: 200px;
            position: relative;
        }

        .ck-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .ck-preview:hover {
            background: #e9ecef;
            border-color: #007bff;
        }

        .ck-preview.empty {
            color: #6c757d;
            font-style: italic;
        }

        .ck-copy-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 10px;
            cursor: pointer;
            margin-left: 8px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .ck-preview:hover .ck-copy-btn {
            opacity: 1;
        }

        .ck-copy-btn:hover {
            background: #0056b3;
        }

        .ck-copy-btn:active {
            background: #28a745;
        }

        /* 复制成功提示 */
        .copy-success {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 9999;
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background: #e9ecef;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CK复制功能修复测试</h1>

        <div class="test-section">
            <h3>📋 测试修复后的CK复制功能</h3>
            <p>以下表格模拟推广操作记录中的CK数据，测试修复后的复制功能：</p>
            
            <table>
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>推广用户</th>
                        <th>操作类型</th>
                        <th>CK数据</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody id="testTableBody">
                    <!-- 测试数据将在这里生成 -->
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🎯 测试操作</h3>
            <button class="btn" onclick="generateTestData()">🔄 重新生成测试数据</button>
            <button class="btn" onclick="testAllCopy()">📋 测试批量复制</button>
            <button class="btn" onclick="testSpecialCharacters()">🔧 测试特殊字符</button>
            <button class="btn" onclick="clearStatus()">🗑️ 清除状态</button>
        </div>

        <div id="statusContainer" class="status" style="display: none;">
            <h4>测试状态</h4>
            <div id="statusContent"></div>
        </div>
    </div>

    <script>
        // 测试CK数据（包含各种特殊字符）
        const testCKData = [
            '', // 空数据
            'sessionid=abc123def456', // 简单cookie
            'sessionid=abc123; uid=user456; token=xyz789', // 多个cookie
            '[{"name":"sessionid","value":"abc123def456","domain":".douyin.com"}]', // JSON格式
            '[{"name":"bd_ticket_guard_client_data","value":"eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxfQ%3D%3D","domain":".douyin.com"}]', // 复杂JSON
            'cookie_with_special_chars="value with spaces & symbols < > \' \" \\ / \n \r \t"', // 特殊字符
        ];

        const testUsers = ['1001', '1002', '1003', '1004', '1005', '1006'];
        const testActions = ['扫码', '登录成功', '登录失败', '请求过期', '扫码', '登录成功'];
        const testDescriptions = [
            '空CK数据测试',
            '简单Cookie格式',
            '多个Cookie格式',
            '标准JSON格式',
            '复杂JSON格式',
            '特殊字符测试'
        ];

        let testResults = [];

        // 生成测试数据
        function generateTestData() {
            const tbody = document.getElementById('testTableBody');
            tbody.innerHTML = '';

            testCKData.forEach((ckData, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>推广用户${testUsers[index]}</td>
                    <td>${testActions[index]}</td>
                    <td class="ck-cell">${renderCKCell(ckData, index)}</td>
                    <td>${testDescriptions[index]}</td>
                `;
                tbody.appendChild(row);
            });

            updateStatus('测试数据已生成', 'success');
            console.log('✅ 测试数据已生成');
        }

        // 渲染CK单元格（修复后的版本）
        function renderCKCell(ckData, index) {
            if (!ckData || ckData === '-' || ckData.trim() === '') {
                return '<div class="ck-preview empty">暂无CK数据</div>';
            }

            // 生成预览文本（显示前30个字符）
            let previewText = ckData.length > 30 ? ckData.substring(0, 30) + '...' : ckData;
            
            // 尝试解析JSON格式的CK数据
            try {
                JSON.parse(ckData);
                previewText = `JSON格式 (${ckData.length}字符)`;
            } catch (e) {
                // 不是JSON格式，使用原始预览
            }

            return `
                <div class="ck-preview" onclick="showCKDetails('${escapeHtml(ckData)}', ${index})" title="点击查看完整CK数据">
                    <span>${previewText}</span>
                    <button class="ck-copy-btn" onclick="event.stopPropagation(); quickCopyCK('${escapeHtml(ckData)}', this, ${index})" title="快速复制">
                        📋
                    </button>
                </div>
            `;
        }

        // HTML转义函数（修复后的版本）
        function escapeHtml(text) {
            if (!text) return '';
            return text
                .replace(/\\/g, '\\\\')  // 先处理反斜杠
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;')
                .replace(/\n/g, '\\n')   // 处理换行符
                .replace(/\r/g, '\\r')   // 处理回车符
                .replace(/\t/g, '\\t');  // 处理制表符
        }

        // HTML反转义函数（修复后的版本）
        function unescapeHtml(text) {
            if (!text) return '';
            return text
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'")
                .replace(/\\n/g, '\n')   // 处理换行符
                .replace(/\\r/g, '\r')   // 处理回车符
                .replace(/\\t/g, '\t')   // 处理制表符
                .replace(/\\\\/g, '\\'); // 最后处理反斜杠
        }

        // 快速复制CK数据（修复后的版本）
        async function quickCopyCK(ckData, button, index) {
            const originalCKData = unescapeHtml(ckData);
            
            console.log(`📋 测试${index + 1}: 尝试复制CK数据，长度:`, originalCKData.length);
            console.log(`📋 CK数据预览:`, originalCKData.substring(0, 100) + (originalCKData.length > 100 ? '...' : ''));
            
            try {
                const success = await copyToClipboard(originalCKData);
                
                if (success) {
                    // 显示复制成功效果
                    const originalText = button.innerHTML;
                    const originalBg = button.style.background;
                    
                    button.innerHTML = '✅';
                    button.style.background = '#28a745';
                    
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.style.background = originalBg || '#007bff';
                    }, 1500);
                    
                    showCopySuccess(`测试${index + 1}: CK数据已复制 (${originalCKData.length}字符)`);
                    testResults[index] = { success: true, length: originalCKData.length };
                } else {
                    showCopySuccess(`测试${index + 1}: 复制失败`, 'error');
                    testResults[index] = { success: false, error: '复制失败' };
                }
            } catch (error) {
                console.error(`❌ 测试${index + 1}: 复制CK数据失败:`, error);
                showCopySuccess(`测试${index + 1}: 复制失败 - ${error.message}`, 'error');
                testResults[index] = { success: false, error: error.message };
            }
        }

        // 复制到剪贴板函数（修复后的版本）
        async function copyToClipboard(text) {
            try {
                // 现代浏览器使用Clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    console.log('✅ 使用Clipboard API复制成功');
                    return true;
                }
                
                // 兼容旧浏览器
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                textArea.style.opacity = '0';
                textArea.style.pointerEvents = 'none';
                document.body.appendChild(textArea);
                
                textArea.focus();
                textArea.select();
                textArea.setSelectionRange(0, text.length);
                
                const result = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                console.log('✅ 使用execCommand复制:', result ? '成功' : '失败');
                return result;
            } catch (error) {
                console.error('❌ 复制失败:', error);
                return false;
            }
        }

        // 显示CK详情
        function showCKDetails(ckData, index) {
            const originalCKData = unescapeHtml(ckData);
            alert(`CK数据详情 (测试${index + 1}):\n\n长度: ${originalCKData.length}字符\n\n内容:\n${originalCKData}`);
        }

        // 显示复制成功提示
        function showCopySuccess(message, type = 'success') {
            // 移除现有的提示
            const existing = document.querySelector('.copy-success');
            if (existing) {
                existing.remove();
            }
            
            const toast = document.createElement('div');
            toast.className = 'copy-success';
            toast.textContent = message;
            
            if (type === 'error') {
                toast.style.background = '#dc3545';
            }
            
            document.body.appendChild(toast);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        // 更新状态显示
        function updateStatus(message, type = 'success') {
            const container = document.getElementById('statusContainer');
            const content = document.getElementById('statusContent');
            
            container.className = `status ${type}`;
            content.innerHTML = message;
            container.style.display = 'block';
        }

        // 测试批量复制
        async function testAllCopy() {
            testResults = [];
            updateStatus('开始批量复制测试...', 'success');
            
            const copyButtons = document.querySelectorAll('.ck-copy-btn');
            
            for (let i = 0; i < copyButtons.length; i++) {
                const button = copyButtons[i];
                if (button.style.display !== 'none') {
                    button.click();
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
                }
            }
            
            // 显示测试结果
            setTimeout(() => {
                const successCount = testResults.filter(r => r && r.success).length;
                const totalCount = testResults.length;
                
                updateStatus(`批量复制测试完成！成功: ${successCount}/${totalCount}`, 
                    successCount === totalCount ? 'success' : 'error');
            }, 2000);
        }

        // 测试特殊字符
        function testSpecialCharacters() {
            const specialCK = 'test="value with \' \" \\ / \n \r \t & < > symbols"';
            
            copyToClipboard(specialCK).then(success => {
                if (success) {
                    showCopySuccess('特殊字符测试成功！');
                    updateStatus('特殊字符复制测试通过', 'success');
                } else {
                    showCopySuccess('特殊字符测试失败！', 'error');
                    updateStatus('特殊字符复制测试失败', 'error');
                }
            });
        }

        // 清除状态
        function clearStatus() {
            const container = document.getElementById('statusContainer');
            container.style.display = 'none';
            testResults = [];
            
            const existing = document.querySelector('.copy-success');
            if (existing) {
                existing.remove();
            }
        }

        // 页面加载时生成测试数据
        document.addEventListener('DOMContentLoaded', function() {
            generateTestData();
            console.log('🧪 CK复制功能修复测试页面已加载');
        });
    </script>
</body>
</html>
