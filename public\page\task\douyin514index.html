<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui优化版</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .image-preview {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                
                <fieldset>
                    <legend>【修改个人主页】功能</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">名称</label>
                        <div class="layui-input-block">
                            <input type="myName" id="myName" name="myName" placeholder="请输入名称" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">个性签名</label>
                        <div class="layui-input-block">
                            <textarea id="PersonalizedSignature" name="PersonalizedSignature" placeholder="请输入个性签名" class="layui-textarea"></textarea>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <label class="layui-form-label">上传头像</label>
                        <div class="layui-input-block">
                            <input type="file" id="imageUpload" accept="image/*" multiple>
                            <div class="image-preview" id="imagePreview"></div>
                            <!-- 新增隐藏字段存储图片数据 -->
                            <textarea id="headImageData" name="headImageData" style="display:none;"></textarea>
                        </div>
                    </div>

                    <button class="layui-btn layui-btn-sm" value="提交个人主页操作数据" lay-submit="" lay-filter="tijiao">执行任务</button>
                    <button class="layui-btn layui-btn-sm" lay-submit="" lay-filter="stopTask">停止任务</button>
                </fieldset>
            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });

        // 图片上传处理
        const imageUpload = document.getElementById('imageUpload');
        const imagePreview = document.getElementById('imagePreview');
        const imageDataField = document.getElementById('headImageData');
        let imageList = []; // 存储图片信息

        // 生成唯一ID
        function generateUniqueId() {
            return 'img_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
        }

        imageUpload.addEventListener('change', function (e) {
            const files = e.target.files;
            if (!files || files.length === 0) return;

            imageList = []; // 清空原有列表
            imagePreview.innerHTML = ''; // 清空预览

            const formData = new FormData();
            Array.from(files).forEach(file => {
                formData.append('files', file);
            });

            // 显示上传进度
            layui.layer.msg('图片上传中...', {icon: 16, time: 0});
            
            // 调用上传API
            axios.post('/localUpload', formData, {
                onUploadProgress: progress => {
                    const percent = Math.round((progress.loaded * 100) / progress.total);
                    layui.layer.msg(`上传中 ${percent}%`, {icon: 16, time: 0});
                }
            })
                .then(response => {
                    layui.layer.closeAll();
                    console.log('上传成功，服务器返回:', response.data);
                    if (!response.data || !response.data.paths) {
                        throw new Error('服务器返回数据格式不正确');
                    }
                    
                    const paths = response.data.paths; // 服务器返回的路径数组
                    console.log('获取到图片路径:', paths);
                    
                    paths.forEach((path, index) => {
                        const imageId = generateUniqueId();
                        // 动态生成图片URL，使用当前页面域名
                        const serverBaseUrl = window.location.protocol + '//' + window.location.host;
                        const fullUrl = `${serverBaseUrl}${path}`;
                        console.log(`处理图片 ${index}:`, fullUrl);
                        
                        // 存储图片信息（包含ID和完整URL）
                        imageList.push({
                            id: imageId,
                            url: fullUrl,
                            path: path,
                            serverBaseUrl: serverBaseUrl // 存储基础URL备用
                        });
                        
                        // 创建预览元素
                        const div = document.createElement('div');
                        div.className = 'preview-item';
                        div.innerHTML = `
                            <img src="${fullUrl}" alt="预览图">
                            <span class="delete-btn" data-id="${imageId}">×</span>
                        `;
                        imagePreview.appendChild(div);
                    });
                    
                    // 更新隐藏字段
                    updateImageDataField();
                    layui.layer.msg('图片上传成功', {icon: 1});
                })
                .catch(error => {
                    layui.layer.closeAll();
                    console.error('上传失败:', error);
                    layui.layer.msg('图片上传失败: ' + error.message, {icon: 2});
                    // 回退到base64方式
                    console.log('尝试使用base64方式上传...');
                    handleBase64Upload(files);
                });

        // 回退到base64上传方式
        function handleBase64Upload(files) {
            imageList = [];
            imagePreview.innerHTML = '';
            
            Array.from(files).forEach(file => {
                const reader = new FileReader();
                const imageId = generateUniqueId();
                
                reader.onload = function (event) {
                    const base64 = event.target.result;
                    console.log('生成base64图片数据');
                    
                    // 存储图片信息（包含ID和base64）
                    imageList.push({
                        id: imageId,
                        base64: base64
                    });
                    
                    // 创建预览元素
                    const div = document.createElement('div');
                    div.className = 'preview-item';
                    div.innerHTML = `
                        <img src="${base64}" alt="预览图">
                        <span class="delete-btn" data-id="${imageId}">×</span>
                    `;
                    imagePreview.appendChild(div);
                    
                    // 更新隐藏字段
                    updateImageDataField();
                };
                reader.readAsDataURL(file);
            });
        }
        });

        // 点击删除预览图
        imagePreview.addEventListener('click', function (e) {
            if (e.target.classList.contains('delete-btn')) {
                const imageId = e.target.dataset.id;
                // 从数组中移除对应图片
                imageList = imageList.filter(img => img.id !== imageId);
                e.target.parentElement.remove();
                
                // 更新隐藏字段
                updateImageDataField();
            }
        });

        // 更新隐藏字段内容
        function updateImageDataField() {
            console.log('更新隐藏字段...');
            if (imageList.length > 0 && imageList[0].url) {
                // 使用服务器URL方式 - 取第一个图片的完整URL作为字符串
                const imageUrl = imageList[0].url;
                console.log('存储图片URL:', imageUrl);
                imageDataField.value = imageUrl; // 直接存储字符串
            } else if (imageList.length > 0 && imageList[0].base64) {
                // 回退到base64方式 - 取第一个图片的base64数据
                const base64Data = imageList[0].base64.split(',')[1] || '';
                console.log('存储base64数据');
                imageDataField.value = base64Data; // 直接存储字符串
            } else {
                imageDataField.value = ''; // 清空数据
            }
        }

        // 任务执行按钮
        document.querySelector(".layui-btn[lay-filter='tijiao']").addEventListener("click", function (event) {
            event.preventDefault();

            const keyword = document.getElementById("keyword").value.trim();
            const commentContent = document.getElementById("commentContent").value.trim();
            const linkUrl = document.getElementById("linkUrl").value.trim();
            const imageData = imageDataField.value;

            if (!keyword || !commentContent) {
                return layui.layer.msg('请填写关键词和评论内容');
            }

            // 构建请求数据（从表单字段获取）
            const data = {
                myName: document.getElementById("myName").value.trim(),
                PersonalizedSignature: document.getElementById("PersonalizedSignature").value.trim(),
                headImageData: imageDataField.value, // 直接使用字符串值
                likeSwitch: "off",
                commentSwitch: "on"
            };

            // 这里可以替换成你的实际接口
            // axios.post('http://your-api-endpoint', data)
            //     .then(response => {
            //         layui.layer.msg('任务提交成功');
            //         // 重置表单
            //         document.getElementById('keyword').value = '';
            //         document.getElementById('commentContent').value = '';
            //         document.getElementById('linkUrl').value = '';
            //         imageDataField.value = '';
            //         imageUpload.value = '';
            //         imagePreview.innerHTML = '';
            //         imageList = [];
            //     })
            //     .catch(error => {
            //         layui.layer.msg('任务提交失败，请重试');
            //         console.error('请求失败:', error);
            //     });
        });

        // 停止任务按钮
        document.querySelector(".layui-btn[lay-filter='stopTask']").addEventListener("click", function () {
            layui.layer.confirm('确定要停止当前任务吗？', { icon: 3, title: '提示' }, function (index) {
                // 这里添加停止任务的逻辑
                layui.layer.msg('任务已停止', { icon: 5 });
                layer.close(index);
            });
        });
    </script>
</body>

</html>