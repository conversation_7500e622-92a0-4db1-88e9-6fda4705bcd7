<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户仪表板测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .test-section h3 {
            color: #764ba2;
            margin-top: 0;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .result.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
        }

        .login-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 推广用户仪表板测试</h1>

        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>此测试用于验证推广用户登录界面修改后的功能：</p>
            <ul>
                <li>✅ 登录后从promotion_daily_stats表读取统计数据</li>
                <li>✅ 从promotion_visits表读取访问数据</li>
                <li>✅ 从promotion_actions表读取操作记录</li>
                <li>✅ 显示用户信息和今日数据</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔐 推广用户登录测试</h3>
            <div class="login-form">
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="text" id="userId" value="1001" placeholder="推广用户ID">
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input type="password" id="password" value="123456" placeholder="密码">
                </div>
            </div>
            <button class="btn btn-success" onclick="testPromoterLogin()">🔑 测试登录</button>
            <button class="btn btn-danger" onclick="testPromoterLogout()">🚪 测试退出</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>👤 用户信息测试</h3>
            <button class="btn" onclick="testGetUserInfo()">📋 获取用户信息</button>
            <div id="userInfoResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 统计数据测试</h3>
            <button class="btn" onclick="testGetStats()">📈 获取今日统计</button>
            <button class="btn" onclick="testGetActions()">📝 获取操作记录</button>
            
            <div id="statsResult" class="result" style="display: none;"></div>
            
            <div id="statsDisplay" style="display: none;">
                <h4>📊 统计数据展示</h4>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="visitCount">0</div>
                        <div class="stat-label">访问次数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="uniqueIpCount">0</div>
                        <div class="stat-label">独立IP数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="scanCount">0</div>
                        <div class="stat-label">扫码数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successCount">0</div>
                        <div class="stat-label">成功数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="failCount">0</div>
                        <div class="stat-label">失败数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="expireCount">0</div>
                        <div class="stat-label">过期数量</div>
                    </div>
                </div>
            </div>

            <div id="actionsResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔗 快速链接</h3>
            <button class="btn" onclick="openPromoterLogin()">🔑 打开推广用户登录页</button>
            <button class="btn" onclick="openPromoterDashboard()">📊 打开推广用户仪表板</button>
        </div>
    </div>

    <script>
        // 显示结果
        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = message;
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        // 测试推广用户登录
        async function testPromoterLogin() {
            try {
                const userId = document.getElementById('userId').value;
                const password = document.getElementById('password').value;

                showResult('loginResult', '正在测试推广用户登录...');
                
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        password: password
                    })
                });
                
                const result = await response.json();
                showResult('loginResult', 
                    `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, 
                    result.code !== 0
                );
            } catch (error) {
                showResult('loginResult', `错误: ${error.message}`, true);
            }
        }

        // 测试推广用户退出
        async function testPromoterLogout() {
            try {
                showResult('loginResult', '正在测试推广用户退出...');
                
                const response = await fetch('/api/promotion/promoter/logout', {
                    method: 'POST'
                });
                
                const result = await response.json();
                showResult('loginResult', 
                    `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, 
                    result.code !== 0
                );
            } catch (error) {
                showResult('loginResult', `错误: ${error.message}`, true);
            }
        }

        // 测试获取用户信息
        async function testGetUserInfo() {
            try {
                showResult('userInfoResult', '正在获取用户信息...');
                
                const response = await fetch('/api/promotion/promoter/user-info');
                const result = await response.json();
                
                showResult('userInfoResult', 
                    `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, 
                    result.code !== 0
                );
            } catch (error) {
                showResult('userInfoResult', `错误: ${error.message}`, true);
            }
        }

        // 测试获取统计数据
        async function testGetStats() {
            try {
                showResult('statsResult', '正在获取统计数据...');
                
                const response = await fetch('/api/promotion/promoter/today-stats');
                const result = await response.json();
                
                showResult('statsResult', 
                    `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, 
                    result.code !== 0
                );

                // 如果获取成功，显示统计卡片
                if (result.code === 0) {
                    const data = result.data;
                    document.getElementById('visitCount').textContent = data.visit_count;
                    document.getElementById('uniqueIpCount').textContent = data.unique_ip_count;
                    document.getElementById('scanCount').textContent = data.scan_count;
                    document.getElementById('successCount').textContent = data.success_count;
                    document.getElementById('failCount').textContent = data.fail_count;
                    document.getElementById('expireCount').textContent = data.expire_count;
                    document.getElementById('statsDisplay').style.display = 'block';
                }
            } catch (error) {
                showResult('statsResult', `错误: ${error.message}`, true);
            }
        }

        // 测试获取操作记录
        async function testGetActions() {
            try {
                showResult('actionsResult', '正在获取操作记录...');
                
                const response = await fetch('/api/promotion/promoter/today-actions?page=1&limit=10');
                const result = await response.json();
                
                showResult('actionsResult', 
                    `响应状态: ${response.status}\n响应数据: ${JSON.stringify(result, null, 2)}`, 
                    result.code !== 0
                );
            } catch (error) {
                showResult('actionsResult', `错误: ${error.message}`, true);
            }
        }

        // 打开推广用户登录页
        function openPromoterLogin() {
            window.open('/page/promotion/promoter-login.html', '_blank');
        }

        // 打开推广用户仪表板
        function openPromoterDashboard() {
            window.open('/page/promotion/promoter-dashboard.html', '_blank');
        }

        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 推广用户仪表板测试页面已加载');
            console.log('💡 建议测试流程：');
            console.log('1. 先测试登录');
            console.log('2. 获取用户信息');
            console.log('3. 获取统计数据');
            console.log('4. 获取操作记录');
            console.log('5. 打开实际的仪表板页面验证');
        });
    </script>
</body>
</html>
