const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 添加更多测试数据
async function addMoreTestData() {
  console.log('🔧 添加更多测试数据...');
  
  const testUserId = '1001';
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 添加更多访问记录（今日）
    console.log('\n1️⃣ 添加今日访问记录...');
    await connection.execute(
      `INSERT INTO promotion_visits (promotion_user_id, visitor_ip, ip_province, user_agent, referer, visit_time)
       VALUES 
       (?, '*************', '北京市', 'Mozilla/5.0', '', NOW() - INTERVAL 1 HOUR),
       (?, '*************', '上海市', 'Mozilla/5.0', '', NOW() - INTERVAL 2 HOUR),
       (?, '*************', '广东省', 'Mozilla/5.0', '', NOW() - INTERVAL 3 HOUR),
       (?, '*************', '浙江省', 'Mozilla/5.0', '', NOW() - INTERVAL 4 HOUR),
       (?, '*************', '江苏省', 'Mozilla/5.0', '', NOW() - INTERVAL 5 HOUR),
       (?, '*************', '北京市', 'Mozilla/5.0', '', NOW() - INTERVAL 30 MINUTE),
       (?, '*************', '上海市', 'Mozilla/5.0', '', NOW() - INTERVAL 45 MINUTE),
       (?, '*************', '四川省', 'Mozilla/5.0', '', NOW() - INTERVAL 15 MINUTE)`,
      [testUserId, testUserId, testUserId, testUserId, testUserId, testUserId, testUserId, testUserId]
    );
    
    // 2. 添加更多操作记录（今日）
    console.log('\n2️⃣ 添加今日操作记录...');
    await connection.execute(
      `INSERT INTO promotion_actions (promotion_user_id, action_type, ip_address, ip_province, action_time)
       VALUES 
       (?, 'scan', '*************', '北京市', NOW() - INTERVAL 1 HOUR),
       (?, 'login_success', '*************', '北京市', NOW() - INTERVAL 55 MINUTE),
       (?, 'scan', '*************', '上海市', NOW() - INTERVAL 2 HOUR),
       (?, 'login_success', '*************', '上海市', NOW() - INTERVAL 115 MINUTE),
       (?, 'scan', '*************', '广东省', NOW() - INTERVAL 3 HOUR),
       (?, 'login_fail', '*************', '广东省', NOW() - INTERVAL 175 MINUTE),
       (?, 'scan', '*************', '浙江省', NOW() - INTERVAL 4 HOUR),
       (?, 'login_success', '*************', '浙江省', NOW() - INTERVAL 235 MINUTE),
       (?, 'scan', '*************', '江苏省', NOW() - INTERVAL 5 HOUR),
       (?, 'login_success', '*************', '江苏省', NOW() - INTERVAL 295 MINUTE),
       (?, 'scan', '*************', '四川省', NOW() - INTERVAL 15 MINUTE),
       (?, 'login_success', '*************', '四川省', NOW() - INTERVAL 10 MINUTE)`,
      [testUserId, testUserId, testUserId, testUserId, testUserId, testUserId, testUserId, testUserId, testUserId, testUserId, testUserId, testUserId]
    );
    
    console.log('✅ 测试数据添加完成');
    
    // 3. 重新计算今日统计
    console.log('\n3️⃣ 重新计算今日统计...');
    
    const [todayVisits] = await connection.execute(
      `SELECT COUNT(*) as visit_count, COUNT(DISTINCT visitor_ip) as unique_ip_count
       FROM promotion_visits 
       WHERE promotion_user_id = ? AND DATE(visit_time) = CURDATE()`,
      [testUserId]
    );
    
    const [todayActions] = await connection.execute(
      `SELECT 
        COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
        COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
        COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count,
        COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as expire_count
       FROM promotion_actions 
       WHERE promotion_user_id = ? AND DATE(action_time) = CURDATE()`,
      [testUserId]
    );
    
    const visitStats = todayVisits[0];
    const actionStats = todayActions[0];
    
    console.log('今日实时统计:');
    console.log(`   访问次数: ${visitStats.visit_count}`);
    console.log(`   独立IP数: ${visitStats.unique_ip_count}`);
    console.log(`   扫码次数: ${actionStats.scan_count}`);
    console.log(`   成功次数: ${actionStats.success_count}`);
    console.log(`   失败次数: ${actionStats.fail_count}`);
    console.log(`   过期次数: ${actionStats.expire_count}`);
    
    // 4. 更新统计表
    console.log('\n4️⃣ 更新统计表...');
    await connection.execute(
      `INSERT INTO promotion_daily_stats 
       (promotion_user_id, stat_date, visit_count, unique_ip_count, scan_count, success_count, fail_count, expire_count)
       VALUES (?, CURDATE(), ?, ?, ?, ?, ?, ?)
       ON DUPLICATE KEY UPDATE
       visit_count = VALUES(visit_count),
       unique_ip_count = VALUES(unique_ip_count),
       scan_count = VALUES(scan_count),
       success_count = VALUES(success_count),
       fail_count = VALUES(fail_count),
       expire_count = VALUES(expire_count),
       updated_at = NOW()`,
      [
        testUserId,
        visitStats.visit_count,
        visitStats.unique_ip_count,
        actionStats.scan_count,
        actionStats.success_count,
        actionStats.fail_count,
        actionStats.expire_count
      ]
    );
    
    console.log('✅ 统计表更新完成');
    
    // 5. 验证最终数据
    console.log('\n5️⃣ 验证最终数据...');
    const [finalStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    if (finalStats.length > 0) {
      const stats = finalStats[0];
      console.log('✅ 最终统计数据:');
      console.log(`   访问次数: ${stats.visit_count}`);
      console.log(`   独立IP数: ${stats.unique_ip_count}`);
      console.log(`   扫码数量: ${stats.scan_count}`);
      console.log(`   成功数量: ${stats.success_count}`);
      console.log(`   失败数量: ${stats.fail_count}`);
      console.log(`   过期数量: ${stats.expire_count}`);
    }
    
  } catch (error) {
    console.error('❌ 添加数据失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
  
  console.log('\n🎉 测试数据添加完成！');
  console.log('\n🌐 现在推广用户登录界面应该显示丰富的数据了！');
}

// 运行添加数据
addMoreTestData().catch(console.error);
