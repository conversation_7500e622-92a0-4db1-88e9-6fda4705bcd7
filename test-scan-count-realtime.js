const mysql = require('mysql2/promise');
const axios = require('axios');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

const baseURL = 'http://localhost:15001';
const testUserId = '1001';

async function testScanCountRealtime() {
  let connection;
  
  try {
    console.log('🧪 开始测试scan_count实时计算功能');
    console.log('=' .repeat(60));
    
    // 1. 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 2. 清理今日数据，重新开始测试
    console.log('\n1️⃣ 清理今日测试数据...');
    await connection.execute(
      `DELETE FROM promotion_daily_stats WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    console.log('✅ 今日数据已清理');
    
    // 3. 插入一些测试数据到promotion_actions表
    console.log('\n2️⃣ 插入测试操作数据...');
    
    const testActions = [
      { action_type: 'login_success', count: 3 },
      { action_type: 'login_fail', count: 2 },
      { action_type: 'request_expire', count: 1 }
    ];
    
    for (const action of testActions) {
      for (let i = 0; i < action.count; i++) {
        await connection.execute(
          `INSERT INTO promotion_actions 
           (promotion_user_id, action_type, ip_address, ip_province, action_time)
           VALUES (?, ?, ?, ?, NOW())`,
          [testUserId, action.action_type, `192.168.1.${100 + i}`, '北京']
        );
      }
      console.log(`   ✅ 插入 ${action.count} 条 ${action.action_type} 记录`);
    }
    
    const expectedScanCount = 3 + 2 + 1; // success + fail + expire = 6
    console.log(`\n📊 预期scan_count: ${expectedScanCount} (3 + 2 + 1)`);
    
    // 4. 模拟推广用户登录
    console.log('\n3️⃣ 模拟推广用户登录...');
    const loginResponse = await axios.post(`${baseURL}/api/promotion/promoter/login`, {
      user_id: testUserId,
      password: '123456'
    });
    
    if (loginResponse.data.code !== 0) {
      throw new Error(`登录失败: ${loginResponse.data.msg}`);
    }
    
    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';
    console.log('✅ 推广用户登录成功');
    
    // 5. 调用统计数据API，测试实时计算
    console.log('\n4️⃣ 调用统计数据API，测试实时计算...');
    const statsResponse = await axios.get(`${baseURL}/api/promotion/promoter/today-stats`, {
      headers: {
        Cookie: cookieHeader
      }
    });
    
    if (statsResponse.data.code !== 0) {
      throw new Error(`获取统计数据失败: ${statsResponse.data.msg}`);
    }
    
    const stats = statsResponse.data.data;
    console.log('\n📈 API返回的统计数据:');
    console.log(`   访问次数: ${stats.visit_count}`);
    console.log(`   独立IP数: ${stats.unique_ip_count}`);
    console.log(`   扫码数量: ${stats.scan_count}`);
    console.log(`   成功数量: ${stats.success_count}`);
    console.log(`   失败数量: ${stats.fail_count}`);
    console.log(`   过期数量: ${stats.expire_count}`);
    
    // 6. 验证scan_count计算是否正确
    console.log('\n5️⃣ 验证scan_count计算...');
    const actualScanCount = stats.scan_count;
    const calculatedScanCount = stats.success_count + stats.fail_count + stats.expire_count;
    
    console.log(`   API返回的scan_count: ${actualScanCount}`);
    console.log(`   手动计算scan_count: ${calculatedScanCount} (${stats.success_count} + ${stats.fail_count} + ${stats.expire_count})`);
    console.log(`   预期scan_count: ${expectedScanCount}`);
    
    // 验证结果
    if (actualScanCount === calculatedScanCount && actualScanCount === expectedScanCount) {
      console.log('\n🎉 测试通过！scan_count实时计算正确');
      console.log(`   ✅ scan_count = ${actualScanCount} (正确)`);
      console.log(`   ✅ 数值相加，不是字符串拼接`);
      console.log(`   ✅ 实时计算逻辑正确`);
    } else {
      console.log('\n❌ 测试失败！scan_count计算有问题');
      if (actualScanCount !== calculatedScanCount) {
        console.log(`   ❌ API返回值与手动计算不一致: ${actualScanCount} ≠ ${calculatedScanCount}`);
      }
      if (actualScanCount !== expectedScanCount) {
        console.log(`   ❌ 实际值与预期值不一致: ${actualScanCount} ≠ ${expectedScanCount}`);
      }
    }
    
    // 7. 检查数据库中的数据
    console.log('\n6️⃣ 检查数据库中的统计数据...');
    const [dbStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats 
       WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    if (dbStats.length > 0) {
      const dbStat = dbStats[0];
      console.log('\n💾 数据库中的统计数据:');
      console.log(`   visit_count: ${dbStat.visit_count}`);
      console.log(`   unique_ip_count: ${dbStat.unique_ip_count}`);
      console.log(`   scan_count: ${dbStat.scan_count}`);
      console.log(`   success_count: ${dbStat.success_count}`);
      console.log(`   fail_count: ${dbStat.fail_count}`);
      console.log(`   expire_count: ${dbStat.expire_count}`);
      
      const dbCalculatedScanCount = dbStat.success_count + dbStat.fail_count + dbStat.expire_count;
      if (dbStat.scan_count === dbCalculatedScanCount) {
        console.log(`   ✅ 数据库中scan_count正确: ${dbStat.scan_count}`);
      } else {
        console.log(`   ❌ 数据库中scan_count错误: ${dbStat.scan_count} ≠ ${dbCalculatedScanCount}`);
      }
    } else {
      console.log('   ⚠️  数据库中没有找到今日统计数据');
    }
    
    // 8. 测试数据类型
    console.log('\n7️⃣ 测试数据类型...');
    console.log(`   scan_count类型: ${typeof stats.scan_count} (值: ${stats.scan_count})`);
    console.log(`   success_count类型: ${typeof stats.success_count} (值: ${stats.success_count})`);
    console.log(`   fail_count类型: ${typeof stats.fail_count} (值: ${stats.fail_count})`);
    console.log(`   expire_count类型: ${typeof stats.expire_count} (值: ${stats.expire_count})`);
    
    if (typeof stats.scan_count === 'number' && 
        typeof stats.success_count === 'number' && 
        typeof stats.fail_count === 'number' && 
        typeof stats.expire_count === 'number') {
      console.log('   ✅ 所有统计数据都是数字类型');
    } else {
      console.log('   ❌ 存在非数字类型的统计数据');
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('🧪 scan_count实时计算测试完成');
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('   HTTP状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('✅ 数据库连接已关闭');
    }
  }
}

// 运行测试
testScanCountRealtime().catch(console.error);
