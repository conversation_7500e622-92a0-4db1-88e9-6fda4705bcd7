<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .captcha-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .captcha-item {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .captcha-img {
            width: 100px;
            height: 40px;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>验证码调试页面</h1>
        
        <!-- 显示所有验证码图片 -->
        <div class="test-section">
            <h3>1. 所有验证码图片预览</h3>
            <div class="captcha-grid" id="captchaGrid">
                <!-- 动态生成 -->
            </div>
        </div>

        <!-- 当前验证码测试 -->
        <div class="test-section">
            <h3>2. 当前验证码测试</h3>
            <div style="text-align: center;">
                <img id="currentCaptcha" class="captcha-img" src="/images/captcha1.jpg" alt="当前验证码">
                <br>
                <input type="text" id="captchaInput" placeholder="输入验证码" maxlength="4" style="padding: 8px; margin: 10px;">
                <br>
                <button class="btn" onclick="getCaptcha()">获取新验证码</button>
                <button class="btn" onclick="verifyCaptcha()">验证验证码</button>
                <button class="btn" onclick="refreshCaptcha()">刷新验证码</button>
            </div>
            <div id="testResult" class="result info">点击"获取新验证码"开始测试</div>
        </div>

        <!-- 验证码配置信息 -->
        <div class="test-section">
            <h3>3. 验证码配置信息</h3>
            <div id="configInfo" class="result info">加载中...</div>
        </div>

        <!-- 会话信息 -->
        <div class="test-section">
            <h3>4. 会话和统计信息</h3>
            <button class="btn" onclick="getStats()">获取统计信息</button>
            <div id="statsInfo" class="result info">点击"获取统计信息"查看详情</div>
        </div>
    </div>

    <script src="/lib/axios/axios.min.js"></script>
    <script>
        // 验证码配置
        const captchaConfig = [
            { image: "captcha1.jpg", code: "XSZG" },
            { image: "captcha2.jpg", code: "MNBV" },
            { image: "captcha3.jpg", code: "QWER" },
            { image: "captcha4.jpg", code: "ASDF" },
            { image: "captcha5.jpg", code: "ZXCV" },
            { image: "captcha6.jpg", code: "TYUI" },
            { image: "captcha7.jpg", code: "GHJK" },
            { image: "captcha8.jpg", code: "BNML" },
            { image: "captcha9.jpg", code: "POIU" },
            { image: "captcha10.jpg", code: "LKJH" }
        ];

        // 初始化页面
        function initPage() {
            // 生成验证码预览网格
            const grid = document.getElementById('captchaGrid');
            captchaConfig.forEach((item, index) => {
                const div = document.createElement('div');
                div.className = 'captcha-item';
                div.innerHTML = `
                    <h4>${item.image}</h4>
                    <img src="/images/${item.image}" class="captcha-img" alt="${item.code}">
                    <p><strong>验证码: ${item.code}</strong></p>
                    <button class="btn" onclick="testSpecificCaptcha('${item.image}', '${item.code}')">测试此验证码</button>
                `;
                grid.appendChild(div);
            });

            // 显示配置信息
            const configDiv = document.getElementById('configInfo');
            configDiv.innerHTML = `
                <strong>验证码配置:</strong><br>
                - 总数量: ${captchaConfig.length} 个<br>
                - 过期时间: 5分钟<br>
                - 最大尝试次数: 5次<br>
                - 验证方式: 不区分大小写<br><br>
                <strong>配置列表:</strong><br>
                ${captchaConfig.map(item => `${item.image} -> ${item.code}`).join('<br>')}
            `;
        }

        // 获取验证码
        function getCaptcha() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.textContent = '正在获取验证码...';
            resultDiv.className = 'result info';

            axios.get('/api/captcha/get-captcha')
                .then(res => {
                    if (res.data.code === 0) {
                        const imgElement = document.getElementById('currentCaptcha');
                        imgElement.src = res.data.data.image + '?t=' + res.data.data.timestamp;
                        
                        const imageName = res.data.data.image.split('/').pop();
                        const expectedCode = captchaConfig.find(item => item.image === imageName)?.code || '未知';
                        
                        resultDiv.innerHTML = `
                            ✅ 获取验证码成功<br>
                            图片: ${imageName}<br>
                            预期验证码: <strong>${expectedCode}</strong><br>
                            时间戳: ${res.data.data.timestamp}
                        `;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = '❌ 获取验证码失败: ' + res.data.msg;
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '❌ 请求失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }

        // 验证验证码
        function verifyCaptcha() {
            const input = document.getElementById('captchaInput');
            const resultDiv = document.getElementById('testResult');
            const code = input.value.trim();

            if (!code) {
                resultDiv.textContent = '❌ 请输入验证码';
                resultDiv.className = 'result error';
                return;
            }

            resultDiv.textContent = '正在验证验证码...';
            resultDiv.className = 'result info';

            axios.post('/api/captcha/verify-captcha', { captcha: code })
                .then(res => {
                    if (res.data.code === 0) {
                        resultDiv.innerHTML = `✅ ${res.data.msg}`;
                        resultDiv.className = 'result success';
                        input.value = '';
                    } else {
                        resultDiv.innerHTML = `❌ ${res.data.msg}`;
                        resultDiv.className = 'result error';
                        
                        // 如果有新验证码，更新图片
                        if (res.data.data && res.data.data.newCaptcha) {
                            const imgElement = document.getElementById('currentCaptcha');
                            imgElement.src = res.data.data.newCaptcha.image + '?t=' + res.data.data.newCaptcha.timestamp;
                            
                            const imageName = res.data.data.newCaptcha.image.split('/').pop();
                            const expectedCode = captchaConfig.find(item => item.image === imageName)?.code || '未知';
                            
                            resultDiv.innerHTML += `<br>🔄 已自动切换到新验证码<br>新图片: ${imageName}<br>新验证码: <strong>${expectedCode}</strong>`;
                            input.value = '';
                        }
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '❌ 验证失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }

        // 刷新验证码
        function refreshCaptcha() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.textContent = '正在刷新验证码...';
            resultDiv.className = 'result info';

            axios.post('/api/captcha/refresh-captcha')
                .then(res => {
                    if (res.data.code === 0) {
                        const imgElement = document.getElementById('currentCaptcha');
                        imgElement.src = res.data.data.image + '?t=' + res.data.data.timestamp;
                        
                        const imageName = res.data.data.image.split('/').pop();
                        const expectedCode = captchaConfig.find(item => item.image === imageName)?.code || '未知';
                        
                        resultDiv.innerHTML = `
                            ✅ 刷新验证码成功<br>
                            新图片: ${imageName}<br>
                            新验证码: <strong>${expectedCode}</strong>
                        `;
                        resultDiv.className = 'result success';
                        document.getElementById('captchaInput').value = '';
                    } else {
                        resultDiv.textContent = '❌ 刷新失败: ' + res.data.msg;
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '❌ 刷新失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }

        // 测试特定验证码
        function testSpecificCaptcha(imageName, expectedCode) {
            const imgElement = document.getElementById('currentCaptcha');
            imgElement.src = `/images/${imageName}?t=${Date.now()}`;
            
            const input = document.getElementById('captchaInput');
            input.value = expectedCode;
            
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = `
                📋 已设置测试验证码<br>
                图片: ${imageName}<br>
                验证码: <strong>${expectedCode}</strong><br>
                请点击"验证验证码"进行测试
            `;
            resultDiv.className = 'result info';
        }

        // 获取统计信息
        function getStats() {
            const statsDiv = document.getElementById('statsInfo');
            statsDiv.textContent = '正在获取统计信息...';
            statsDiv.className = 'result info';

            axios.get('/api/captcha/captcha-stats')
                .then(res => {
                    if (res.data.code === 0) {
                        const stats = res.data.data;
                        let html = `
                            <strong>统计信息:</strong><br>
                            - 总验证码数量: ${stats.totalCaptchas}<br>
                            - 活跃会话数: ${stats.activeSessions}<br><br>
                        `;
                        
                        if (stats.sessions.length > 0) {
                            html += '<strong>活跃会话:</strong><br>';
                            stats.sessions.forEach((session, index) => {
                                html += `
                                    ${index + 1}. 会话: ${session.sessionId}<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;图片: ${session.image}<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;尝试次数: ${session.attempts}/${session.maxAttempts}<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;创建时间: ${session.createTime}<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;剩余时间: ${Math.round(session.remainingTime/1000)}秒<br><br>
                                `;
                            });
                        } else {
                            html += '<em>当前没有活跃的验证码会话</em>';
                        }
                        
                        statsDiv.innerHTML = html;
                        statsDiv.className = 'result success';
                    } else {
                        statsDiv.textContent = '❌ 获取统计信息失败: ' + res.data.msg;
                        statsDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    statsDiv.textContent = '❌ 获取统计信息失败: ' + error.message;
                    statsDiv.className = 'result error';
                });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 验证码调试页面已加载');
            initPage();
            getCaptcha(); // 自动获取一个验证码
        });
    </script>
</body>
</html>
