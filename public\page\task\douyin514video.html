<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui优化版</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .image-preview {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                
                <fieldset>
                    <legend>【视频发布】功能</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">视频标题</label>
                        <div class="layui-input-block">
                            <input type="text" id="videoTitle" name="videoTitle" placeholder="请输入视频标题" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">视频描述</label>
                        <div class="layui-input-block">
                            <textarea id="videoDescription" name="videoDescription" placeholder="请输入视频描述" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">上传媒体</label>
                        <div class="layui-input-block">
                            <input type="file" id="mediaUpload" accept="image/*,video/mp4,video/quicktime" multiple>
                            <div class="image-preview" id="mediaPreview"></div>
                            <div id="ossLink" class="layui-form-mid layui-word-aux" style="margin-top:10px;"></div>
                            <input type="hidden" id="ossLinkData" name="ossLink">
                        </div>
                    </div>
                        
                    <button class="layui-btn layui-btn-sm" value="发视频" lay-submit="" lay-filter="tijiao">执行任务</button>
                    <button class="layui-btn layui-btn-sm" lay-submit="" lay-filter="stopTask">停止任务</button>
                </fieldset>

            </form>
            
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.1.min.js"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    
    <script>
        // 表格相关代码已简化，移除MySQL依赖
        layui.use(['table', 'layer'], function(){
            var table = layui.table;
            var layer = layui.layer;
            
            // 初始化空表格
            table.render({
                elem: '#linkTable',
                data: [],
                cols: [[
                    {field: 'index', title: '序号', width: 80},
                    {field: 'phoneNumber', title: '设备编号', width: 150},
                    {field: 'username', title: '名称', width: 150},
                    {field: 'links', title: '链接', minWidth: 200}
                ]]
            });
        });
        
        // MySQL相关代码已全部删除
    </script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });

        // 本地文件上传功能
        const mediaUpload = document.getElementById('mediaUpload');
        const mediaPreview = document.getElementById('mediaPreview');
        const serverLinkDisplay = document.getElementById('ossLink');
        const serverLinkData = document.getElementById('ossLinkData');
        let currentMedia = [];

        mediaUpload.addEventListener('change', async function(e) {
            const files = e.target.files;
            if (!files || files.length === 0) return;

            mediaPreview.innerHTML = '';
            serverLinkDisplay.innerHTML = '上传中...';
            currentMedia = [];

            const formData = new FormData();
            // 添加所有文件
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            try {
                const response = await axios.post('/localUpload', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    onUploadProgress: progress => {
                        const percent = Math.round((progress.loaded * 100) / progress.total);
                        serverLinkDisplay.innerHTML = `上传中: ${percent}%`;
                    }
                });

                if (response.data && response.data.paths) {
                    console.log('上传返回数据:', response.data); // 调试日志
                    const fileUrls = response.data.paths.map(path => 
                        path.startsWith('http') ? path : `http://**************:15001${path}`
                    );
                    currentMedia = fileUrls;
                    
                    // 显示第一个文件预览和完整URL
                    const firstFile = files[0];
                    const fullUrl = fileUrls[0];
                    if (firstFile.type.startsWith('video/')) {
                        mediaPreview.innerHTML = `
                            <video controls width="320" height="240">
                                <source src="${fullUrl}" type="${firstFile.type}">
                                您的浏览器不支持视频标签
                            </video>
                        `;
                    } else if (firstFile.type.startsWith('image/')) {
                        mediaPreview.innerHTML = `
                            <img src="${fullUrl}" style="max-width:320px;max-height:240px;">
                        `;
                    }
                    
                    // 存入隐藏字段并显示完整URL
                    serverLinkData.value = fileUrls.join(',');
                    serverLinkDisplay.innerHTML = `
                        <div>完整URL: <a href="${fullUrl}" target="_blank">${fullUrl}</a></div>
                        <div>已保存 ${fileUrls.length} 个文件</div>
                    `;
                }
            } catch (error) {
                console.error('上传失败:', error);
                serverLinkDisplay.innerHTML = '上传失败';
                layer.msg('文件上传失败: ' + error.message, {icon: 2});
            }
        });

        function copyToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            layer.msg('链接已复制', {icon: 1});
        }
          


        // 停止任务按钮
        document.querySelector(".layui-btn[lay-filter='stopTask']").addEventListener("click", function () {
            layui.layer.confirm('确定要停止当前任务吗？', { icon: 3, title: '提示' }, function (index) {
                // 这里添加停止任务的逻辑
                layui.layer.msg('任务已停止', { icon: 5 });
                layer.close(index);
            });
        });
    </script>
</body>

</html>