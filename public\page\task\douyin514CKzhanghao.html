<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>抖音账号管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .layuimini-container {
            padding: 15px;
        }

        .account-card {
            border: 1px solid #e6e6e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .account-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .account-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .account-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }

        .status-active {
            background: #e8f5e8;
            color: #52c41a;
        }

        .status-inactive {
            background: #fff2e8;
            color: #fa8c16;
        }

        .status-banned {
            background: #ffebee;
            color: #f5222d;
        }

        .account-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 10px;
        }

        .info-item {
            display: flex;
            align-items: center;
        }

        .info-label {
            font-weight: bold;
            margin-right: 8px;
            color: #666;
            min-width: 80px;
        }

        .info-value {
            color: #333;
        }

        .account-actions {
            text-align: right;
        }

        .search-bar {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .stats-bar {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
            color: white;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <!-- 统计信息 -->
            <div class="stats-bar">
                <div class="stat-item">
                    <span class="stat-number" id="totalAccounts">0</span>
                    <span class="stat-label">总账号数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="activeAccounts">0</span>
                    <span class="stat-label">正常账号</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="inactiveAccounts">0</span>
                    <span class="stat-label">异常账号</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="disabledAccounts">0</span>
                    <span class="stat-label">禁用账号</span>
                </div>
            </div>

            <!-- 搜索和操作栏 -->
            <div class="search-bar">
                <form class="layui-form" lay-filter="searchForm">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <input type="text" name="searchKeyword" placeholder="搜索用户名" class="layui-input">
                        </div>
                        <div class="layui-col-md3">
                            <select name="statusFilter">
                                <option value="">全部状态</option>
                                <option value="active">正常</option>
                                <option value="inactive">异常</option>
                                <option value="disabled">禁用</option>
                            </select>
                        </div>
                        <div class="layui-col-md5">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search"></i> 搜索
                            </button>
                            <button type="reset" class="layui-btn layui-btn-sm layui-btn-primary">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="addAccountBtn">
                                <i class="layui-icon layui-icon-add-1"></i> 添加账号
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" id="importAccountBtn">
                                <i class="layui-icon layui-icon-upload"></i> 批量导入
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="batchDeleteBtn">
                                <i class="layui-icon layui-icon-delete"></i> 批量删除
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 账号列表 -->
            <div id="accountList">
                <!-- 账号卡片将在这里动态生成 -->
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="layui-icon layui-icon-face-cry"></i>
                <div>暂无账号数据</div>
                <div style="margin-top: 10px;">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" id="addFirstAccountBtn">
                        <i class="layui-icon layui-icon-add-1"></i> 添加第一个账号
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use(['form', 'layer', 'element'], function () {
            var form = layui.form;
            var layer = layui.layer;
            var element = layui.element;

            // 账号数据
            let accountsData = [];
            let filteredData = [];
            let currentStats = {
                total: 0,
                active: 0,
                inactive: 0,
                disabled: 0
            };

            // 初始化页面
            function init() {
                loadAccounts();
                bindEvents();
            }

            // 加载账号数据
            async function loadAccounts(searchParams = {}) {
                try {
                    const params = new URLSearchParams(searchParams);
                    const response = await axios.get(`/api/douyin/accounts?${params}`);

                    if (response.data.code === 0) {
                        accountsData = response.data.data;
                        filteredData = [...accountsData];
                        currentStats = response.data.stats;
                        updateStats();
                        renderAccountList();
                    } else {
                        layer.msg(response.data.msg || '加载账号数据失败', { icon: 2 });
                    }
                } catch (error) {
                    console.error('加载账号数据失败:', error);
                    layer.msg('加载账号数据失败', { icon: 2 });
                }
            }

            // 更新统计信息
            function updateStats() {
                document.getElementById('totalAccounts').textContent = currentStats.total || 0;
                document.getElementById('activeAccounts').textContent = currentStats.active || 0;
                document.getElementById('inactiveAccounts').textContent = currentStats.inactive || 0;
                document.getElementById('disabledAccounts').textContent = currentStats.disabled || 0;
            }

            // 渲染账号列表
            function renderAccountList() {
                const container = document.getElementById('accountList');
                const emptyState = document.getElementById('emptyState');

                if (filteredData.length === 0) {
                    container.innerHTML = '';
                    emptyState.style.display = 'block';
                    return;
                }

                emptyState.style.display = 'none';
                container.innerHTML = filteredData.map(account => createAccountCard(account)).join('');
            }

            // 创建账号卡片HTML
            function createAccountCard(account) {
                const statusClass = `status-${account.status}`;
                const statusText = {
                    'active': '正常',
                    'inactive': '异常',
                    'disabled': '禁用'
                }[account.status];

                // 格式化时间
                const createTime = account.created_at ? new Date(account.created_at).toLocaleString('zh-CN') : '未知';

                return `
                    <div class="account-card" data-id="${account.id}">
                        <div class="account-header">
                            <div class="account-name">${account.username}</div>
                            <div class="account-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="account-info">
                            <div class="info-item">
                                <span class="info-label">用户名:</span>
                                <span class="info-value">${account.username}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">密码:</span>
                                <span class="info-value">******</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">账号状态:</span>
                                <span class="info-value">${statusText}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">保存CK次数:</span>
                                <span class="info-value">${account.ckcount || 0}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">创建时间:</span>
                                <span class="info-value">${createTime}</span>
                            </div>
                        </div>
                        <div class="account-actions">
                            <button class="layui-btn layui-btn-xs layui-btn-normal edit-btn" data-id="${account.id}">
                                <i class="layui-icon layui-icon-edit"></i> 编辑
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-warm view-btn" data-id="${account.id}">
                                <i class="layui-icon layui-icon-about"></i> 详情
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-danger delete-btn" data-id="${account.id}">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                            </button>
                        </div>
                    </div>
                `;
            }

            // 绑定事件
            function bindEvents() {
                // 搜索功能
                form.on('submit(search)', function (data) {
                    const formData = data.field;
                    filterAccounts(formData);
                    return false;
                });

                // 添加账号按钮
                document.getElementById('addAccountBtn').addEventListener('click', function () {
                    showAccountForm();
                });

                document.getElementById('addFirstAccountBtn').addEventListener('click', function () {
                    showAccountForm();
                });

                // 批量导入按钮
                document.getElementById('importAccountBtn').addEventListener('click', function () {
                    showImportDialog();
                });

                // 批量删除按钮
                document.getElementById('batchDeleteBtn').addEventListener('click', function () {
                    layer.msg('批量删除功能开发中...', { icon: 6 });
                });

                // 账号操作按钮事件委托
                document.getElementById('accountList').addEventListener('click', function (e) {
                    const target = e.target.closest('button');
                    if (!target) return;

                    const accountId = parseInt(target.dataset.id);

                    if (target.classList.contains('edit-btn')) {
                        editAccount(accountId);
                    } else if (target.classList.contains('view-btn')) {
                        viewAccount(accountId);
                    } else if (target.classList.contains('delete-btn')) {
                        deleteAccount(accountId);
                    }
                });
            }

            // 过滤账号
            function filterAccounts(filters) {
                const searchParams = {};

                if (filters.searchKeyword) {
                    searchParams.search = filters.searchKeyword;
                }

                if (filters.statusFilter) {
                    searchParams.status = filters.statusFilter;
                }



                loadAccounts(searchParams);
            }

            // 显示账号表单
            function showAccountForm(account = null) {
                const isEdit = !!account;
                const title = isEdit ? '编辑账号' : '添加账号';

                const formHtml = `
                    <form class="layui-form" lay-filter="accountForm" style="padding: 20px;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">用户名</label>
                            <div class="layui-input-block">
                                <input type="text" name="username" value="${account?.username || ''}"
                                       placeholder="请输入用户名" class="layui-input" lay-verify="required">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">密码</label>
                            <div class="layui-input-block">
                                <input type="password" name="password" value="${account?.password || ''}"
                                       placeholder="请输入密码" class="layui-input" lay-verify="required">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">账号状态</label>
                            <div class="layui-input-block">
                                <select name="status" lay-verify="required">
                                    <option value="">请选择状态</option>
                                    <option value="active" ${account?.status === 'active' ? 'selected' : ''}>正常</option>
                                    <option value="inactive" ${account?.status === 'inactive' ? 'selected' : ''}>异常</option>
                                    <option value="disabled" ${account?.status === 'disabled' ? 'selected' : ''}>禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">保存CK次数</label>
                            <div class="layui-input-block">
                                <input type="number" name="ckcount" value="${account?.ckcount || 0}"
                                       placeholder="请输入保存CK次数" class="layui-input" lay-verify="number">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="saveAccount">${isEdit ? '更新' : '保存'}</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                `;

                layer.open({
                    type: 1,
                    title: title,
                    content: formHtml,
                    area: ['500px', '600px'],
                    success: function (layero, index) {
                        form.render();

                        // 表单提交
                        form.on('submit(saveAccount)', function (data) {
                            const formData = data.field;

                            if (isEdit) {
                                updateAccount(account.id, formData);
                            } else {
                                addAccount(formData);
                            }

                            layer.close(index);
                            return false;
                        });
                    }
                });
            }

            // 添加账号
            async function addAccount(formData) {
                try {
                    const response = await axios.post('/api/douyin/accounts', formData);

                    if (response.data.code === 0) {
                        layer.msg('账号添加成功', { icon: 1 });
                        loadAccounts(); // 重新加载数据
                    } else {
                        layer.msg(response.data.msg || '添加账号失败', { icon: 2 });
                    }
                } catch (error) {
                    console.error('添加账号失败:', error);
                    layer.msg('添加账号失败', { icon: 2 });
                }
            }

            // 更新账号
            async function updateAccount(id, formData) {
                try {
                    const response = await axios.put(`/api/douyin/accounts/${id}`, formData);

                    if (response.data.code === 0) {
                        layer.msg('账号更新成功', { icon: 1 });
                        loadAccounts(); // 重新加载数据
                    } else {
                        layer.msg(response.data.msg || '更新账号失败', { icon: 2 });
                    }
                } catch (error) {
                    console.error('更新账号失败:', error);
                    layer.msg('更新账号失败', { icon: 2 });
                }
            }

            // 编辑账号
            function editAccount(id) {
                const account = accountsData.find(acc => acc.id === id);
                if (account) {
                    showAccountForm(account);
                }
            }

            // 查看账号详情
            function viewAccount(id) {
                const account = accountsData.find(acc => acc.id === id);
                if (!account) return;

                const statusText = {
                    'active': '正常',
                    'inactive': '异常',
                    'disabled': '禁用'
                }[account.status];

                const createTime = account.created_at ? new Date(account.created_at).toLocaleString('zh-CN') : '未知';

                const detailHtml = `
                    <div style="padding: 20px; line-height: 2;">
                        <div style="margin-bottom: 15px;">
                            <strong>用户名：</strong>${account.username}
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>密码：</strong>******
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>账号状态：</strong><span style="color: ${account.status === 'active' ? '#52c41a' : account.status === 'inactive' ? '#fa8c16' : '#f5222d'}">${statusText}</span>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>保存CK次数：</strong>${account.ckcount || 0}
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>创建时间：</strong>${createTime}
                        </div>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '账号详情',
                    content: detailHtml,
                    area: ['400px', '500px'],
                    btn: ['编辑', '关闭'],
                    yes: function (index) {
                        layer.close(index);
                        editAccount(id);
                    }
                });
            }

            // 删除账号
            async function deleteAccount(id) {
                const account = accountsData.find(acc => acc.id === id);
                if (!account) return;

                layer.confirm(`确定要删除账号"${account.username}"吗？`, {
                    icon: 3,
                    title: '确认删除'
                }, async function (index) {
                    try {
                        const response = await axios.delete(`/api/douyin/accounts/${id}`);

                        if (response.data.code === 0) {
                            layer.msg('账号删除成功', { icon: 1 });
                            loadAccounts(); // 重新加载数据
                        } else {
                            layer.msg(response.data.msg || '删除账号失败', { icon: 2 });
                        }
                    } catch (error) {
                        console.error('删除账号失败:', error);
                        layer.msg('删除账号失败', { icon: 2 });
                    }
                    layer.close(index);
                });
            }

            // 显示批量导入对话框
            function showImportDialog() {
                const importHtml = `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <h4>批量导入说明：</h4>
                            <p style="color: #666; line-height: 1.6;">
                                请按照以下格式输入账号信息，每行一个账号：<br>
                                <code>用户名,密码,状态,保存CK次数</code><br><br>
                                示例：<br>
                                <code>testuser001,password123,active,5</code><br>
                                <code>testuser002,password456,inactive,3</code>
                            </p>
                        </div>
                        <div>
                            <textarea id="importData" placeholder="请输入账号数据..." style="width: 100%; height: 200px; border: 1px solid #e6e6e6; padding: 10px; border-radius: 4px;"></textarea>
                        </div>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '批量导入账号',
                    content: importHtml,
                    area: ['600px', '450px'],
                    btn: ['导入', '取消'],
                    yes: function (index) {
                        const importData = document.getElementById('importData').value.trim();
                        if (!importData) {
                            layer.msg('请输入要导入的数据', { icon: 2 });
                            return;
                        }

                        importAccounts(importData);
                        layer.close(index);
                    }
                });
            }

            // 批量导入账号
            async function importAccounts(data) {
                const lines = data.split('\n').filter(line => line.trim());
                const accounts = [];

                // 解析数据
                for (let i = 0; i < lines.length; i++) {
                    try {
                        const parts = lines[i].split(',').map(part => part.trim());
                        if (parts.length < 4) {
                            continue; // 跳过格式不正确的行
                        }

                        const [username, password, status, ckcount = '0'] = parts;

                        // 验证必填字段
                        if (!username || !password || !status) {
                            continue;
                        }

                        // 验证状态
                        if (!['active', 'inactive', 'disabled'].includes(status)) {
                            continue;
                        }

                        accounts.push({
                            username,
                            password,
                            status,
                            ckcount: parseInt(ckcount) || 0
                        });
                    } catch (error) {
                        console.error(`第${i + 1}行解析失败:`, error.message);
                    }
                }

                if (accounts.length === 0) {
                    layer.msg('没有有效的账号数据', { icon: 2 });
                    return;
                }

                // 调用后端API
                try {
                    const response = await axios.post('/api/douyin/accounts/batch-import', { accounts });

                    if (response.data.code === 0) {
                        layer.msg(response.data.msg, { icon: 1 });
                        loadAccounts(); // 重新加载数据
                    } else {
                        layer.msg(response.data.msg || '批量导入失败', { icon: 2 });
                    }
                } catch (error) {
                    console.error('批量导入失败:', error);
                    layer.msg('批量导入失败', { icon: 2 });
                }
            }

            init();
        });
    </script>
</body>

</html>