
    <!DOCTYPE html>
    <html>
    <head>
      <title>验证码: XSZG</title>
      <style>
        body { 
          margin: 0; 
          padding: 20px; 
          font-family: Arial, sans-serif; 
          background: #f5f5f5;
        }
        .captcha-container {
          text-align: center;
          background: white;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          max-width: 300px;
          margin: 0 auto;
        }
        .captcha-image {
          border: 2px solid #ddd;
          border-radius: 4px;
          margin: 10px 0;
        }
        .info {
          color: #666;
          font-size: 14px;
          margin-top: 10px;
        }
      </style>
    </head>
    <body>
      <div class="captcha-container">
        <h2>验证码图片</h2>
        <div class="captcha-image">
          
    <svg width="100" height="40" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#F8F9FA"/>
      <line x1="93.12024679828836" y1="20.10026381170003" x2="42.703426051073734" y2="29.38141498690582" stroke="#F7DC6F" stroke-width="1" opacity="0.3"/><line x1="67.93568082377668" y1="1.4649260361111516" x2="78.3336928119581" y2="8.758501892838071" stroke="#4ECDC4" stroke-width="1" opacity="0.3"/><line x1="56.93376757922963" y1="34.4507810570832" x2="50.964067121797086" y2="26.876759269010144" stroke="#45B7D1" stroke-width="1" opacity="0.3"/>
      <circle cx="33.976670187483315" cy="0.8092976222268966" r="1" fill="#F7DC6F" opacity="0.5"/><circle cx="30.40125058352061" cy="8.89104606316983" r="1" fill="#4ECDC4" opacity="0.5"/><circle cx="54.68124829381911" cy="3.147085856375167" r="1" fill="#4ECDC4" opacity="0.5"/><circle cx="11.47454076404253" cy="22.380526765423163" r="1" fill="#45B7D1" opacity="0.5"/><circle cx="54.574743806392846" cy="39.50800479939325" r="1" fill="#4ECDC4" opacity="0.5"/><circle cx="74.77857433531167" cy="7.818649837078979" r="1" fill="#4ECDC4" opacity="0.5"/><circle cx="36.526265791533" cy="33.212040208572105" r="1" fill="#FFEAA7" opacity="0.5"/><circle cx="8.58630293743996" cy="16.44606062249709" r="1" fill="#4ECDC4" opacity="0.5"/><circle cx="81.59552240746673" cy="26.244498705840506" r="1" fill="#DDA0DD" opacity="0.5"/><circle cx="51.51862532034019" cy="9.353986275874169" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="75.66071293877694" cy="1.890619575747543" r="1" fill="#98D8C8" opacity="0.5"/><circle cx="47.85797228093227" cy="27.9449656279715" r="1" fill="#FF6B6B" opacity="0.5"/><circle cx="79.83491926770587" cy="8.23953589028454" r="1" fill="#F7DC6F" opacity="0.5"/><circle cx="23.047135968337518" cy="15.753625410836474" r="1" fill="#FFEAA7" opacity="0.5"/><circle cx="98.23193568648672" cy="21.083564195329217" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="52.474707276501675" cy="21.56433796430356" r="1" fill="#FFEAA7" opacity="0.5"/><circle cx="78.62056259591137" cy="35.73413675706155" r="1" fill="#4ECDC4" opacity="0.5"/><circle cx="96.99909882703821" cy="39.89400956576144" r="1" fill="#98D8C8" opacity="0.5"/><circle cx="36.418979149937414" cy="7.253697947384294" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="24.35966113361825" cy="11.486230425219901" r="1" fill="#DDA0DD" opacity="0.5"/>
      
      <text x="20" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#98D8C8" 
            text-anchor="middle"
            transform="rotate(-7.990927160508674 20 25.333333333333332)">
        X
      </text>
      <text x="40" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#4ECDC4" 
            text-anchor="middle"
            transform="rotate(-11.571461114903265 40 25.333333333333332)">
        S
      </text>
      <text x="60" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#4ECDC4" 
            text-anchor="middle"
            transform="rotate(2.494124108521558 60 25.333333333333332)">
        Z
      </text>
      <text x="80" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#45B7D1" 
            text-anchor="middle"
            transform="rotate(4.923777829942173 80 25.333333333333332)">
        G
      </text>
    </svg>
  
        </div>
        <div class="info">
          <strong>文件名:</strong> captcha1.jpg<br>
          <strong>验证码:</strong> XSZG<br>
          <strong>说明:</strong> 请将此图片保存为 captcha1.jpg
        </div>
      </div>
    </body>
    </html>
  