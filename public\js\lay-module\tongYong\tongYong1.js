// layui.define(['layer', 'form'], function (exports) {
//     var $ = layui.$
//     var form = layui.form
//     var layer = layui.layer

//     var tongYong = {
//         tongYong1: function () {
//             var savedFormData = [];
//             var storage = window.localStorage;
//             var click_store = null
//             let groups_check = {}
            
//             // 获取配置缓存
//             let htmlName = window.document.location.pathname.replace(".html", "").replace("/page/task/", "")
//             let optionData = storage.getItem(htmlName)
            
//             if (optionData && optionData != "") {
//                 optionData = JSON.parse(optionData)
//                 for (let key in optionData) {
//                     try {
//                         if (optionData[key] && optionData[key] != "") {
//                             let temDom = document.getElementsByName(key)[0]
//                             if (temDom.type == "number" || temDom.type == "text" || temDom.type == "textarea") {
//                                 temDom.value = optionData[key]
//                             } else if (temDom.type == "radio") {
//                                 let idid = optionData[key]
//                                 if (/^\d+$/.test(idid)) {
//                                     idid = "数字" + idid
//                                 }
//                                 document.getElementById(idid).checked = "on"
//                                 form.render("radio");
//                             } else if (temDom.type == "checkbox") {
//                                 temDom.checked = "on"
//                             }
//                         }
//                     } catch (error) {
//                         console.log(error)
//                     }
//                 }
//             }

//             let groupData = storage.getItem("groupData")
//             if (!groupData || groupData == "") {
//                 $.get("/indexGroup", function (res, status) {
//                     if (status) {
//                         groupData = (res.data)
//                         storage.setItem("groupData", JSON.stringify(groupData))
//                         groupData.forEach(groupDoc => {
//                             addInput(groupDoc)
//                         })
//                         addInput({ _id: '空闲', groupName: '空闲列表' })
//                         addInput({ _id: '忙碌', groupName: '忙碌列表' })
//                         addInput({ _id: '全部', groupName: '全部列表' })
//                         form.render("checkbox");
//                     } else {
//                         layer.msg("访问服务器失败")
//                     }
//                 })
//             } else {
//                 groupData = JSON.parse(groupData)
//                 groupData.forEach(groupDoc => {
//                     addInput(groupDoc)
//                 })
//                 addInput({ _id: '空闲', groupName: '空闲列表' })
//                 addInput({ _id: '忙碌', groupName: '忙碌列表' })
//                 addInput({ _id: '全部', groupName: '全部列表' })
//                 form.render("checkbox");
//             }

//             // 改进的设备选择按钮创建函数
//             function addInput(groupDoc) {
//                 let button = document.createElement('input')
//                 button.type = "button"
//                 button.value = "◉" + groupDoc.groupName
//                 button.name = groupDoc.groupName
//                 button.className = "layui-btn layui-btn-primary"
//                 button.setAttribute('data-selected', 'false')
                
//                 if (groupDoc.groupName == "空闲列表") {
//                     button.className = "layui-btn"
//                     groups_check[groupDoc.groupName] = groupDoc._id
//                     button.setAttribute('data-selected', 'true')
//                 } else if (groupDoc.groupName == "忙碌列表") {
//                     button.className = "layui-btn layui-btn-danger"
//                 } else if (groupDoc.groupName == "全部列表") {
//                     button.className = "layui-btn layui-btn-normal"
//                 }

//                 button.onclick = function () {
//                     const isSelected = this.getAttribute('data-selected') === "true";
//                     console.log('按钮点击:', this.name, '当前状态:', isSelected);
                    
//                     if (isSelected) {
//                         this.className = this.className.includes('danger') ? 
//                             "layui-btn layui-btn-danger" : "layui-btn layui-btn-primary";
//                         delete groups_check[this.name];
//                         this.setAttribute('data-selected', 'false');
//                     } else {
//                         this.className = "layui-btn";
//                         groups_check[this.name] = groupDoc._id;
//                         this.setAttribute('data-selected', 'true');
//                     }
                    
//                     $("#inputaaa").attr("value", JSON.stringify(groups_check));
//                     console.log('更新后的选中设备:', groups_check);
//                 };
                
//                 document.getElementById("groups1").appendChild(button)
//             }

//             // 其他原有函数保持不变...
//             window.addEventListener("storage", function (e) {
//                 window.location.reload();
//             });

//             form.on('submit(tijiao)', function (data) {
//                 btn_renWu(data)
//                 return false;
//             });

//             form.on('submit(saveTask)', function (data) {
//                 let newTask = data.field
//                 for (let key in newTask) {
//                     if (newTask[key].includes("◉")) {
//                         delete newTask[key];
//                     }
//                 }
//                 savedFormData.push(newTask)
//                 let formattedData = savedFormData.map(function (item) {
//                     return JSON.stringify(item, null, 2);
//                 });
//                 $('#zhuiJia').html(formattedData.join("<br>"));
//                 return false;
//             });

//             form.on('submit(delTask)', function (data) {
//                 $('#zhuiJia').html("无任务组");
//                 savedFormData = []
//                 return false;
//             });

//             form.on('checkbox(quanXuan1)', function (data) {
//                 if (data.elem.checked) {
//                     groupData.forEach(groupDoc => {
//                         let dom = $('input[name="' + groupDoc.groupName + '"]')
//                         dom.attr("class", "layui-btn")
//                         groups_check[groupDoc.groupName] = groupDoc._id
//                         dom.attr('data-selected', 'true')
//                     })
//                     $("#inputaaa").attr("value", JSON.stringify(groups_check))
//                 } else {
//                     $("#groups1").children().attr("class", "layui-btn layui-btn-primary")
//                     $("#groups1").children().attr('data-selected', 'false')
//                     groups_check = {}
//                     $("#inputaaa").attr("value", "")
//                 }
//                 form.render("checkbox");
//             });

//             function btn_renWu(data, type, savedFormData) {
//                 console.log("提交任务发送的消息：",data);
                
//                 let groupDocArr = []
//                 let socketIdArr = []
//                 for (let groupName in groups_check) {
//                     let groupValue = groups_check[groupName]
//                     if (groupValue) {
//                         if (typeof (groupValue) == "string") {
//                             groupDocArr.push({ groupName: groupName, groupId: groupValue })
//                         } else {
//                             for (let i in groupValue) {
//                                 if (!socketIdArr.includes(groupValue[i])) {
//                                     socketIdArr.push(groupValue[i])
//                                 }
//                             }
//                         }
//                     }
//                 }
//                 if (groupDocArr.length == 0 && socketIdArr.length == 0) {
//                     layer.msg("没选择任何设备")
//                     return false
//                 }
                
//                 let taskData = data.field
//                 for (let oneData in taskData) {
//                     if (taskData[oneData].indexOf("◉") > -1) {
//                         delete taskData[oneData];
//                     }
//                 }
                
//                 // 原有任务处理逻辑...
//             }
//         }
//     }
//     exports('tongYong', tongYong);
// });
