const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

async function testNewUsageLogic() {
    console.log(colors.bold('🧪 测试新的使用次数逻辑'));
    console.log('='.repeat(70));
    console.log(colors.cyan('新逻辑: 获取代理时立即增加使用次数，返回更新后的结果\n'));

    try {
        // 1. 获取初始状态
        console.log(colors.blue('📋 步骤1: 获取浙江省代理的初始状态'));
        
        // 先通过省份列表查看当前状态
        const provincesResponse = await axios.get(`${BASE_URL}/get-available-provinces`);
        console.log('可用省份:', JSON.stringify(provincesResponse.data.data, null, 2));

        // 获取浙江省代理
        const initialResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=浙江省`);
        
        if (initialResponse.data.code !== 0 || !initialResponse.data.data) {
            console.log(colors.red('❌ 未找到浙江省代理'));
            return;
        }

        const initialProxy = initialResponse.data.data;
        console.log(colors.green('✅ 获取到代理:'));
        console.log(`   🆔 代理ID: ${initialProxy.id}`);
        console.log(`   🔗 SOCKS5: ${initialProxy.sk5.substring(0, 30)}...`);
        console.log(`   📊 使用次数: ${colors.bold(initialProxy.usage_count)} (获取后立即增加)`);
        console.log(`   🔄 状态: ${colors.bold(initialProxy.status || 'active')}`);

        // 2. 再次获取，验证使用次数继续增加
        console.log(colors.blue('\n🔄 步骤2: 再次获取同一代理，验证使用次数增加'));
        
        const secondResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=浙江省`);
        
        if (secondResponse.data.code === 0 && secondResponse.data.data) {
            const secondProxy = secondResponse.data.data;
            console.log(colors.green('✅ 第二次获取:'));
            console.log(`   📊 使用次数: ${colors.bold(secondProxy.usage_count)}`);
            console.log(`   🔄 状态: ${colors.bold(secondProxy.status || 'active')}`);
            
            const expectedCount = initialProxy.usage_count + 1;
            if (secondProxy.usage_count === expectedCount) {
                console.log(colors.green('✅ 验证通过: 使用次数正确增加了1'));
            } else {
                console.log(colors.red(`❌ 验证失败: 期望${expectedCount}，实际${secondProxy.usage_count}`));
            }
        } else {
            console.log(colors.yellow('⚠️  第二次获取失败，可能代理已被禁用'));
        }

        // 3. 测试批量获取
        console.log(colors.blue('\n📦 步骤3: 测试批量获取代理'));
        
        const batchResponse = await axios.get(`${BASE_URL}/get-proxies-by-province?province=浙江&limit=2`);
        
        console.log('批量获取响应:');
        console.log(JSON.stringify(batchResponse.data, null, 2));
        
        if (batchResponse.data.code === 0 && batchResponse.data.data.length > 0) {
            console.log(colors.green(`✅ 批量获取成功，返回${batchResponse.data.data.length}个代理`));
            batchResponse.data.data.forEach((proxy, index) => {
                console.log(`   ${index + 1}. ID:${proxy.id}, 使用次数:${proxy.usage_count}, 状态:${proxy.status || 'active'}`);
            });
        } else {
            console.log(colors.yellow('⚠️  批量获取无结果'));
        }

        // 4. 测试报告成功状态
        console.log(colors.blue('\n✅ 步骤4: 测试报告成功状态'));
        
        const reportSuccessData = {
            sk5: initialProxy.sk5,
            status: 'success'
        };
        
        console.log('发送成功报告:', JSON.stringify(reportSuccessData, null, 2));
        
        const successReportResponse = await axios.post(`${BASE_URL}/report-proxy-status`, reportSuccessData);
        console.log('成功报告响应:');
        console.log(JSON.stringify(successReportResponse.data, null, 2));
        
        if (successReportResponse.data.code === 0) {
            console.log(colors.green('✅ 成功状态报告完成'));
            console.log(`   📊 使用次数: ${successReportResponse.data.data.usage_count} (不再增加)`);
        }

        // 5. 测试报告失败状态
        console.log(colors.blue('\n❌ 步骤5: 测试报告失败状态'));
        
        const reportFailData = {
            sk5: initialProxy.sk5,
            status: 'failed',
            error_msg: '连接超时'
        };
        
        console.log('发送失败报告:', JSON.stringify(reportFailData, null, 2));
        
        const failReportResponse = await axios.post(`${BASE_URL}/report-proxy-status`, reportFailData);
        console.log('失败报告响应:');
        console.log(JSON.stringify(failReportResponse.data, null, 2));
        
        if (failReportResponse.data.code === 0) {
            console.log(colors.yellow('⚠️  失败状态报告完成'));
            console.log(`   📊 使用次数: ${failReportResponse.data.data.usage_count} (应该减少1)`);
        }

        // 6. 测试多次获取直到达到上限
        console.log(colors.blue('\n🔄 步骤6: 多次获取直到达到使用上限'));
        
        let attemptCount = 0;
        const maxAttempts = 15; // 防止无限循环
        
        while (attemptCount < maxAttempts) {
            attemptCount++;
            console.log(colors.cyan(`\n--- 第${attemptCount}次尝试获取 ---`));
            
            const testResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=浙江省`);
            
            if (testResponse.data.code === 0 && testResponse.data.data) {
                const proxy = testResponse.data.data;
                console.log(`   使用次数: ${proxy.usage_count}, 状态: ${proxy.status || 'active'}`);
                
                if (proxy.status === 'disabled' || proxy.usage_count >= 10) {
                    console.log(colors.red('🚫 代理已达到使用上限并被禁用'));
                    break;
                }
            } else {
                console.log(colors.red('❌ 无法获取代理，可能已被禁用'));
                break;
            }
            
            // 短暂延迟
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        console.log(colors.bold('\n🎉 新使用次数逻辑测试完成!'));
        console.log('='.repeat(70));
        
        // 总结
        console.log(colors.bold('\n📊 新逻辑总结:'));
        console.log('✅ 获取代理时立即增加使用次数');
        console.log('✅ 返回更新后的使用次数');
        console.log('✅ 达到10次时自动禁用');
        console.log('✅ 报告成功时不再增加使用次数');
        console.log('✅ 报告失败时减少使用次数（回退）');

    } catch (error) {
        console.error(colors.red('❌ 测试过程中发生错误:'));
        console.error('错误信息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
console.log(colors.bold('🚀 新使用次数逻辑测试脚本'));
console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
console.log('\n' + colors.yellow('⏳ 3秒后开始测试...'));

setTimeout(() => {
    testNewUsageLogic().catch(error => {
        console.error(colors.red('💥 测试脚本执行失败:'), error.message);
        process.exit(1);
    });
}, 3000);
