// MongoDB用户模型已禁用
// 这个文件替代原来的userMod.js，避免MongoDB依赖

console.log('🚫 MongoDB用户模型已禁用');

// 创建一个模拟的用户模型以保持兼容性
const mockUserMod = {
    findOne: () => Promise.reject(new Error('MongoDB用户模型已禁用，请使用MySQL')),
    find: () => Promise.reject(new Error('MongoDB用户模型已禁用，请使用MySQL')),
    findById: () => Promise.reject(new Error('MongoDB用户模型已禁用，请使用MySQL')),
    create: () => Promise.reject(new Error('MongoDB用户模型已禁用，请使用MySQL')),
    updateOne: () => Promise.reject(new Error('MongoDB用户模型已禁用，请使用MySQL')),
    deleteOne: () => Promise.reject(new Error('MongoDB用户模型已禁用，请使用MySQL')),
    aggregate: () => Promise.reject(new Error('MongoDB用户模型已禁用，请使用MySQL')),
    save: () => Promise.reject(new Error('MongoDB用户模型已禁用，请使用MySQL'))
};

// 导出模拟对象
module.exports = mockUserMod;

console.log('💡 提示: 用户管理功能已禁用，如需用户管理请使用MySQL数据库');
