<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API数据测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 15px 0; padding: 15px; border-radius: 5px; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
        .stats-display { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 15px 0; }
        .stat-card { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007cba; }
        .stat-label { font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API数据测试</h1>
        
        <div class="test-section">
            <h3>步骤1: 登录</h3>
            <button onclick="testLogin()">登录测试</button>
            <div id="loginResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>步骤2: 获取统计数据</h3>
            <button onclick="testStats()">获取统计数据</button>
            <div id="statsResult" class="result"></div>
            
            <div class="stats-display" id="statsDisplay" style="display: none;">
                <div class="stat-card">
                    <div class="stat-number" id="visitCount">0</div>
                    <div class="stat-label">访问次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="uniqueIpCount">0</div>
                    <div class="stat-label">独立IP数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="scanCount">0</div>
                    <div class="stat-label">扫码数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successCount">0</div>
                    <div class="stat-label">成功数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failCount">0</div>
                    <div class="stat-label">失败数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="expireCount">0</div>
                    <div class="stat-label">过期数量</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>步骤3: 获取操作记录</h3>
            <button onclick="testActions()">获取操作记录</button>
            <div id="actionsResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>一键完整测试</h3>
            <button onclick="runFullTest()">运行完整测试</button>
            <div id="fullTestResult" class="result"></div>
        </div>
    </div>

    <script>
        // 登录测试
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = '正在登录...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: '1001', password: '123456' })
                });
                
                const data = await response.json();
                
                const resultText = `登录结果:
状态码: ${response.status}
响应: ${JSON.stringify(data, null, 2)}
Cookies: ${document.cookie}`;
                
                resultDiv.textContent = resultText;
                
                if (data.code === 0) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent += '\n\n✅ 登录成功！';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent += '\n\n❌ 登录失败！';
                }
                
            } catch (error) {
                resultDiv.textContent = `登录失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 统计数据测试
        async function testStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.textContent = '正在获取统计数据...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch('/api/promotion/promoter/today-stats', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                const resultText = `统计数据结果:
状态码: ${response.status}
响应: ${JSON.stringify(data, null, 2)}`;
                
                resultDiv.textContent = resultText;
                
                if (data.code === 0) {
                    const stats = data.data;
                    
                    // 更新显示卡片
                    document.getElementById('visitCount').textContent = stats.visit_count;
                    document.getElementById('uniqueIpCount').textContent = stats.unique_ip_count;
                    document.getElementById('scanCount').textContent = stats.scan_count;
                    document.getElementById('successCount').textContent = stats.success_count;
                    document.getElementById('failCount').textContent = stats.fail_count;
                    document.getElementById('expireCount').textContent = stats.expire_count;
                    document.getElementById('statsDisplay').style.display = 'grid';
                    
                    const hasData = Object.values(stats).some(val => val > 0);
                    
                    resultDiv.textContent += `

数据分析:
访问次数: ${stats.visit_count}
独立IP数: ${stats.unique_ip_count}
扫码数量: ${stats.scan_count}
成功数量: ${stats.success_count}
失败数量: ${stats.fail_count}
过期数量: ${stats.expire_count}
有非零数据: ${hasData}`;
                    
                    if (hasData) {
                        resultDiv.className = 'result success';
                        resultDiv.textContent += '\n\n✅ 数据获取成功，有非零数据！';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent += '\n\n⚠️ 数据全部为0！';
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent += '\n\n❌ 获取统计数据失败！';
                }
                
            } catch (error) {
                resultDiv.textContent = `获取统计数据失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 操作记录测试
        async function testActions() {
            const resultDiv = document.getElementById('actionsResult');
            resultDiv.textContent = '正在获取操作记录...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch('/api/promotion/promoter/today-actions?page=1&limit=5', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                const resultText = `操作记录结果:
状态码: ${response.status}
响应: ${JSON.stringify(data, null, 2)}`;
                
                resultDiv.textContent = resultText;
                
                if (data.code === 0) {
                    const actions = data.data;
                    
                    resultDiv.textContent += `

数据分析:
记录总数: ${actions.total}
当前页记录数: ${actions.list.length}`;
                    
                    if (actions.list.length > 0) {
                        resultDiv.className = 'result success';
                        resultDiv.textContent += '\n\n✅ 操作记录获取成功！';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent += '\n\n⚠️ 没有操作记录！';
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent += '\n\n❌ 获取操作记录失败！';
                }
                
            } catch (error) {
                resultDiv.textContent = `获取操作记录失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 完整测试
        async function runFullTest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.textContent = '🚀 开始完整测试流程...\n';
            resultDiv.className = 'result info';
            
            try {
                // 1. 登录
                resultDiv.textContent += '1️⃣ 正在登录...\n';
                await testLogin();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 2. 获取统计数据
                resultDiv.textContent += '2️⃣ 正在获取统计数据...\n';
                await testStats();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 3. 获取操作记录
                resultDiv.textContent += '3️⃣ 正在获取操作记录...\n';
                await testActions();
                
                resultDiv.textContent += '\n🎉 完整测试完成！请查看上方各个测试结果。';
                resultDiv.className = 'result success';
                
            } catch (error) {
                resultDiv.textContent += `\n❌ 完整测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 页面加载时显示初始状态
        window.onload = function() {
            console.log('📋 API数据测试页面已加载');
            console.log('当前cookies:', document.cookie);
        };
    </script>
</body>
</html>
