<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .captcha-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .captcha-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .captcha-img {
            border: 1px solid #ddd;
            cursor: pointer;
            border-radius: 4px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-danger {
            background: #dc3545;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .stats-table th {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>验证码系统测试</h1>
        
        <!-- 基础验证码测试 -->
        <div class="test-section">
            <h3>1. 基础验证码功能测试</h3>
            <div class="captcha-container">
                <input type="text" id="captchaInput1" class="captcha-input" placeholder="请输入验证码" maxlength="4">
                <img id="captchaImg1" class="captcha-img" src="/images/captcha1.jpg" alt="验证码" width="100" height="40">
                <button class="btn" onclick="refreshCaptcha('captchaImg1')">刷新</button>
                <button class="btn btn-success" onclick="verifyCaptcha('captchaInput1')">验证</button>
            </div>
            <div id="result1" class="result info">点击"刷新"获取验证码，然后输入验证码进行测试</div>
        </div>

        <!-- 错误处理测试 -->
        <div class="test-section">
            <h3>2. 错误处理和自动切换测试</h3>
            <div class="captcha-container">
                <input type="text" id="captchaInput2" class="captcha-input" placeholder="故意输入错误验证码">
                <img id="captchaImg2" class="captcha-img" src="/images/captcha1.jpg" alt="验证码" width="100" height="40">
                <button class="btn btn-danger" onclick="testWrongCaptcha()">测试错误验证码</button>
            </div>
            <div id="result2" class="result info">这个测试会故意输入错误的验证码，观察自动切换效果</div>
        </div>

        <!-- 批量测试 -->
        <div class="test-section">
            <h3>3. 批量随机验证码测试</h3>
            <button class="btn" onclick="batchTest()">批量测试10次</button>
            <button class="btn" onclick="clearBatchResults()">清空结果</button>
            <div id="batchResults" class="result info">点击"批量测试"查看随机验证码效果</div>
        </div>

        <!-- 统计信息 -->
        <div class="test-section">
            <h3>4. 验证码统计信息</h3>
            <button class="btn" onclick="loadStats()">获取统计信息</button>
            <div id="statsContainer">
                <div class="result info">点击"获取统计信息"查看当前验证码状态</div>
            </div>
        </div>

        <!-- 验证码配置信息 -->
        <div class="test-section">
            <h3>5. 验证码配置信息</h3>
            <div class="result info">
                <strong>验证码图片列表:</strong><br>
                1. captcha1.jpg - XSZG<br>
                2. captcha2.jpg - MNBV<br>
                3. captcha3.jpg - QWER<br>
                4. captcha4.jpg - ASDF<br>
                5. captcha5.jpg - ZXCV<br>
                6. captcha6.jpg - TYUI<br>
                7. captcha7.jpg - GHJK<br>
                8. captcha8.jpg - BNML<br>
                9. captcha9.jpg - POIU<br>
                10. captcha10.jpg - LKJH<br><br>
                <strong>功能特点:</strong><br>
                - 随机选择验证码图片<br>
                - 错误时自动切换新图片<br>
                - 5分钟过期时间<br>
                - 最多尝试5次<br>
                - 会话级别的验证码管理
            </div>
        </div>
    </div>

    <script src="/lib/axios/axios.min.js"></script>
    <script>
        // 验证码管理器
        const CaptchaTestManager = {
            // 获取新验证码
            getCaptcha: function() {
                return axios.get('/api/captcha/get-captcha')
                    .then(res => {
                        if (res.data.code === 0) {
                            return res.data.data;
                        } else {
                            throw new Error(res.data.msg);
                        }
                    });
            },

            // 刷新验证码
            refreshCaptcha: function() {
                return axios.post('/api/captcha/refresh-captcha')
                    .then(res => {
                        if (res.data.code === 0) {
                            return res.data.data;
                        } else {
                            throw new Error(res.data.msg);
                        }
                    });
            },

            // 验证验证码
            verifyCaptcha: function(code) {
                return axios.post('/api/captcha/verify-captcha', { captcha: code })
                    .then(res => {
                        return res.data;
                    });
            },

            // 获取统计信息
            getStats: function() {
                return axios.get('/api/captcha/captcha-stats')
                    .then(res => {
                        if (res.data.code === 0) {
                            return res.data.data;
                        } else {
                            throw new Error(res.data.msg);
                        }
                    });
            }
        };

        // 刷新验证码
        function refreshCaptcha(imgId) {
            const img = document.getElementById(imgId);
            const resultId = imgId.replace('captchaImg', 'result');
            const resultDiv = document.getElementById(resultId);

            resultDiv.textContent = '正在刷新验证码...';
            resultDiv.className = 'result info';

            CaptchaTestManager.refreshCaptcha()
                .then(data => {
                    img.src = data.image + '?t=' + data.timestamp;
                    resultDiv.textContent = '✅ 验证码刷新成功，图片: ' + data.image;
                    resultDiv.className = 'result success';
                })
                .catch(error => {
                    resultDiv.textContent = '❌ 刷新失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }

        // 验证验证码
        function verifyCaptcha(inputId) {
            const input = document.getElementById(inputId);
            const resultId = inputId.replace('captchaInput', 'result');
            const resultDiv = document.getElementById(resultId);
            const imgId = inputId.replace('captchaInput', 'captchaImg');
            const img = document.getElementById(imgId);

            const code = input.value.trim();
            if (!code) {
                resultDiv.textContent = '❌ 请输入验证码';
                resultDiv.className = 'result error';
                return;
            }

            resultDiv.textContent = '正在验证...';
            resultDiv.className = 'result info';

            CaptchaTestManager.verifyCaptcha(code)
                .then(result => {
                    if (result.code === 0) {
                        resultDiv.textContent = '✅ ' + result.msg;
                        resultDiv.className = 'result success';
                        input.value = '';
                    } else {
                        resultDiv.textContent = '❌ ' + result.msg;
                        resultDiv.className = 'result error';
                        
                        // 如果需要刷新验证码
                        if (result.data && result.data.needRefresh && result.data.newCaptcha) {
                            img.src = result.data.newCaptcha.image + '?t=' + result.data.newCaptcha.timestamp;
                            input.value = '';
                            resultDiv.textContent += '\n🔄 已自动切换到新验证码';
                        }
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '❌ 验证失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }

        // 测试错误验证码
        function testWrongCaptcha() {
            const input = document.getElementById('captchaInput2');
            const resultDiv = document.getElementById('result2');
            const img = document.getElementById('captchaImg2');

            // 输入一个肯定错误的验证码
            input.value = 'WRONG';
            
            resultDiv.textContent = '正在测试错误验证码...';
            resultDiv.className = 'result info';

            CaptchaTestManager.verifyCaptcha('WRONG')
                .then(result => {
                    resultDiv.textContent = '❌ ' + result.msg;
                    resultDiv.className = 'result error';
                    
                    // 如果需要刷新验证码
                    if (result.data && result.data.needRefresh && result.data.newCaptcha) {
                        img.src = result.data.newCaptcha.image + '?t=' + result.data.newCaptcha.timestamp;
                        input.value = '';
                        resultDiv.textContent += '\n🔄 已自动切换到新验证码: ' + result.data.newCaptcha.image;
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '❌ 测试失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }

        // 批量测试
        function batchTest() {
            const resultDiv = document.getElementById('batchResults');
            resultDiv.textContent = '开始批量测试...\n';
            resultDiv.className = 'result info';

            let results = [];
            let promises = [];

            for (let i = 0; i < 10; i++) {
                const promise = CaptchaTestManager.refreshCaptcha()
                    .then(data => {
                        results.push(`${i + 1}. 获取验证码: ${data.image} (时间戳: ${data.timestamp})`);
                        return data;
                    })
                    .catch(error => {
                        results.push(`${i + 1}. 错误: ${error.message}`);
                    });
                
                promises.push(promise);
            }

            Promise.all(promises)
                .then(() => {
                    resultDiv.textContent = '批量测试完成:\n' + results.join('\n');
                    resultDiv.className = 'result success';
                })
                .catch(error => {
                    resultDiv.textContent = '批量测试失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }

        // 清空批量测试结果
        function clearBatchResults() {
            const resultDiv = document.getElementById('batchResults');
            resultDiv.textContent = '点击"批量测试"查看随机验证码效果';
            resultDiv.className = 'result info';
        }

        // 加载统计信息
        function loadStats() {
            const container = document.getElementById('statsContainer');
            container.innerHTML = '<div class="result info">正在加载统计信息...</div>';

            CaptchaTestManager.getStats()
                .then(stats => {
                    let html = '<div class="result success">';
                    html += `<strong>验证码统计信息:</strong><br>`;
                    html += `总验证码数量: ${stats.totalCaptchas}<br>`;
                    html += `活跃会话数: ${stats.activeSessions}<br><br>`;
                    
                    if (stats.sessions.length > 0) {
                        html += '<table class="stats-table">';
                        html += '<thead><tr><th>会话ID</th><th>验证码图片</th><th>尝试次数</th><th>最大尝试次数</th><th>创建时间</th><th>剩余时间(ms)</th></tr></thead>';
                        html += '<tbody>';
                        
                        stats.sessions.forEach(session => {
                            html += '<tr>';
                            html += `<td>${session.sessionId}</td>`;
                            html += `<td>${session.image}</td>`;
                            html += `<td>${session.attempts}</td>`;
                            html += `<td>${session.maxAttempts}</td>`;
                            html += `<td>${session.createTime}</td>`;
                            html += `<td>${session.remainingTime}</td>`;
                            html += '</tr>';
                        });
                        
                        html += '</tbody></table>';
                    } else {
                        html += '<em>当前没有活跃的验证码会话</em>';
                    }
                    
                    html += '</div>';
                    container.innerHTML = html;
                })
                .catch(error => {
                    container.innerHTML = `<div class="result error">加载统计信息失败: ${error.message}</div>`;
                });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 验证码测试页面已加载');
            
            // 自动加载第一个验证码
            refreshCaptcha('captchaImg1');
            refreshCaptcha('captchaImg2');
        });
    </script>
</body>
</html>
