-- 推广操作记录表
CREATE TABLE IF NOT EXISTS promotion_actions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    promotion_user_id VARCHAR(50) NOT NULL COMMENT '推广用户ID',
    action_type ENUM(
        'scan',
        'login_success',
        'login_fail',
        'request_expire'
    ) NOT NULL COMMENT '操作类型',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
    ip_province VARCHAR(50) COMMENT 'IP所属省份',
    douyin_name VARCHAR(200) COMMENT '抖音名',
    douyin_id VARCHAR(100) COMMENT '抖音号',
    ck_data TEXT COMMENT 'CK数据',
    user_agent TEXT COMMENT '用户代理',
    action_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    extra_data JSON COMMENT '额外数据',
    INDEX idx_promotion_user_id (promotion_user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_ip_address (ip_address),
    INDEX idx_action_time (action_time),
    INDEX idx_ip_province (ip_province)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '推广操作记录表';
-- 推广访问记录表
CREATE TABLE IF NOT EXISTS promotion_visits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    promotion_user_id VARCHAR(50) NOT NULL COMMENT '推广用户ID',
    visitor_ip VARCHAR(45) NOT NULL COMMENT '访问者IP',
    user_agent TEXT COMMENT '用户代理',
    referer VARCHAR(500) COMMENT '来源页面',
    visit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
    INDEX idx_promotion_user_id (promotion_user_id),
    INDEX idx_visitor_ip (visitor_ip),
    INDEX idx_visit_time (visit_time)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '推广访问记录表';
-- 插入示例访问数据
INSERT INTO promotion_visits (
        promotion_user_id,
        visitor_ip,
        user_agent,
        referer
    )
VALUES (
        '1001',
        '*************',
        'Mozilla/5.0 Windows',
        'http://localhost:15001?id=1001'
    ),
    (
        '1001',
        '*************',
        'Mozilla/5.0 Windows',
        'http://localhost:15001?id=1001'
    ),
    (
        '1002',
        '*************',
        'Mozilla/5.0 iPhone',
        'http://localhost:15001?id=1002'
    ),
    (
        '1001',
        '*************',
        'Mozilla/5.0 Android',
        'http://localhost:15001?id=1001'
    ),
    (
        '1003',
        '*************',
        'Mozilla/5.0 Windows',
        'http://localhost:15001?id=1003'
    );
-- 插入示例操作数据
INSERT INTO promotion_actions (
        promotion_user_id,
        action_type,
        ip_address,
        ip_province,
        douyin_name,
        douyin_id,
        ck_data
    )
VALUES (
        '1001',
        'scan',
        '*************',
        '北京',
        NULL,
        NULL,
        NULL
    ),
    (
        '1001',
        'login_success',
        '*************',
        '北京',
        '测试用户1',
        'dy123456',
        'sessionid=abc123'
    ),
    (
        '1001',
        'scan',
        '*************',
        '上海',
        NULL,
        NULL,
        NULL
    ),
    (
        '1001',
        'login_fail',
        '*************',
        '上海',
        NULL,
        NULL,
        NULL
    ),
    (
        '1002',
        'scan',
        '*************',
        '广东',
        NULL,
        NULL,
        NULL
    ),
    (
        '1002',
        'login_success',
        '*************',
        '广东',
        '测试用户2',
        'dy789012',
        'sessionid=ghi789'
    ),
    (
        '1001',
        'request_expire',
        '*************',
        '浙江',
        NULL,
        NULL,
        NULL
    ),
    (
        '1003',
        'scan',
        '*************',
        '江苏',
        NULL,
        NULL,
        NULL
    ),
    (
        '1003',
        'login_success',
        '*************',
        '江苏',
        '测试用户3',
        'dy345678',
        'sessionid=mno345'
    ),
    (
        '1002',
        'scan',
        '*************',
        '广东',
        NULL,
        NULL,
        NULL
    ),
    (
        '1002',
        'login_fail',
        '*************',
        '广东',
        NULL,
        NULL,
        NULL
    );
-- 更新user表中的统计数据
UPDATE user
SET visit_count = (
        SELECT COUNT(*)
        FROM promotion_visits
        WHERE promotion_user_id = user.promoter_id
    ),
    unique_ip_count = (
        SELECT COUNT(DISTINCT visitor_ip)
        FROM promotion_visits
        WHERE promotion_user_id = user.promoter_id
    ),
    scan_count = (
        SELECT COUNT(*)
        FROM promotion_actions
        WHERE promotion_user_id = user.promoter_id
            AND action_type = 'scan'
    ),
    success_count = (
        SELECT COUNT(*)
        FROM promotion_actions
        WHERE promotion_user_id = user.promoter_id
            AND action_type = 'login_success'
    ),
    fail_count = (
        SELECT COUNT(*)
        FROM promotion_actions
        WHERE promotion_user_id = user.promoter_id
            AND action_type = 'login_fail'
    ),
    expire_count = (
        SELECT COUNT(*)
        FROM promotion_actions
        WHERE promotion_user_id = user.promoter_id
            AND action_type = 'request_expire'
    ),
    last_stat_update = NOW()
WHERE user_type = 'promoter';