const fs = require('fs');
const path = require('path');

// 创建多张验证码图片
function createCaptchaImages() {
  console.log('🖼️ 创建验证码图片...');
  
  const sourceImage = path.join(__dirname, 'public', 'images', 'captcha.jpg');
  const targetDir = path.join(__dirname, 'public', 'images');
  
  // 检查源图片是否存在
  if (!fs.existsSync(sourceImage)) {
    console.error('❌ 源验证码图片不存在:', sourceImage);
    return;
  }
  
  // 验证码图片列表
  const captchaImages = [
    'captcha1.jpg',
    'captcha2.jpg', 
    'captcha3.jpg',
    'captcha4.jpg',
    'captcha5.jpg',
    'captcha6.jpg',
    'captcha7.jpg',
    'captcha8.jpg',
    'captcha9.jpg',
    'captcha10.jpg'
  ];
  
  try {
    // 复制源图片到多个验证码图片
    captchaImages.forEach((imageName, index) => {
      const targetPath = path.join(targetDir, imageName);
      
      if (!fs.existsSync(targetPath)) {
        fs.copyFileSync(sourceImage, targetPath);
        console.log(`✅ 创建验证码图片: ${imageName}`);
      } else {
        console.log(`⚠️  验证码图片已存在: ${imageName}`);
      }
    });
    
    console.log('\n🎉 验证码图片创建完成！');
    console.log('\n📋 创建的图片列表:');
    captchaImages.forEach((imageName, index) => {
      console.log(`${index + 1}. ${imageName}`);
    });
    
    console.log('\n💡 提示:');
    console.log('- 这些图片目前都是相同的，在实际使用中应该替换为不同的验证码图片');
    console.log('- 每张图片对应不同的验证码文本，在 routes/captcha.js 中配置');
    console.log('- 可以使用在线验证码生成器或图片编辑软件创建不同的验证码图片');
    
  } catch (error) {
    console.error('❌ 创建验证码图片失败:', error.message);
  }
}

// 创建验证码图片说明文件
function createCaptchaReadme() {
  const readmeContent = `# 验证码图片说明

## 图片列表和对应验证码

| 图片文件 | 验证码文本 | 说明 |
|---------|-----------|------|
| captcha1.jpg | XSZG | 验证码图片1 |
| captcha2.jpg | MNBV | 验证码图片2 |
| captcha3.jpg | QWER | 验证码图片3 |
| captcha4.jpg | ASDF | 验证码图片4 |
| captcha5.jpg | ZXCV | 验证码图片5 |
| captcha6.jpg | TYUI | 验证码图片6 |
| captcha7.jpg | GHJK | 验证码图片7 |
| captcha8.jpg | BNML | 验证码图片8 |
| captcha9.jpg | POIU | 验证码图片9 |
| captcha10.jpg | LKJH | 验证码图片10 |

## 使用说明

1. **替换图片**: 将上述图片替换为真实的验证码图片
2. **验证码文本**: 确保图片中显示的文本与配置中的验证码文本一致
3. **图片格式**: 支持 jpg, png, gif 等常见格式
4. **图片尺寸**: 建议尺寸为 100x40 像素，适合登录界面显示

## 生成验证码图片的方法

### 方法1: 在线生成器
- 使用在线验证码生成器
- 设置合适的尺寸和字体
- 下载并重命名为对应的文件名

### 方法2: 编程生成
- 使用 Canvas API 或图片处理库
- 生成包含随机文本的验证码图片
- 保存为对应的文件名

### 方法3: 图片编辑软件
- 使用 Photoshop、GIMP 等软件
- 创建包含验证码文本的图片
- 添加干扰线、噪点等增加识别难度

## 安全建议

1. **定期更换**: 定期更换验证码图片和文本
2. **增加复杂度**: 添加干扰元素，增加机器识别难度
3. **限制尝试次数**: 系统已实现尝试次数限制
4. **会话管理**: 验证码与用户会话绑定，防止重复使用
`;

  const readmePath = path.join(__dirname, 'public', 'images', 'CAPTCHA_README.md');
  
  try {
    fs.writeFileSync(readmePath, readmeContent, 'utf8');
    console.log('✅ 创建验证码说明文件: CAPTCHA_README.md');
  } catch (error) {
    console.error('❌ 创建说明文件失败:', error.message);
  }
}

// 执行创建
if (require.main === module) {
  createCaptchaImages();
  createCaptchaReadme();
}

module.exports = {
  createCaptchaImages,
  createCaptchaReadme
};
