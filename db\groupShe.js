const mongoose = require('./db.js');
//2.创建约束对象Schema(定义结构),每个Schema对应一个collection(集合)
let groupShe = new mongoose.Schema({
    groupName: {
        type: String,
        index: { unique: true }    //是否唯一,并且加索引
    },
    groupStatus: String,

    created: {
        type: Date,
        default: localDate(),
        // 复合索引
        // index: { unique: false, expires: '1d' }//一天后删除数据
    },
})

function localDate(v) {
    const d = new Date(v || Date.now());
    d.setMinutes(d.getMinutes() - d.getTimezoneOffset());
    return d.toISOString();
}


//3.创建模型对象
//参数(集合对象,约束对象)
// let groupMod = mongoose.model('groupMod', groupSchema)

module.exports = groupShe;

