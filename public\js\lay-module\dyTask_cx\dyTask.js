layui.define(["layer", "form", "table", "util"], function (exports) {
  var $ = layui.$;
  var form = layui.form;
  var layer = layui.layer;
  var table = layui.table;

  var dyTask = {
    dyTask1: function () {
      var savedFormData = [];
      var storage = window.localStorage;
      var click_store = null;
      let groups_check = {};
      var globalTaskData = {}; // 存储任务数据的全局变量
      let htmlName = window.document.location.pathname
        .replace(".html", "")
        .replace("/page/task/", "");
      let optionData = storage.getItem(htmlName);
      //设置配置缓存
      if (optionData && optionData != "") {
        optionData = JSON.parse(optionData);
        for (let key in optionData) {
          try {
            if (optionData[key] && optionData[key] != "") {
              let temDom = document.getElementsByName(key)[0];
              if (temDom) {
                if (
                  temDom.type == "number" ||
                  temDom.type == "text" ||
                  temDom.type == "textarea"
                ) {
                  temDom.value = optionData[key];
                } else if (temDom.type == "radio") {
                  let idid = optionData[key];
                  if (/^\d+$/.test(idid)) {
                    idid = "数字" + idid;
                  }
                  document.getElementById(idid).checked = "on";
                  form.render("radio");
                } else if (temDom.type == "checkbox") {
                  temDom.checked = "on";
                } else {
                  console.log("未知dom类型:" + temDom.type);
                }
              } else {
                // console.log("元素未找到:", key);
              }
            }
          } catch (error) {
            console.log(error);
          }
        }
      }
      let groupData = storage.getItem("groupData");
      if (!groupData || groupData == "") {
        $.get("/indexGroup", function (res, status) {
          if (status) {
            groupData = res.data;
            storage.setItem("groupData", JSON.stringify(groupData));
            groupData.forEach((groupDoc) => {
              addInput(groupDoc);
            });
            addInput({ _id: "空闲", groupName: "空闲列表" });
            addInput({ _id: "忙碌", groupName: "忙碌列表" });
            addInput({ _id: "全部", groupName: "全部列表" });
            form.render("checkbox");
          } else {
            layer.msg("访问服务器失败");
          }
        });
      } else {
        groupData = JSON.parse(groupData);
        groupData.forEach((groupDoc) => {
          console.log("groupDoc", groupDoc);
          addInput(groupDoc);
        });
        addInput({ _id: "空闲", groupName: "空闲列表" });
        addInput({ _id: "忙碌", groupName: "忙碌列表" });
        addInput({ _id: "全部", groupName: "全部列表" });
        form.render("checkbox");
      }
      //监听storage事件
      window.addEventListener("storage", function (e) {
        window.location.reload();
      });

      form.on("submit(saveTask)", function (data) {
        let newTask = data.field;
        // 删除特定的键
        for (let key in newTask) {
          if (newTask[key].includes("◉")) {
            delete newTask[key];
          }
        }

        savedFormData.push(newTask);

        let formattedData = savedFormData.map(function (item) {
          return JSON.stringify(item, null, 2); // 格式化对象，使其更易于阅读
        });

        $("#zhuiJia").html(formattedData.join("<br>"));

        return false;
      });
      form.on("submit(delTask)", function (data) {
        $("#zhuiJia").html("无任务组");
        savedFormData = [];
        return false;
      });

      //监听全选
      form.on("checkbox(quanXuan1)", function (data) {
        if (data.elem.checked) {
          // $("#groups1").children().prop("class", "layui-btn")
          groupData.forEach((groupDoc) => {
            let dom = $('input[name="' + groupDoc.groupName + '"]');
            dom.attr("class", "layui-btn");
            // dom.attr("value", "✓" + group.groupName)
            groups_check[groupDoc.groupName] = groupDoc._id;
          });
          $("#inputaaa").attr("value", JSON.stringify(groups_check));
        } else {
          $("#groups1").children().prop("class", "layui-btn layui-btn-primary");
          groups_check = {};
          $("#inputaaa").attr("value", "");
        }
        form.render("checkbox");
      });
      function btn_renWu(data, type, savedFormData) {
        console.log("提交任务发送的消息：", data);

        let taskName = data.action;
        console.log("任务名称", taskName);

        // 汇总所有设备
        let groupDocArr = [];
        // let socketIdArr = [];
        // for (let groupName in groups_check) {
        //   let groupValue = groups_check[groupName];
        //   if (groupValue) {
        //     if (typeof groupValue == "string") {
        //       groupDocArr.push({ groupName: groupName, groupId: groupValue });
        //     } else {
        //       //是数组
        //       for (let i in groupValue) {
        //         if (!socketIdArr.includes(groupValue[i])) {
        //           socketIdArr.push(groupValue[i]);
        //         }
        //       }
        //     }
        //   }
        // }
        // if (groupDocArr.length == 0 && socketIdArr.length == 0) {
        //   layer.msg("没选择任何设备");
        //   return false;
        // }
        let taskData = data;
        // for (let oneData in taskData) {
        //     if (taskData[oneData].indexOf("◉") > -1) {
        //         delete taskData[oneData];
        //     }
        // }
        if (taskName == "发消息任务") {
          let aim = taskData.url地址;
          // layer.msg(aim);
          if (!aim) {
            layer.msg("请输入url地址");
            return false;
          }
          let keyword = taskData.发送消息;
          if (!keyword) {
            layer.msg("请输入要发送消息的内容");
            return false;
          }
        }
        storage.setItem(htmlName, JSON.stringify(taskData));
        layer.confirm(`执行[${data.task_name}]的任务??`, function (index) {
          layer.close(index);
          let newData = {
            groupDocArr: groupDocArr,
            socketIdArr: data.device_code,
            taskName: taskName,
            timeStamp: Date.now(),
            taskData: taskData,
            userName: storage.getItem("userName"),
          };
          console.log(newData);

          axios
            .post("/wsRouter/faTast", { type: "任务", data: newData })
            .then((res) => {
              layer.msg(res.data.msg);
            })
            .catch((e) => {
              layer.alert(e.message);
            });
        });
      }
      function addInput(groupDoc) {
        let button = document.createElement("input");
        button.type = "button";
        button.value = "◉" + groupDoc.groupName;
        button.name = groupDoc.groupName;
        button.className = "layui-btn layui-btn-primary";
        if (groupDoc.groupName == "空闲列表") {
          button.style.color = "#00CCFF"; // 设置按钮名称的颜色为红色
        } else if (groupDoc.groupName == "忙碌列表") {
          button.style.color = "#FFCC00"; // 设置按钮名称的颜色为黄色
        } else if (groupDoc.groupName == "全部列表") {
          button.style.color = "#F00"; // 设置按钮名称的颜色为红色
        }
        button.onclick = function () {
          //单击
          if (
            this.name == "空闲列表" ||
            this.name == "忙碌列表" ||
            this.name == "全部列表"
          ) {
            layer.msg("空闲/忙碌/全部列表不支持全选,请双击进入");
            return;
          }
          if (click_store != null) {
            clearTimeout(click_store);
            click_store = null;
          }
          click_store = setTimeout(function () {
            let dom = $('input[name="' + groupDoc.groupName + '"]');
            let domClass = dom.attr("class");
            if (domClass == "layui-btn") {
              dom.attr("class", "layui-btn layui-btn-primary");
              groups_check[groupDoc.groupName] = null;
              $("#inputaaa").attr("value", JSON.stringify(groups_check));
            } else if (domClass == "layui-btn layui-btn-primary") {
              dom.attr("class", "layui-btn");
              groups_check[groupDoc.groupName] = groupDoc._id;
              $("#inputaaa").attr("value", JSON.stringify(groups_check));
            }
          }, 300);
        };
        button.ondblclick = function () {
          //双击
          if (click_store != null) {
            clearTimeout(click_store);
            click_store = null;
          }
          let dom = $('input[name="' + groupDoc.groupName + '"]');
          let domClass = dom.attr("class");
          if (domClass == "layui-btn layui-btn-warm") {
            groups_check[groupDoc.groupName] = null;
            $("#inputaaa").attr("value", JSON.stringify(groups_check));
            dom.attr("class", "layui-btn layui-btn-primary");
          } else if (domClass == "layui-btn layui-btn-primary") {
            //展开分组下的设备列表
            window.groupObj = {
              groupName: groupDoc.groupName,
              _id: groupDoc._id,
            };
            // console.log(group._id);
            layer.open({
              title: "【" + groupDoc.groupName + "】下面的设备列表",
              type: 2,
              area: ["80%", "98%"],
              content: "/page/devices/tableGroup_deviceList.html",
              closeBtn: 1,
              shadeClose: true, //其他区域关闭
              btn: ["确认", "取消"],
              yes: function (index, layero) {
                let iframeWin = $(layero).find("iframe")[0].contentWindow;
                let socketIdArr = iframeWin.formData();
                if (socketIdArr) {
                  dom.attr("class", "layui-btn layui-btn-warm");
                  groups_check[groupDoc.groupName] = socketIdArr;
                  $("#inputaaa").attr("value", JSON.stringify(groups_check));
                  layer.close(index);
                }
              },
            });
          }
        };
        document.getElementById("groups1").appendChild(button);
      }

      // 监听 "添加任务" 按钮
      $(document).on("click", "#addTaskBtn", function () {
        layer.open({
          type: 1,
          title: "添加任务",
          area: ["60%", "98%"],
          offset: "10px",
          content: `
                    <form class="layui-form" id="taskForm" style="padding: 20px;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">任务类型</label>
                            <div class="layui-input-block">
                                <select name="selectTask" lay-filter="selectTask">
                                    <option value="">请选择任务</option>
                                    <option value="关注任务">关注任务</option>
                                    <option value="养号任务">养号任务</option>
                                </select>
                            </div>
                        </div>
                        <div id="dynamicContent"></div> <!-- 这里是动态内容区域 -->
                        <div class="layui-form-item"style="margin-left:63%">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="confirmAddTask">确认</button>
                                <button type="button" class="layui-btn layui-btn-primary" id="cancelAddTask">取消</button>
                            </div>
                        </div>
                    </form>
                    `,
          success: function (layero, index) {
            var form = layui.form;
            form.render(); // 重新渲染 select 组件

            // 初始化变量
            let keywordIndex = 0; // 关键词索引
            let keywordsArrayForTask = []; // 关注任务关键词数组
            let keywordsArrayForNurture = []; // 养号任务关键词数组
            let commentIndex = 0; // 评论索引
            let commentsArray = []; // 存储评论内容
            let selectedValue = ""; // 任务类型选择值

            // 移除之前的事件监听器
            document
              .getElementById("dynamicContent")
              .removeEventListener("click", addKeywordHandler);
            document
              .getElementById("dynamicContent")
              .removeEventListener("click", removeKeywordHandler);
            document
              .getElementById("dynamicContent")
              .removeEventListener("click", addCommentHandler);
            document
              .getElementById("dynamicContent")
              .removeEventListener("click", removeCommentHandler);

            // 定义事件处理函数
            function addKeywordHandler(event) {
              if (event.target && event.target.id === "addKeywordBtn") {
                var newKeywordInput = `
                <div id="keyword_${keywordIndex}" style="display: flex; align-items: center; gap: 10px;">
                    <input type="text" name="keyword" placeholder="请输入关键词" class="layui-input" id="input_keyword_${keywordIndex}" style="width: 200px;">
                    <button type="button" class="layui-btn layui-btn-danger remove-keyword" data-index="${keywordIndex}">删除</button>
                </div>
            `;
                document
                  .getElementById("keywordsContainer")
                  .insertAdjacentHTML("beforeend", newKeywordInput);
                keywordIndex++;
                if (selectedValue === "关注任务") {
                  keywordsArrayForTask.push(""); // 存储关注任务关键词
                } else if (selectedValue === "养号任务") {
                  keywordsArrayForNurture.push(""); // 存储养号任务关键词
                }
                form.render();
              }
            }

            function removeKeywordHandler(event) {
              if (
                event.target &&
                event.target.classList.contains("remove-keyword")
              ) {
                var index = event.target.getAttribute("data-index");
                var keywordElement = document.getElementById(
                  "keyword_" + index
                );
                if (keywordElement) {
                  keywordElement.remove();
                }
                if (selectedValue === "关注任务") {
                  keywordsArrayForTask.splice(index, 1); // 从关注任务关键词数组中删除
                } else if (selectedValue === "养号任务") {
                  keywordsArrayForNurture.splice(index, 1); // 从养号任务关键词数组中删除
                }
                form.render();
              }
            }

            function addCommentHandler(event) {
              if (event.target && event.target.id === "addCommentBtn") {
                var newCommentInput = `
                <div id="comment_${commentIndex}" style="display: flex; align-items: center; gap: 10px;">
                    <input type="text" name="comment" placeholder="请输入评论内容" class="layui-input" id="input_comment_${commentIndex}" style="width: 200px;">
                    <button type="button" class="layui-btn layui-btn-danger remove-comment" data-index="${commentIndex}">删除</button>
                </div>
            `;
                document
                  .getElementById("commentsContainer")
                  .insertAdjacentHTML("beforeend", newCommentInput);
                commentIndex++;
                commentsArray.push(""); // 存储评论内容
              }
            }

            function removeCommentHandler(event) {
              if (
                event.target &&
                event.target.classList.contains("remove-comment")
              ) {
                var index = event.target.getAttribute("data-index");
                var commentElement = document.getElementById(
                  "comment_" + index
                );
                if (commentElement) {
                  commentElement.remove();
                }
                commentsArray.splice(index, 1);
              }
            }

            // 监听任务类型选择
            form.on("select(selectTask)", function (data) {
              selectedValue = data.value; // 更新任务类型选择值
              var dynamicContent = document.getElementById("dynamicContent");

              if (
                selectedValue === "关注任务" ||
                selectedValue === "养号任务"
              ) {
                // **基础结构：关键词筛选**
                let dynamicHtml = `
        <div class="layui-form-item">
            <label class="layui-form-label">关键词筛选</label>
            <div id="keywordsContainer" style="display: flex; flex-direction: column; gap: 10px;">
                <button type="button" class="layui-btn" id="addKeywordBtn" style="background-color: #009688; color: white;">添加关键词</button>
            </div>
        </div>
    `;

                // **"关注任务" 的内容**
                if (selectedValue === "关注任务") {
                  dynamicHtml += `
            <div class="layui-form-item">
                <label class="layui-form-label">选择省份</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="province" value="北京" title="北京" lay-skin="primary" id="provinceBeijing">
                    <input type="checkbox" name="province" value="天津" title="天津" lay-skin="primary" id="provinceTianjin">
                    <input type="checkbox" name="province" value="上海" title="上海" lay-skin="primary" id="provinceShanghai">
                    <input type="checkbox" name="province" value="重庆" title="重庆" lay-skin="primary" id="provinceChongqing">
                    <input type="checkbox" name="province" value="河北" title="河北" lay-skin="primary" id="provinceHebei">
                    <input type="checkbox" name="province" value="河南" title="河南" lay-skin="primary" id="provinceHenan">
                    <input type="checkbox" name="province" value="云南" title="云南" lay-skin="primary" id="provinceYunnan">
                    <input type="checkbox" name="province" value="辽宁" title="辽宁" lay-skin="primary" id="provinceLiaoning">
                    <input type="checkbox" name="province" value="黑龙江" title="黑龙江" lay-skin="primary" id="provinceHeilongjiang">
                    <input type="checkbox" name="province" value="湖南" title="湖南" lay-skin="primary" id="provinceHunan">
                    <input type="checkbox" name="province" value="安徽" title="安徽" lay-skin="primary" id="provinceAnhui">
                    <input type="checkbox" name="province" value="山东" title="山东" lay-skin="primary" id="provinceShandong">
                    <input type="checkbox" name="province" value="新疆" title="新疆" lay-skin="primary" id="provinceXinjiang">
                    <input type="checkbox" name="province" value="江苏" title="江苏" lay-skin="primary" id="provinceJiangsu">
                    <input type="checkbox" name="province" value="浙江" title="浙江" lay-skin="primary" id="provinceZhejiang">
                    <input type="checkbox" name="province" value="江西" title="江西" lay-skin="primary" id="provinceJiangxi">
                    <input type="checkbox" name="province" value="湖北" title="湖北" lay-skin="primary" id="provinceHubei">
                    <input type="checkbox" name="province" value="广西" title="广西" lay-skin="primary" id="provinceGuangxi">
                    <input type="checkbox" name="province" value="甘肃" title="甘肃" lay-skin="primary" id="provinceGansu">
                    <input type="checkbox" name="province" value="山西" title="山西" lay-skin="primary" id="provinceShanxi">
                    <input type="checkbox" name="province" value="内蒙古" title="内蒙古" lay-skin="primary" id="provinceNeimenggu">
                    <input type="checkbox" name="province" value="陕西" title="陕西" lay-skin="primary" id="provinceShaanxi">
                    <input type="checkbox" name="province" value="吉林" title="吉林" lay-skin="primary" id="provinceJilin">
                    <input type="checkbox" name="province" value="福建" title="福建" lay-skin="primary" id="provinceFujian">
                    <input type="checkbox" name="province" value="贵州" title="贵州" lay-skin="primary" id="provinceGuizhou">
                    <input type="checkbox" name="province" value="四川" title="四川" lay-skin="primary" id="provinceSichuan">
                    <input type="checkbox" name="province" value="海南" title="海南" lay-skin="primary" id="provinceHainan">
                    <input type="checkbox" name="province" value="台湾" title="台湾" lay-skin="primary" id="provinceTaiwan">
                    <input type="checkbox" name="province" value="香港" title="香港" lay-skin="primary" id="provinceHongkong">
                    <input type="checkbox" name="province" value="澳门" title="澳门" lay-skin="primary" id="provinceMacau">
                    <input type="checkbox" name="province" value="广东" title="广东" lay-skin="primary" id="provincGuangdong">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">已选择省份</label>
                <div class="layui-input-block">
                    <input type="text" id="provinceDisplay" class="layui-input" readonly placeholder="请选择省份">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">最大关注数</label>
                <div class="layui-input-block">
                    <input type="number" id="maxFollowCount" class="layui-input" placeholder="请输入最大关注个数">
                </div>
            </div>
        `;
                }

                // **"养号任务" 的内容**
                if (selectedValue === "养号任务") {
                  dynamicHtml += `
            <div class="layui-form-item">
                <label class="layui-form-label">养号时长</label>
                <div class="layui-input-block">
                    <input type="number" id="nurtureDuration" class="layui-input" placeholder="请输入养号时长（分钟）">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">点赞概率%</label>
                <div class="layui-input-block">
                    <input type="number" id="likeProbability" class="layui-input" placeholder="请输入点赞概率（0-100）">
                </div>
            </div>

            <!-- 评论概率 -->
            <div class="layui-form-item">
                <label class="layui-form-label">评论概率%</label>
                <div class="layui-input-block">
                    <input type="number" id="commentProbability" class="layui-input" placeholder="请输入评论概率（0-100）">
                </div>
            </div>

            <!-- 评论内容管理 -->
            <div class="layui-form-item">
                <label class="layui-form-label">评论内容</label>
                <div id="commentsContainer" style="display: flex; flex-direction: column; gap: 10px;">
                    <button type="button" class="layui-btn" id="addCommentBtn" style="background-color: #009688; color: white;">添加评论</button>
                </div>
            </div>
        `;
                }

                // **渲染动态内容**
                dynamicContent.innerHTML = dynamicHtml;

                // **绑定事件监听器**
                document
                  .getElementById("dynamicContent")
                  .addEventListener("click", addKeywordHandler);
                document
                  .getElementById("dynamicContent")
                  .addEventListener("click", removeKeywordHandler);
                document
                  .getElementById("dynamicContent")
                  .addEventListener("click", addCommentHandler);
                document
                  .getElementById("dynamicContent")
                  .addEventListener("click", removeCommentHandler);

                // **监听省份多选（仅在 "关注任务" 里执行）**
                if (selectedValue === "关注任务") {
                  form.on("checkbox", function () {
                    var selectedProvinces = [];
                    var checkboxes = document.querySelectorAll(
                      'input[name="province"]:checked'
                    );

                    checkboxes.forEach(function (checkbox) {
                      selectedProvinces.push(checkbox.value);
                    });

                    document.getElementById("provinceDisplay").value =
                      selectedProvinces.join(", ");
                  });
                }

                // **获取养号时长和点赞概率（仅在 "养号任务" 里执行）**
                if (selectedValue === "养号任务") {
                  document
                    .getElementById("nurtureDuration")
                    .addEventListener("input", function () {
                      // console.log("养号时长:", this.value);
                    });
                  document
                    .getElementById("likeProbability")
                    .addEventListener("input", function () {
                      // console.log("点赞概率:", (this.value / 100).toFixed(2));
                    });
                }
              } else {
                // **清空动态内容**
                dynamicContent.innerHTML = "";

                // 重置索引和数组
                keywordIndex = 0;
                keywordsArrayForTask = [];
                keywordsArrayForNurture = [];
                commentIndex = 0;
                commentsArray = [];
              }

              form.render(); // 重新渲染表单
            });

            // 监听取消按钮
            document
              .getElementById("cancelAddTask")
              .addEventListener("click", function () {
                layer.close(index); // 关闭弹出层
              });
          },
        });
      });
      // 表单提交
      form.on("submit(confirmAddTask)", function (data) {
        var selectedTask = data.field.selectTask;
        var taskData = {}; // 存储表单数据对象
        taskData.selectedTask = selectedTask; // 选择的任务

        // **获取关键词输入框的值**
        var keywordsArray = [];
        var keywordInputs = document.querySelectorAll('[id^="input_keyword_"]');
        keywordInputs.forEach(function (input) {
          if (input.value.trim() !== "") {
            keywordsArray.push(input.value.trim());
          }
        });
        taskData.keywords = keywordsArray; // 存储关键词数组

        if (selectedTask === "关注任务") {
          // **获取已选省份**
          var selectedProvinces = [];
          var checkboxes = document.querySelectorAll(
            'input[name="province"]:checked'
          );
          checkboxes.forEach(function (checkbox) {
            selectedProvinces.push(checkbox.value);
          });
          taskData.province = selectedProvinces; // 存储省份数组

          // **获取最大关注数**
          var maxFollowCountInput = document.getElementById("maxFollowCount");
          taskData.max_followers = maxFollowCountInput
            ? parseInt(maxFollowCountInput.value) || 0
            : 0;
        } else if (selectedTask === "养号任务") {
          // **获取养号时长**
          var nurtureDurationInput = document.getElementById("nurtureDuration");
          taskData.account_duration = nurtureDurationInput
            ? parseInt(nurtureDurationInput.value) || 0
            : 0;

          // **获取点赞率 (转换为 0.xx 小数)**
          var likeProbabilityInput = document.getElementById("likeProbability");
          let likeProbabilityValue = likeProbabilityInput
            ? parseFloat(likeProbabilityInput.value) || 0
            : 0;

          // 如果用户输入的是 0.7 这种小数，直接使用
          taskData.like_rate =
            likeProbabilityValue <= 1
              ? likeProbabilityValue
              : (likeProbabilityValue / 100).toFixed(2);

          // 获取评论率
          var commentProbabilityInput =
            document.getElementById("commentProbability");
          let commentProbabilityValue = commentProbabilityInput
            ? parseFloat(commentProbabilityInput.value) || 0
            : 0;

          taskData.comment_rate =
            commentProbabilityValue <= 1
              ? commentProbabilityValue
              : (commentProbabilityValue / 100).toFixed(2);

          // **获取评论内容**
          var commentsArray = [];
          var commentInputs = document.querySelectorAll(
            '[id^="input_comment_"]'
          );
          commentInputs.forEach(function (input) {
            if (input.value.trim() !== "") {
              commentsArray.push(input.value.trim());
            }
          });
          taskData.comment_content = commentsArray; // 存储评论内容数组
        }

        // 获取设备编码 (socketId)
        let groupDocArr = [];
        let socketIdArr = [];
        for (let groupName in groups_check) {
          let groupValue = groups_check[groupName];
          if (groupValue) {
            if (typeof groupValue === "string") {
              groupDocArr.push({ groupName: groupName, groupId: groupValue });
            } else {
              for (let i in groupValue) {
                if (!socketIdArr.includes(groupValue[i])) {
                  socketIdArr.push(groupValue[i]);
                }
              }
            }
          }
        }
        if (groupDocArr.length === 0 && socketIdArr.length === 0) {
          layer.msg("没选择任何设备");
          return false;
        }
        // **组合数据**
        const finalTaskData = {
          device_code: socketIdArr,
          task_name: selectedTask || "",
          keywords: taskData.keywords || [],
          province: taskData.province || [],
          max_followers: taskData.max_followers || 0,
          account_duration: taskData.account_duration || 0,
          like_rate: taskData.like_rate || 0,
          comment_rate: taskData.comment_rate || 0,
          comment_content: taskData.comment_content || [],
          device_status: "空闲", // 默认设备状态为空
        };

        axios
          .post("/dyTask/addTask", finalTaskData)
          .then((res) => {
            console.log("任务添加成功:", res.data);
            layer.msg("任务添加成功");
          })
          .catch((err) => {
            console.error("任务添加失败:", err);
            layer.msg("任务添加失败");
          });
        $.ajax({
          url: "/dyTask/getTasks", // 获取所有任务的接口
          type: "GET", // 请求类型
          success: function (res) {
            if (res.code === 0) {
              // 成功获取到数据，重新加载表格
              table.reload("taskTable", {
                data: res.data, // 将获取到的数据传递给表格进行重新渲染
              });
              layer.msg("数据库刷新成功"); // 提示用户数据库刷新成功
            } else {
              layer.msg("获取任务数据失败");
            }
          },
          error: function (err) {
            console.error("请求失败", err);
            layer.msg("刷新数据库失败");
          },
        });
        console.log("最终任务数据:", finalTaskData);

        layer.closeAll();
        return false; // 阻止表单跳转
      });

      // 原始的开始任务和结束任务
      // document
      //   .querySelector('button[lay-filter="tijiao"]')
      //   .addEventListener("click", function (event) {
      //     var buttonValue = event.target.value;
      //     globalTaskData.buttonValue = buttonValue;
      //     btn_renWu(globalTaskData);
      //   });

      // 表格
      // 渲染任务表格
      table.render({
        elem: "#taskTable",
        url: "/dyTask/getTasks", //  直接调用接口
        toolbar: "#toolbar",
        cols: [
          [
            { field: "task_id", title: "任务ID", sort: true },
            { field: "task_name", title: "任务名称" },
            { field: "device_code", title: "设备编码" },
            {
              field: "created_at",
              title: "创建时间",
              sort: true,
              templet: function (d) {
                return new Date(d.created_at).toLocaleString(); // 格式化时间
              }
            },
            { title: "操作", toolbar: "#toolbar" }
          ]
        ],
        page: {
          curr: 1,
          limit: 11,
          limits: [11, 19, 29, 49, 99],
          groups: 5,
          layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'],
          theme: '#1E9FFF'
        }
      });       
      // 监听工具栏事件
      table.on("tool(taskTable)", function (obj) {
        let data = obj.data;
        const event = obj.event;

        // 解析 JSON 格式的数据
        try {
          data.device_code = JSON.parse(data.device_code);
          data.keywords = JSON.parse(data.keywords);
          data.province = JSON.parse(data.province);
          data.comment_content = JSON.parse(data.comment_content);
        } catch (e) {
          console.error("JSON解析失败:", e);
        }

        // 添加 task_status 状态字段
        if (event === "startTask") {
          data.action = "开始任务"; // 将按钮值（开始任务）写入 data
          layer.msg("开始任务: " + data.task_name);
          console.log("开始任务数据:", data);
          btn_renWu(data);
        } else if (event === "endTask") {
          data.action = "结束任务"; // 将按钮值（结束任务）写入 data
          layer.msg("结束任务: " + data.task_name);
          console.log("结束任务数据:", data);
          btn_renWu(data);
        } else if (event === "deleteTask") {
          layer.confirm("确认删除该任务吗？", function (index) {
            $.ajax({
              url: `/dyTask/deleteTask/${data.task_id}`,
              type: "DELETE",
              success: function (res) {
                if (res.message === "任务删除成功") {
                  layer.msg("删除成功");
                  table.reload("taskTable"); // 重新加载表格
                } else {
                  layer.msg("删除失败");
                }
              },
            });
            layer.close(index);
          });
        }
      });
    },
  };
  exports("dyTask", dyTask);
});
