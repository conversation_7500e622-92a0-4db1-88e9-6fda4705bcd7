const mysql = require('mysql2/promise');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4"
};

async function quickTestPromotion() {
  console.log('🧪 快速测试推广用户管理功能...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查推广用户数据
    console.log('\n📋 1. 检查推广用户数据:');
    const [promoters] = await connection.execute(
      `SELECT promoter_id as user_id, username, promotion_link, status, created_at, updated_at 
       FROM user 
       WHERE user_type = 'promoter' 
       ORDER BY created_at DESC`
    );
    
    console.log(`找到 ${promoters.length} 个推广用户:`);
    promoters.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.user_id}, 用户名: ${user.username}, 状态: ${user.status ? '启用' : '禁用'}`);
      console.log(`   推广链接: ${user.promotion_link}`);
      console.log(`   创建时间: ${user.created_at}`);
    });
    
    // 2. 如果没有数据，创建一些测试数据
    if (promoters.length === 0) {
      console.log('\n➕ 没有推广用户数据，创建测试数据...');
      
      const testUsers = [
        {
          username: 'promoter1001',
          password: '123456',
          promoter_id: '1001',
          promotion_link: 'http://localhost:15001?id=1001'
        },
        {
          username: 'promoter1002',
          password: '123456',
          promoter_id: '1002',
          promotion_link: 'http://localhost:15001?id=1002'
        },
        {
          username: 'promoter1003',
          password: '123456',
          promoter_id: '1003',
          promotion_link: 'http://localhost:15001?id=1003'
        }
      ];
      
      for (const userData of testUsers) {
        try {
          await connection.execute(
            `INSERT INTO user (username, password, user_type, promoter_id, promotion_link, status, ckcount)
             VALUES (?, ?, 'promoter', ?, ?, 1, 0)`,
            [userData.username, userData.password, userData.promoter_id, userData.promotion_link]
          );
          console.log(`✅ 创建测试用户: ${userData.username} (ID: ${userData.promoter_id})`);
        } catch (error) {
          if (error.code === 'ER_DUP_ENTRY') {
            console.log(`⚠️  用户已存在: ${userData.username}`);
          } else {
            console.error(`❌ 创建用户失败: ${userData.username}`, error.message);
          }
        }
      }
      
      // 重新查询数据
      const [newPromoters] = await connection.execute(
        `SELECT promoter_id as user_id, username, promotion_link, status, created_at 
         FROM user 
         WHERE user_type = 'promoter' 
         ORDER BY created_at DESC`
      );
      
      console.log(`\n✅ 现在有 ${newPromoters.length} 个推广用户`);
    }
    
    // 3. 测试API格式
    console.log('\n📡 3. 测试API返回格式:');
    const [apiData] = await connection.execute(
      `SELECT promoter_id as user_id, username, promotion_link, status, created_at, updated_at 
       FROM user 
       WHERE user_type = 'promoter' 
       ORDER BY created_at DESC 
       LIMIT 3`
    );
    
    const apiResponse = {
      code: 0,
      msg: '获取成功',
      data: {
        list: apiData,
        total: apiData.length,
        page: 1,
        limit: 10
      }
    };
    
    console.log('API响应格式预览:');
    console.log(JSON.stringify(apiResponse, null, 2));
    
    await connection.end();
    
    console.log('\n🎉 快速测试完成！');
    console.log('\n📋 测试结果:');
    console.log('✅ 数据库连接正常');
    console.log('✅ 推广用户数据存在');
    console.log('✅ API格式正确');
    
    console.log('\n🔗 现在可以访问以下页面测试:');
    console.log('- 简化版推广管理: http://localhost:15001/page/promotion/admin-promotion-simple.html');
    console.log('- 调试页面: http://localhost:15001/debug-promotion.html');
    console.log('- 推广用户登录: http://localhost:15001/page/promotion/promoter-login.html');
    
    console.log('\n🔑 可用的推广用户账户:');
    apiData.forEach(user => {
      console.log(`- 用户ID: ${user.user_id}, 密码: 123456, 用户名: ${user.username}`);
    });
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    process.exit(1);
  }
}

quickTestPromotion();
