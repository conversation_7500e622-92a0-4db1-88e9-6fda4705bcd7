const axios = require('axios');

// 简化的推广用户测试
async function testPromoterSimple() {
  console.log('🧪 简化的推广用户登录测试...');
  
  const baseURL = 'http://localhost:15001';
  
  // 测试用户
  const testUser = { user_id: '1001', password: '123456', username: 'promoter1001' };
  
  console.log(`\n🔍 测试用户: ${testUser.username} (ID: ${testUser.user_id})`);
  console.log('─'.repeat(60));
  
  try {
    // 1. 测试登录
    console.log('1️⃣ 测试登录...');
    const loginResponse = await axios.post(`${baseURL}/api/promotion/promoter/login`, {
      user_id: testUser.user_id,
      password: testUser.password
    });
    
    console.log('登录响应:', loginResponse.data);
    
    if (loginResponse.data.code === 0) {
      console.log(`   ✅ 登录成功: ${loginResponse.data.msg}`);
      
      // 获取session cookie
      const cookies = loginResponse.headers['set-cookie'];
      console.log('收到的cookies:', cookies);
      
      if (cookies && cookies.length > 0) {
        const sessionCookie = cookies.find(cookie => cookie.includes('connect.sid'));
        console.log('Session cookie:', sessionCookie);
        
        if (sessionCookie) {
          // 2. 测试获取用户信息
          console.log('\n2️⃣ 测试获取用户信息...');
          const userInfoResponse = await axios.get(`${baseURL}/api/promotion/promoter/user-info`, {
            headers: {
              'Cookie': sessionCookie
            }
          });
          
          console.log('用户信息响应:', userInfoResponse.data);
          
          if (userInfoResponse.data.code === 0) {
            const userInfo = userInfoResponse.data.data;
            console.log(`   ✅ 用户信息获取成功:`);
            console.log(`      用户ID: ${userInfo.user_id}`);
            console.log(`      用户名: ${userInfo.username}`);
            
            // 3. 测试获取今日统计数据
            console.log('\n3️⃣ 测试获取今日统计数据...');
            const statsResponse = await axios.get(`${baseURL}/api/promotion/promoter/today-stats`, {
              headers: {
                'Cookie': sessionCookie
              }
            });
            
            console.log('统计数据响应:', statsResponse.data);
            
            if (statsResponse.data.code === 0) {
              const stats = statsResponse.data.data;
              console.log(`   ✅ 今日统计数据获取成功:`);
              console.log(`      访问次数: ${stats.visit_count}`);
              console.log(`      独立IP数: ${stats.unique_ip_count}`);
              console.log(`      扫码数量: ${stats.scan_count}`);
              console.log(`      成功数量: ${stats.success_count}`);
              console.log(`      失败数量: ${stats.fail_count}`);
              console.log(`      过期数量: ${stats.expire_count}`);
              
              // 4. 测试获取今日操作记录
              console.log('\n4️⃣ 测试获取今日操作记录...');
              const actionsResponse = await axios.get(`${baseURL}/api/promotion/promoter/today-actions?page=1&limit=5`, {
                headers: {
                  'Cookie': sessionCookie
                }
              });
              
              console.log('操作记录响应:', actionsResponse.data);
              
              if (actionsResponse.data.code === 0) {
                const actions = actionsResponse.data.data;
                console.log(`   ✅ 今日操作记录获取成功:`);
                console.log(`      总记录数: ${actions.total}`);
                console.log(`      当前页记录数: ${actions.list.length}`);
                
                if (actions.list.length > 0) {
                  console.log(`      最近操作记录:`);
                  actions.list.slice(0, 3).forEach((action, index) => {
                    console.log(`        ${index + 1}. 用户: ${action.user_id}, 时间: ${action.time}, IP: ${action.ip}, 状态: ${action.status}`);
                  });
                } else {
                  console.log(`      暂无操作记录`);
                }
              } else {
                console.log(`   ❌ 今日操作记录获取失败: ${actionsResponse.data.msg}`);
              }
              
            } else {
              console.log(`   ❌ 今日统计数据获取失败: ${statsResponse.data.msg}`);
            }
            
          } else {
            console.log(`   ❌ 用户信息获取失败: ${userInfoResponse.data.msg}`);
          }
          
        } else {
          console.log('   ❌ 未找到session cookie');
        }
      } else {
        console.log('   ❌ 登录响应中没有cookies');
      }
      
    } else {
      console.log(`   ❌ 登录失败: ${loginResponse.data.msg}`);
    }
    
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`);
    if (error.response) {
      console.log(`      响应状态: ${error.response.status}`);
      console.log(`      响应数据:`, error.response.data);
    }
  }
  
  console.log('\n🎉 推广用户登录测试完成！');
  
  console.log('\n💡 测试总结:');
  console.log('1. 推广用户登录功能');
  console.log('2. Session cookie处理');
  console.log('3. 用户信息获取功能');
  console.log('4. 今日统计数据获取功能（从promotion_daily_stats和promotion_visits表）');
  console.log('5. 今日操作记录获取功能（从promotion_actions表）');
  
  console.log('\n🔧 数据查询逻辑验证:');
  console.log('- 使用登录用户的user_id作为promotion_user_id查询对应数据');
  console.log('- promotion_daily_stats表: 优先查询今日统计数据');
  console.log('- promotion_visits表: 实时计算访问统计（如果daily_stats无数据）');
  console.log('- promotion_actions表: 实时计算操作统计和获取操作记录');
  
  console.log('\n🌐 前端界面测试:');
  console.log('- 访问 http://localhost:15001/page/promotion/promoter-login.html 进行登录');
  console.log('- 登录后自动跳转到 http://localhost:15001/page/promotion/promoter-dashboard.html');
  console.log('- 界面显示当前登录用户的专属数据');
}

// 运行测试
if (require.main === module) {
  testPromoterSimple().catch(console.error);
}

module.exports = testPromoterSimple;
