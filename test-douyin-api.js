const mysql = require("mysql2/promise");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  charset: "utf8mb4",
  connectionLimit: 10,
  waitForConnections: true,
  queueLimit: 0,
};

async function testDatabase() {
  try {
    console.log("开始测试数据库连接...");

    // 先创建一个不指定数据库的连接来创建数据库
    const tempConfig = { ...dbConfig };
    const tempPool = mysql.createPool(tempConfig);

    const connection = await tempPool.getConnection();

    // 创建数据库
    await connection.query(
      `CREATE DATABASE IF NOT EXISTS douyin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`
    );
    console.log("✓ 数据库 douyin 已创建或已存在");

    connection.release();
    await tempPool.end();

    // 现在使用指定数据库的连接
    const dbConfigWithDB = { ...dbConfig, database: "douyin" };
    const pool = mysql.createPool(dbConfigWithDB);

    const dbConnection = await pool.getConnection();

    // 创建user表
    await dbConnection.query(`
      CREATE TABLE IF NOT EXISTS user (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        status TINYINT DEFAULT 1 COMMENT '1=active, 0=inactive, 2=disabled',
        ckcount INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log("✓ user表已创建或已存在");

    // 插入测试数据
    try {
      await dbConnection.query(
        `
        INSERT INTO user (username, password, status, ckcount)
        VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE ckcount = VALUES(ckcount)
      `,
        ["testuser1", "password123", 1, 5]
      );
      console.log("✓ 测试数据已插入");
    } catch (err) {
      if (err.code !== "ER_DUP_ENTRY") {
        throw err;
      }
      console.log("✓ 测试数据已存在");
    }

    // 查询数据
    const [rows] = await dbConnection.query("SELECT * FROM user LIMIT 5");
    console.log("✓ 查询成功，数据条数:", rows.length);
    console.log("数据示例:", rows);

    dbConnection.release();
    await pool.end();

    console.log("✓ 数据库测试完成");
  } catch (err) {
    console.error("✗ 数据库测试失败:", err.message);
  }
}

// 运行测试
testDatabase();
