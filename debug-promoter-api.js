const axios = require('axios');

// 调试推广用户API接口
async function debugPromoterAPI() {
  console.log('🔍 调试推广用户API接口...');
  
  const baseURL = 'http://localhost:15001';
  
  // 测试用户
  const testUser = { user_id: '1001', password: '123456' };
  
  console.log(`\n📋 测试用户: ID=${testUser.user_id}, 密码=${testUser.password}`);
  console.log('─'.repeat(60));
  
  try {
    // 1. 测试登录API
    console.log('1️⃣ 测试登录API...');
    const loginResponse = await axios.post(`${baseURL}/api/promotion/promoter/login`, testUser);
    
    console.log('✅ 登录API响应:');
    console.log('   状态码:', loginResponse.status);
    console.log('   响应数据:', JSON.stringify(loginResponse.data, null, 2));
    console.log('   响应头:', loginResponse.headers);
    
    if (loginResponse.data.code === 0) {
      // 获取session cookie
      const cookies = loginResponse.headers['set-cookie'];
      console.log('   收到的cookies:', cookies);
      
      if (cookies && cookies.length > 0) {
        const sessionCookie = cookies.find(cookie => cookie.includes('connect.sid'));
        
        if (sessionCookie) {
          console.log('   Session cookie:', sessionCookie);
          
          // 2. 测试用户信息API
          console.log('\n2️⃣ 测试用户信息API...');
          try {
            const userInfoResponse = await axios.get(`${baseURL}/api/promotion/promoter/user-info`, {
              headers: { 'Cookie': sessionCookie }
            });
            
            console.log('✅ 用户信息API响应:');
            console.log('   状态码:', userInfoResponse.status);
            console.log('   响应数据:', JSON.stringify(userInfoResponse.data, null, 2));
            
          } catch (userInfoError) {
            console.log('❌ 用户信息API错误:');
            console.log('   状态码:', userInfoError.response?.status);
            console.log('   错误信息:', userInfoError.message);
            console.log('   响应数据:', userInfoError.response?.data);
          }
          
          // 3. 测试统计数据API
          console.log('\n3️⃣ 测试统计数据API...');
          try {
            const statsResponse = await axios.get(`${baseURL}/api/promotion/promoter/today-stats`, {
              headers: { 'Cookie': sessionCookie }
            });
            
            console.log('✅ 统计数据API响应:');
            console.log('   状态码:', statsResponse.status);
            console.log('   响应数据:', JSON.stringify(statsResponse.data, null, 2));
            
          } catch (statsError) {
            console.log('❌ 统计数据API错误:');
            console.log('   状态码:', statsError.response?.status);
            console.log('   错误信息:', statsError.message);
            console.log('   响应数据:', statsError.response?.data);
          }
          
          // 4. 测试操作记录API
          console.log('\n4️⃣ 测试操作记录API...');
          try {
            const actionsResponse = await axios.get(`${baseURL}/api/promotion/promoter/today-actions?page=1&limit=5`, {
              headers: { 'Cookie': sessionCookie }
            });
            
            console.log('✅ 操作记录API响应:');
            console.log('   状态码:', actionsResponse.status);
            console.log('   响应数据:', JSON.stringify(actionsResponse.data, null, 2));
            
          } catch (actionsError) {
            console.log('❌ 操作记录API错误:');
            console.log('   状态码:', actionsError.response?.status);
            console.log('   错误信息:', actionsError.message);
            console.log('   响应数据:', actionsError.response?.data);
          }
          
        } else {
          console.log('❌ 未找到session cookie');
        }
      } else {
        console.log('❌ 登录响应中没有cookies');
      }
    } else {
      console.log('❌ 登录失败:', loginResponse.data.msg);
    }
    
  } catch (error) {
    console.log('❌ 登录API错误:');
    console.log('   状态码:', error.response?.status);
    console.log('   错误信息:', error.message);
    console.log('   响应数据:', error.response?.data);
  }
  
  // 5. 测试不带认证的API调用（模拟前端直接调用）
  console.log('\n5️⃣ 测试不带认证的API调用（模拟前端问题）...');
  
  const apiEndpoints = [
    '/api/promotion/promoter/user-info',
    '/api/promotion/promoter/today-stats',
    '/api/promotion/promoter/today-actions'
  ];
  
  for (const endpoint of apiEndpoints) {
    try {
      console.log(`\n   测试: ${endpoint}`);
      const response = await axios.get(`${baseURL}${endpoint}`);
      console.log('   ✅ 成功:', response.data);
    } catch (error) {
      console.log('   ❌ 失败:');
      console.log('      状态码:', error.response?.status);
      console.log('      错误信息:', error.message);
      console.log('      响应数据:', error.response?.data);
    }
  }
  
  console.log('\n🎉 API调试完成！');
  
  console.log('\n💡 问题分析:');
  console.log('1. 检查登录API是否正常返回session cookie');
  console.log('2. 检查后续API调用是否正确传递cookie');
  console.log('3. 检查服务器端session处理是否正常');
  console.log('4. 检查API路由是否正确注册');
  
  console.log('\n🔧 可能的解决方案:');
  console.log('1. 确保前端正确处理和传递session cookie');
  console.log('2. 检查服务器端session配置');
  console.log('3. 添加更详细的错误处理和日志');
  console.log('4. 检查CORS配置是否影响cookie传递');
}

// 运行调试
if (require.main === module) {
  debugPromoterAPI().catch(console.error);
}

module.exports = debugPromoterAPI;
