<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                
                <fieldset>
                    <legend>【发消息】功能</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">商品</label>
                        <div class="layui-input-block">
                            <textarea id="productName" name="productName" placeholder="多个商品使用 | 分割" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-block">
                            <textarea id="keyword" name="keyword" placeholder="多个关键词使用 | 分割" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <button class="layui-btn layui-btn-sm" lay-submit="" value="商品浏览任务" lay-filter="tijiao">发消息任务</button>
                    <button class="layui-btn layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止</button>
                </fieldset>
            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });
        document.querySelector(".layui-btn[value='发消息任务']").addEventListener("click", function (event) {
    event.preventDefault();

    const productName = document.getElementById("productName").value;
    const keyword = document.getElementById("keyword").value;

    if (!productName.trim() || !keyword.trim()) {
        alert("请输入商品名称和关键词");
        return;
    }

    axios.post('http://**************:3101/saveData', { productName, keyword })
        .then(response => {
            console.log("成功")
            alert(response.data.message);
        })
        .catch(error => {
            console.error('数据存储失败:', error);
            alert('数据存储失败');
        });
});
    </script>
</body>

</html>
