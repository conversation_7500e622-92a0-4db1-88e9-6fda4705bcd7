// mysql接口文件
const express = require('express');
const router = express.Router();
const bodyParser = require('body-parser');
const axios = require('axios');
const mysql = require('../db/mysql.js');

router.use(bodyParser.urlencoded({ extended: true }));
router.use(bodyParser.json());

// 添加任务接口
router.post('/addTask', (req, res) => {
  const { device_code, task_name, keywords, province, max_followers, account_duration, like_rate, comment_rate, comment_content, device_status } = req.body;

  // 校验必填字段
  if (!device_code || !task_name) {
    return res.status(400).json({ error: '设备编码和任务名称为必填项' });
  }

  // 确保 device_code, keywords, province, comment_content 是数组
  if (!Array.isArray(device_code) || !Array.isArray(keywords) || !Array.isArray(province) || !Array.isArray(comment_content)) {
    return res.status(400).json({ error: '设备编码、关键词、省份和评论内容必须是数组' });
  }

  const query = `INSERT INTO tasks (device_code, task_name, keywords, province, max_followers, account_duration, like_rate, comment_rate, comment_content, device_status) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

  const values = [
    JSON.stringify(device_code),
    task_name,
    JSON.stringify(keywords),
    JSON.stringify(province),
    max_followers,
    account_duration,
    like_rate,
    comment_rate,
    JSON.stringify(comment_content),
    device_status
  ];

  mysql.query(query, values, (err, results) => {
    if (err) {
      console.error('数据库插入错误:', err);
      return res.status(500).json({ error: '数据库插入失败' });
    }

    res.status(201).json({ message: '任务添加成功', taskId: results.insertId });
  });
});

// 删除任务接口
router.delete('/deleteTask/:task_id', (req, res) => {
  const { task_id } = req.params;

  const query = 'DELETE FROM tasks WHERE task_id = ?';

  mysql.query(query, [task_id], (err, results) => {
    if (err) {
      console.error('删除任务出错:', err);
      return res.status(500).json({ error: '删除任务失败' });
    }

    if (results.affectedRows === 0) {
      return res.status(404).json({ error: '任务不存在' });
    }

    res.status(200).json({ message: '任务删除成功' });
  });
});

// 获取所有任务接口
router.get('/getTasks', (req, res) => {
  const page = parseInt(req.query.page) || 1;   // layui自动传的页码
  const limit = parseInt(req.query.limit) || 11;  //  layui自动传的每页数量

  const offset = (page - 1) * limit;  //  计算偏移量

  // 查询总条数
  const countQuery = 'SELECT COUNT(*) as total FROM tasks';

  // 查询分页数据
  const query = `SELECT * FROM tasks ORDER BY created_at DESC LIMIT ?, ?`;

  mysql.query(countQuery, (err, countResult) => {
    if (err) {
      console.error('获取任务总数出错:', err);
      return res.status(500).json({ code: 1, msg: '获取任务总数失败', data: [] });
    }

    const total = countResult[0].total;

    mysql.query(query, [offset, limit], (err, results) => {
      if (err) {
        console.error('获取任务出错:', err);
        return res.status(500).json({ code: 1, msg: '获取任务失败', data: [] });
      }

      res.status(200).json({
        code: 0,
        msg: '获取任务成功',
        count: total, // layui需要的 `count`
        data: results // layui需要的 `data`
      });
    });
  });
});
// 更新任务状态接口
router.put('/updateTaskStatus/:task_id', (req, res) => {
  const { task_id } = req.params;
  const { device_status } = req.body;

  if (!device_status) {
    return res.status(400).json({ error: '设备状态不能为空' });
  }

  const query = 'UPDATE tasks SET device_status = ? WHERE task_id = ?';

  mysql.query(query, [device_status, task_id], (err, results) => {
    if (err) {
      console.error('更新任务状态出错:', err);
      return res.status(500).json({ error: '更新任务状态失败' });
    }

    if (results.affectedRows === 0) {
      return res.status(404).json({ error: '任务不存在' });
    }

    res.status(200).json({ success: true, message: '任务状态更新成功' });
  });
});

module.exports = router;
