@echo off
chcp 65001 >nul
echo.
echo ========================================
echo          MySQL权限重置工具
echo ========================================
echo.

echo 🔧 正在检查MySQL服务状态...
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到MySQL服务
    echo 🛑 正在停止MySQL服务...
    net stop mysql
) else (
    sc query mysql80 >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ 找到MySQL80服务
        echo 🛑 正在停止MySQL80服务...
        net stop mysql80
    ) else (
        echo ❌ 未找到MySQL服务，请手动检查服务名称
        pause
        exit /b 1
    )
)

echo.
echo 📝 创建临时SQL脚本...
echo USE mysql; > reset_password.sql
echo UPDATE user SET authentication_string='' WHERE User='root'; >> reset_password.sql
echo UPDATE user SET plugin='mysql_native_password' WHERE User='root'; >> reset_password.sql
echo FLUSH PRIVILEGES; >> reset_password.sql

echo.
echo 🔄 以安全模式重置密码...
echo 请等待MySQL重置完成...

start /b mysqld --skip-grant-tables --skip-networking --init-file="%cd%\reset_password.sql"

timeout /t 5 /nobreak >nul

echo.
echo 🛑 停止安全模式MySQL...
taskkill /f /im mysqld.exe >nul 2>&1

echo.
echo 🚀 重新启动MySQL服务...
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    net start mysql
) else (
    net start mysql80
)

echo.
echo 🧹 清理临时文件...
del reset_password.sql >nul 2>&1

echo.
echo ✅ MySQL密码重置完成！
echo 💡 现在root用户无密码，可以直接连接
echo.
echo 🧪 测试连接...
mysql -u root -e "SELECT 'MySQL连接成功!' as result;"

if %errorlevel% equ 0 (
    echo ✅ MySQL连接测试成功！
) else (
    echo ❌ MySQL连接测试失败，请手动检查
)

echo.
echo 按任意键退出...
pause >nul
