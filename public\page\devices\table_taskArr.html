<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">

            <script type="text/html" id="toolbarDemo">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm layui-btn-danger data-delete-btn" lay-event="delList"> 删除勾选 </button>
    
                    <button class="layui-btn layui-btn-sm layui-btn-danger data-delete-btn" lay-event="delAll"> 全部删除 </button>
                </div>
            </script>
            <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
            <script type="text/html" id="currentTableBar">
            <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="deleteOne">删除</a>
            </script>

        </div>
    </div>
    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script>


        let daiLi_userName = null

        setTimeout(function () {

            layui.use(['form', 'table'], function () {
                var $ = layui.jquery,
                    form = layui.form,
                    table = layui.table;

                let tableIns = table.render({
                    elem: '#currentTableId',
                    url: '/tasks_arr',
                    method: 'get',
                    where: {
                        daiLi_userName: daiLi_userName
                    },
                    toolbar: '#toolbarDemo',
                    defaultToolbar: ['filter', 'exports', 'print', {
                        title: '提示',
                        layEvent: 'LAYTABLE_TIPS',
                        icon: 'layui-icon-tips'
                    }],
                    cols: [[
                        { type: "checkbox", width: 50 },
                        { field: 'taskID', minWidth: 60, title: '任务ID', sort: true },
                        { field: 'taskName', minWidth: 60, title: '任务名称', sort: true },
                        { field: 'taskNumZhong', minWidth: 60, title: '任务总数', sort: true },
                        { field: 'taskNumDevice', minWidth: 60, title: '完成进度', sort: true },
                        {
                            field: 'taskDate', minWidth: 60, title: '任务参数', sort: true, templet: function (d) {
                                return JSON.stringify(d.taskDate)
                            }
                        },
                        { field: 'createTime', minWidth: 60, title: '创建时间', sort: true },
                        { title: '操作', minWidth: 55, toolbar: '#currentTableBar', align: "center" }
                    ]],
                    // limits: [50, 200, 1000, 2000, 5000],
                    // limit: 50,
                    // page: true,
                    // skin: 'line'
                });


                /**
                 * toolbar监听事件
                 */
                table.on('toolbar(currentTableFilter)', function (obj) {
                    // let data = obj.data;
                    if (obj.event === 'delList') {  // 监听批量删除操作
                        const checkStatus = table.checkStatus('currentTableId')
                        const data = checkStatus.data;
                        if (data.length == 0) {
                            return
                        }
                        layer.confirm('真的删除这些数据么?', function (index) {
                            let idArr = []
                            data.forEach(d => {
                                idArr.push(d._id)
                            })
                            tableIns.reload({
                                url: '/tasks_arr/delList',
                                method: 'post',
                                where: {
                                    idArr: idArr,
                                    daiLi_userName: daiLi_userName
                                }
                            })
                            layer.close(index);
                        });
                    } else if (obj.event == "delAll") {
                        layer.confirm('真的全部删除么?', function (index) {
                            tableIns.reload({
                                url: '/tasks_arr/delList',
                                method: 'post',
                                where: {
                                    type: "all",
                                    daiLi_userName: daiLi_userName
                                }
                            })
                            layer.close(index);
                        });
                    }
                });


                table.on('tool(currentTableFilter)', function (obj) {
                    const data = obj.data
                    if (obj.event === 'deleteOne') {
                        layer.confirm('真的删除【' + data.ksId + '】么?', function (index) {
                            obj.del();
                            tableIns.reload({
                                url: '/tasks_arr/delOne',
                                method: 'post',
                                where: {
                                    _id: data._id,
                                    daiLi_userName: daiLi_userName
                                }
                            })
                            layer.close(index);
                        });
                    }
                });
            });

        }, 300)
        function chuanCan(userName) {
            // console.log("传参过来了:" + userName);
            daiLi_userName = userName
            // console.log("daiLi_userName" + daiLi_userName);
        }
    </script>

</body>

</html>