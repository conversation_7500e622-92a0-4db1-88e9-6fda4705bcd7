<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户登录（简化版）</title>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .form-item {
            margin-bottom: 20px;
        }
        
        .form-item label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-item input {
            width: 100%;
            height: 45px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 0 15px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-item input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .login-btn {
            width: 100%;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .message {
            padding: 10px;
            border-radius: 5px;
            margin: 15px 0;
            text-align: center;
            font-size: 14px;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin-top: 20px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-title">推广用户登录</div>
            <div style="color: #666; font-size: 14px;">简化版 - 无依赖</div>
        </div>
        
        <form id="loginForm">
            <div class="form-item">
                <label for="user_id">用户ID</label>
                <input type="text" id="user_id" name="user_id" value="1001" required>
            </div>
            
            <div class="form-item">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" value="123456" required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">登录</button>
        </form>
        
        <div id="message"></div>
        <div class="debug-log" id="debugLog">调试日志：\n</div>
    </div>

    <script>
        const debugLog = document.getElementById('debugLog');
        const messageDiv = document.getElementById('message');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        function showMessage(message, type = 'info') {
            messageDiv.textContent = message;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
        }
        
        function hideMessage() {
            messageDiv.style.display = 'none';
        }
        
        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            addLog('🚀 开始登录流程');
            hideMessage();
            
            const formData = new FormData(this);
            const loginData = {
                user_id: formData.get('user_id').trim(),
                password: formData.get('password').trim()
            };
            
            addLog(`📋 登录数据: 用户ID=${loginData.user_id}`);
            
            if (!loginData.user_id || !loginData.password) {
                addLog('❌ 用户ID或密码为空');
                showMessage('请输入用户ID和密码', 'error');
                return;
            }
            
            // 禁用按钮
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                addLog('🔄 发送登录请求...');
                
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                
                addLog(`📡 收到响应: 状态码=${response.status}`);
                
                const result = await response.json();
                addLog(`📥 响应数据: ${JSON.stringify(result)}`);
                addLog(`🍪 当前cookies: ${document.cookie}`);
                
                if (result.code === 0) {
                    addLog('✅ 登录成功！');
                    showMessage('登录成功，正在跳转...', 'success');
                    
                    addLog('🔄 准备跳转到仪表板...');
                    addLog('执行: window.location.href = "/promoter-dashboard"');
                    
                    // 立即跳转
                    window.location.href = '/promoter-dashboard';
                    
                } else {
                    addLog(`❌ 登录失败: ${result.msg}`);
                    showMessage(result.msg || '登录失败', 'error');
                }
                
            } catch (error) {
                addLog(`❌ 请求失败: ${error.message}`);
                showMessage('网络错误，请稍后重试', 'error');
            } finally {
                // 恢复按钮
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
        
        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('📋 简化版登录页面已加载');
            addLog(`🌐 当前URL: ${window.location.href}`);
            addLog(`🍪 初始cookies: ${document.cookie || '无'}`);
            
            // 检查URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const userId = urlParams.get('user_id');
            if (userId) {
                document.getElementById('user_id').value = userId;
                addLog(`🔗 从URL参数获取用户ID: ${userId}`);
            }
            
            const error = urlParams.get('error');
            if (error) {
                showMessage(decodeURIComponent(error), 'error');
                addLog(`⚠️ URL错误参数: ${error}`);
            }
        });
    </script>
</body>
</html>
