const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

async function addLongSocks5TestData() {
    console.log(colors.bold('🧪 添加长SOCKS5地址测试数据'));
    console.log('='.repeat(60));
    console.log(colors.cyan('目的: 测试页面对长SOCKS5地址的显示处理\n'));

    const testProxies = [
        {
            sk5: 'very-long-hostname-for-testing-display.example.com:8080',
            ip: '*************',
            province: '北京市',
            usage_count: 3,
            status: 'active'
        },
        {
            sk5: 'super-extremely-long-domain-name-that-might-cause-display-issues.proxy-service.com:9999',
            ip: '*************',
            province: '上海市',
            usage_count: 7,
            status: 'active'
        },
        {
            sk5: 'auth-proxy.example-service.com|8888|very_long_username_for_testing|very_long_password_123456789|2025-12-31',
            ip: '*************',
            province: '广东省',
            usage_count: 9,
            status: 'active'
        },
        {
            sk5: 'socks5://username:<EMAIL>:1080',
            ip: '*************',
            province: '浙江省',
            usage_count: 2,
            status: 'active'
        },
        {
            sk5: '123.456.789.012:1080|user123|pass456|2025-06-30',
            ip: '123.456.789.012',
            province: '江苏省',
            usage_count: 5,
            status: 'active'
        },
        {
            sk5: 'short.com:1080',
            ip: '*************',
            province: '山东省',
            usage_count: 1,
            status: 'active'
        }
    ];

    console.log(colors.blue('📝 准备添加以下测试代理:'));
    testProxies.forEach((proxy, index) => {
        console.log(`${index + 1}. ${colors.cyan(proxy.province)} - ${proxy.sk5.substring(0, 50)}${proxy.sk5.length > 50 ? '...' : ''}`);
    });

    console.log(colors.yellow('\n⏳ 开始添加测试数据...'));

    let successCount = 0;
    let skipCount = 0;

    for (const [index, proxy] of testProxies.entries()) {
        try {
            console.log(colors.blue(`\n📤 添加第${index + 1}个代理...`));
            console.log(`   省份: ${proxy.province}`);
            console.log(`   SOCKS5: ${proxy.sk5}`);
            console.log(`   使用次数: ${proxy.usage_count}`);

            const response = await axios.post(`${BASE_URL}/proxies`, proxy);
            
            if (response.data.code === 0) {
                successCount++;
                console.log(colors.green(`   ✅ 添加成功 (ID: ${response.data.data.id})`));
            } else {
                skipCount++;
                console.log(colors.yellow(`   ⚠️  跳过: ${response.data.msg}`));
            }
        } catch (error) {
            skipCount++;
            console.log(colors.red(`   ❌ 添加失败: ${error.response?.data?.msg || error.message}`));
        }

        // 短暂延迟
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log(colors.bold('\n📊 添加结果统计:'));
    console.log(`✅ 成功添加: ${colors.green(successCount)} 个`);
    console.log(`⚠️  跳过/失败: ${colors.yellow(skipCount)} 个`);
    console.log(`📝 总计: ${testProxies.length} 个`);

    // 验证添加结果
    console.log(colors.blue('\n🔍 验证添加结果...'));
    try {
        const listResponse = await axios.get(`${BASE_URL}/proxies`);
        if (listResponse.data.code === 0) {
            const allProxies = listResponse.data.data;
            console.log(colors.green(`✅ 当前数据库中共有 ${allProxies.length} 个代理`));
            
            // 显示最长的几个SOCKS5地址
            const sortedByLength = allProxies
                .sort((a, b) => b.sk5.length - a.sk5.length)
                .slice(0, 3);
                
            console.log(colors.cyan('\n📏 最长的3个SOCKS5地址:'));
            sortedByLength.forEach((proxy, index) => {
                console.log(`${index + 1}. [${proxy.sk5.length}字符] ${proxy.sk5.substring(0, 60)}${proxy.sk5.length > 60 ? '...' : ''}`);
            });
        }
    } catch (error) {
        console.log(colors.red('❌ 验证失败:', error.message));
    }

    console.log(colors.bold('\n🎉 测试数据添加完成!'));
    console.log('='.repeat(60));
    console.log(colors.cyan('💡 现在可以访问页面查看长SOCKS5地址的显示效果:'));
    console.log(colors.bold('   http://localhost:15001/page/task/douyin514CKIP.html'));
    console.log('\n🔧 新功能测试:');
    console.log('   1. 长地址自动省略显示');
    console.log('   2. 点击展开/收起完整地址');
    console.log('   3. 悬停显示复制按钮');
    console.log('   4. 一键复制SOCKS5地址');
    console.log('   5. 响应式布局适配');
}

// 运行测试
console.log(colors.bold('🚀 长SOCKS5地址显示测试脚本'));
console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
console.log('\n' + colors.yellow('⏳ 3秒后开始添加测试数据...'));

setTimeout(() => {
    addLongSocks5TestData().catch(error => {
        console.error(colors.red('💥 测试脚本执行失败:'), error.message);
        process.exit(1);
    });
}, 3000);
