<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>修改密码</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .layui-form-item .layui-input-company {
            width: auto;
            padding-right: 10px;
            line-height: 38px;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">

            <!-- <form class="layui-form layuimini-form" action="/user/changePass" method="POST"> -->
            <form class="layui-form layuimini-form">
                <br>
                <div class="layui-form-item">
                    <label class="layui-form-label required">新的密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="userPass" lay-verify="required" lay-reqtext="新的密码不能为空"
                            placeholder="请输入新的密码" value="" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label required">再次输入</label>
                    <div class="layui-input-block">
                        <input type="password" name="again_password" lay-verify="required" lay-reqtext="新的密码不能为空"
                            placeholder="请再次输入新的密码" value="" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn">确认修改</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <!-- <script src="/js/lay-config.js?v=1.0.4" charset="utf-8"></script> -->

    <script>
        layui.use('form', function () {
            var form = layui.form,
                layer = layui.layer
            /**
             * 初始化表单，要加上，不然刷新部分组件可能会不加载
             */
            // form.render();

            //监听提交
            form.on('submit(saveBtn)', function (data) {
                let datas = data.field
                if (datas.userPass != datas.again_password) {
                    layer.msg("两次输入不一致")
                    return false
                }
                axios.post('/user/changePass', datas).then(res => {
                    if (res.data.code == 0) {
                        layer.msg("修改成功")
                        // location.href = '/'; //后台主页
                        window.location.reload()
                    } else {
                        layer.alert(res.data.msg)
                    }
                }).catch(err => {
                    layer.alert(err.message)
                })

                return false
            });
        });
    </script>
</body>

</html>