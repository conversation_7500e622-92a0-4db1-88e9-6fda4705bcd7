const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 检查推广用户数据
async function checkPromotionData() {
  console.log('🔍 检查推广用户数据库数据...');
  
  const testUserId = '1001';
  const today = new Date().toISOString().split('T')[0];
  
  console.log(`\n📋 检查用户ID: ${testUserId}`);
  console.log(`📅 今日日期: ${today}`);
  console.log('─'.repeat(60));
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查推广用户是否存在
    console.log('\n1️⃣ 检查推广用户是否存在...');
    const [users] = await connection.execute(
      `SELECT promoter_id, username, user_type, visit_count, unique_ip_count, 
              scan_count, success_count, fail_count, expire_count, last_stat_update
       FROM user 
       WHERE promoter_id = ? AND user_type = 'promoter'`,
      [testUserId]
    );
    
    if (users.length > 0) {
      console.log('✅ 推广用户存在:');
      console.log('   用户信息:', JSON.stringify(users[0], null, 2));
    } else {
      console.log('❌ 推广用户不存在，需要先创建推广用户');
      
      // 创建测试推广用户
      console.log('\n🔧 创建测试推广用户...');
      await connection.execute(
        `INSERT INTO user (promoter_id, username, password, user_type, status, ckcount)
         VALUES (?, ?, ?, 'promoter', 1, 0)
         ON DUPLICATE KEY UPDATE
         user_type = 'promoter', status = 1`,
        [testUserId, `promoter${testUserId}`, '123456']
      );
      console.log('✅ 测试推广用户已创建');
    }
    
    // 2. 检查promotion_daily_stats表中的数据
    console.log('\n2️⃣ 检查promotion_daily_stats表中的今日数据...');
    const [dailyStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats 
       WHERE promotion_user_id = ? AND stat_date = ?`,
      [testUserId, today]
    );
    
    if (dailyStats.length > 0) {
      console.log('✅ 找到今日统计数据:');
      console.log('   数据:', JSON.stringify(dailyStats[0], null, 2));
    } else {
      console.log('❌ 没有找到今日统计数据');
    }
    
    // 3. 检查promotion_visits表中的数据
    console.log('\n3️⃣ 检查promotion_visits表中的数据...');
    const [visits] = await connection.execute(
      `SELECT COUNT(*) as total_visits, 
              COUNT(DISTINCT visitor_ip) as unique_ips,
              MIN(visit_time) as first_visit,
              MAX(visit_time) as last_visit
       FROM promotion_visits 
       WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    console.log('✅ 访问统计:');
    console.log('   数据:', JSON.stringify(visits[0], null, 2));
    
    // 检查今日访问数据
    const [todayVisits] = await connection.execute(
      `SELECT COUNT(*) as today_visits, 
              COUNT(DISTINCT visitor_ip) as today_unique_ips
       FROM promotion_visits 
       WHERE promotion_user_id = ? AND DATE(visit_time) = ?`,
      [testUserId, today]
    );
    
    console.log('   今日访问:', JSON.stringify(todayVisits[0], null, 2));
    
    // 4. 检查promotion_actions表中的数据
    console.log('\n4️⃣ 检查promotion_actions表中的数据...');
    const [actions] = await connection.execute(
      `SELECT 
         COUNT(*) as total_actions,
         COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
         COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
         COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count,
         COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as expire_count,
         MIN(action_time) as first_action,
         MAX(action_time) as last_action
       FROM promotion_actions 
       WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    console.log('✅ 操作统计:');
    console.log('   数据:', JSON.stringify(actions[0], null, 2));
    
    // 检查今日操作数据
    const [todayActions] = await connection.execute(
      `SELECT 
         COUNT(*) as today_actions,
         COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as today_scan_count,
         COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as today_success_count,
         COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as today_fail_count,
         COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as today_expire_count
       FROM promotion_actions 
       WHERE promotion_user_id = ? AND DATE(action_time) = ?`,
      [testUserId, today]
    );
    
    console.log('   今日操作:', JSON.stringify(todayActions[0], null, 2));
    
    // 5. 如果没有数据，创建一些测试数据
    if (visits[0].total_visits === 0 && actions[0].total_actions === 0) {
      console.log('\n5️⃣ 没有找到任何数据，创建测试数据...');
      
      // 创建测试访问数据
      const testIPs = ['*************', '*************', '*************'];
      for (const ip of testIPs) {
        await connection.execute(
          `INSERT INTO promotion_visits (promotion_user_id, visitor_ip, ip_province, user_agent)
           VALUES (?, ?, '测试省份', 'Test User Agent')`,
          [testUserId, ip]
        );
      }
      console.log('✅ 已创建测试访问数据');
      
      // 创建测试操作数据
      const testActions = [
        { type: 'scan', ip: '*************' },
        { type: 'login_success', ip: '*************' },
        { type: 'scan', ip: '*************' },
        { type: 'login_success', ip: '*************' },
        { type: 'scan', ip: '*************' },
        { type: 'login_fail', ip: '*************' }
      ];
      
      for (const action of testActions) {
        await connection.execute(
          `INSERT INTO promotion_actions (promotion_user_id, action_type, ip_address, ip_province)
           VALUES (?, ?, ?, '测试省份')`,
          [testUserId, action.type, action.ip]
        );
      }
      console.log('✅ 已创建测试操作数据');
      
      // 重新检查数据
      console.log('\n📊 重新检查创建的数据...');
      const [newVisits] = await connection.execute(
        `SELECT COUNT(*) as total_visits, COUNT(DISTINCT visitor_ip) as unique_ips
         FROM promotion_visits WHERE promotion_user_id = ?`,
        [testUserId]
      );
      
      const [newActions] = await connection.execute(
        `SELECT 
           COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
           COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
           COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count
         FROM promotion_actions WHERE promotion_user_id = ?`,
        [testUserId]
      );
      
      console.log('   新访问数据:', JSON.stringify(newVisits[0], null, 2));
      console.log('   新操作数据:', JSON.stringify(newActions[0], null, 2));
    }
    
    // 6. 检查表结构
    console.log('\n6️⃣ 检查表结构...');
    
    const tables = ['promotion_daily_stats', 'promotion_visits', 'promotion_actions'];
    for (const table of tables) {
      try {
        const [columns] = await connection.execute(
          `DESCRIBE ${table}`
        );
        console.log(`✅ ${table} 表结构:`);
        columns.forEach(col => {
          console.log(`   ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? col.Key : ''}`);
        });
      } catch (error) {
        console.log(`❌ ${table} 表不存在或有问题:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ 检查数据失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
  
  console.log('\n🎉 数据检查完成！');
  
  console.log('\n💡 问题排查建议:');
  console.log('1. 确保推广用户存在且user_type为"promoter"');
  console.log('2. 确保promotion_visits和promotion_actions表有对应用户的数据');
  console.log('3. 确保promotion_daily_stats表结构正确');
  console.log('4. 检查API是否正确使用用户ID查询数据');
  
  console.log('\n🌐 测试建议:');
  console.log('1. 使用用户ID 1001, 密码 123456 登录');
  console.log('2. 如果仍然显示0，检查浏览器控制台的API响应');
  console.log('3. 检查服务器日志中的SQL查询和结果');
}

// 运行检查
if (require.main === module) {
  checkPromotionData().catch(console.error);
}

module.exports = checkPromotionData;
