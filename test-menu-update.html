<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试菜单更新</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .menu-item {
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .child-item {
            margin-left: 20px;
            background: #e9ecef;
            border-left-color: #28a745;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>菜单更新测试</h1>
        
        <button class="btn" onclick="loadMenu()">加载菜单</button>
        <button class="btn" onclick="testPromotionPages()">测试推广页面</button>
        
        <div id="status"></div>
        <div id="menuContent"></div>
    </div>

    <script>
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<p class="${isError ? 'error' : 'success'}">${message}</p>`;
        }

        async function loadMenu() {
            try {
                showStatus('正在加载菜单...');
                
                const response = await fetch('/user/getOneUserMenu');
                const menuData = await response.json();
                
                if (response.ok) {
                    showStatus('✅ 菜单加载成功');
                    displayMenu(menuData);
                } else {
                    showStatus('❌ 菜单加载失败: ' + (menuData.message || '未知错误'), true);
                }
            } catch (error) {
                showStatus('❌ 网络错误: ' + error.message, true);
            }
        }

        function displayMenu(menuData) {
            const container = document.getElementById('menuContent');
            let html = '<h2>菜单结构</h2>';
            
            if (Array.isArray(menuData)) {
                menuData.forEach(item => {
                    html += renderMenuItem(item);
                });
            } else {
                html += '<p>菜单数据格式错误</p>';
            }
            
            container.innerHTML = html;
        }

        function renderMenuItem(item, isChild = false) {
            let html = `<div class="menu-item ${isChild ? 'child-item' : ''}">`;
            html += `<strong>${item.title}</strong>`;
            if (item.href) {
                html += ` - <a href="${item.href}" target="_blank">${item.href}</a>`;
            }
            html += ` (ID: ${item.id})`;
            if (item.icon) {
                html += ` <i class="${item.icon}"></i>`;
            }
            html += '</div>';
            
            if (item.child && Array.isArray(item.child)) {
                item.child.forEach(childItem => {
                    html += renderMenuItem(childItem, true);
                });
            }
            
            return html;
        }

        async function testPromotionPages() {
            showStatus('正在测试推广页面...');
            
            const pages = [
                { name: '推广用户管理', url: '/page/promotion/admin-promotion.html' },
                { name: '推广用户登录', url: '/page/promotion/promoter-login.html' },
                { name: '推广用户仪表板', url: '/page/promotion/promoter-dashboard.html' }
            ];
            
            let results = '<h2>页面测试结果</h2>';
            
            for (const page of pages) {
                try {
                    const response = await fetch(page.url);
                    if (response.ok) {
                        results += `<div class="menu-item"><span class="success">✅ ${page.name}</span> - 页面可访问</div>`;
                    } else {
                        results += `<div class="menu-item"><span class="error">❌ ${page.name}</span> - HTTP ${response.status}</div>`;
                    }
                } catch (error) {
                    results += `<div class="menu-item"><span class="error">❌ ${page.name}</span> - 网络错误</div>`;
                }
            }
            
            document.getElementById('menuContent').innerHTML = results;
            showStatus('✅ 页面测试完成');
        }

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            loadMenu();
        });
    </script>
</body>
</html>
