<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">设备名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="deviceName" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button type="submit" class="layui-btn layui-btn-primary" lay-submit
                            lay-filter="data-search-btn"><i class="layui-icon"></i> 模糊搜索/规则搜索</button>
                    </div>
                    <br>模糊搜:如搜a即包含a的所有;规则搜:如a1-a3,a8,a17-a18(即a1/a2/a3/a8/a17/a18)
                </div>
            </form>
            <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
        </div>
    </div>
    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script>
        var group_Name = ""
        layui.use(['form', 'table'], function () {
            var $ = layui.jquery,
                form = layui.form,
                table = layui.table

            let tableIns = table.render({
                elem: '#currentTableId',
                url: '/indexDevice',
                // toolbar: '#toolbarDemo',
                // defaultToolbar: ['filter', 'exports', 'print', {
                //     title: '提示',
                //     layEvent: 'LAYTABLE_TIPS',
                //     icon: 'layui-icon-tips'
                // }],
                where: {
                    groupId: "空的",
                },
                cols: [[
                    { type: "checkbox", width: 50 },
                    { field: 'deviceName', width: 90, title: '设备名', sort: true },
                    { field: 'taskName', width: 110, title: '当前任务', sort: true },

                    {
                        width: 80, title: '状态', sort: true, templet: function (d) {
                            // console.log(d); // 得到当前行数据
                            if (d.taskStatus == "掉线") {
                                return '<span style="color: #F00;font-weight:bold">掉线</span>'
                            } else if (d.taskStatus == "空闲") {
                                return '<span style="color: #00CCFF;font-weight:bold">空闲</span>'
                            } else if (d.taskStatus == "忙碌") {
                                return '<span style="color: #FFCC00;font-weight:bold">忙碌</span>'
                            }
                        }
                    },
                    {
                        width: 100, title: '分辨率', sort: true, templet: function (d) {
                            return d.device_width + "*" + d.device_height
                        }
                    },

                    { field: 'device_UUID', width: 80, title: '设备ID' },
                    // { field: 'groupId', width: 90, title: '分组ID', sort: true },
                    { field: 'deviceMsg', minWidth: 150, title: '最新消息', sort: true },
                    // { field: 'updateTime', width: 140, title: '更新时间', sort: true }
                ]],
                limits: [50, 200, 1000, 2000, 5000],
                limit: 200,
                page: true,
                skin: 'line'
            });



            form.on('submit(data-search-btn)', function (data) {
                let datas = data.field
                // console.log(datas);
                let type = "str"
                let deviceName = (datas.deviceName + "").replace(/\s/g, "").replace(/，/g, ",")
                if (deviceName.indexOf("-") > -1 || deviceName.indexOf(",") > -1) {
                    type = "arr"
                    // deviceName = deviceArr(deviceName)
                }
                tableIns.reload({
                    url: '/indexDevice',
                    method: 'get',
                    where: {
                        deviceName: deviceName,
                        groupId: "空的",
                        type: type
                    },
                    limits: [1000, 2000, 5000, 10000],
                    limit: 5000,
                    page: true,
                    skin: 'line'
                })
                return false;
            })


            window.formData = function () {
                var checkData = table.checkStatus('currentTableId').data
                // var iframeIndex = parent.layer.getFrameIndex(window.name);
                // parent.layer.close(iframeIndex);
                // return true//checkData

                if (checkData.length == 0) {
                    layer.msg("没选任何设备")
                    return false
                }

                let deviceIdArr = []
                checkData.forEach(device_data => {
                    deviceIdArr.push(device_data._id)
                })

                return deviceIdArr//JSON.stringify(checkData)


                // layer.prompt({
                //     formType: 2,
                //     value: group_Name,
                //     title: '请输入组名',
                //     area: ['150px', '40px'] //自定义文本域宽高
                // }, function (value, index, elem) {
                //     group_Name = value
                //     layer.close(index);
                //     if (value = "") {
                //         layer.msg("没有输入")
                //     } else {
                //         //表格重载
                //         // console.log(checkData)
                //         tableIns.reload({
                //             url: '/indexDevice',
                //             method: 'post',
                //             where: {
                //                 groupId: "空的",
                //                 groupName: group_Name,
                //                 checkData: JSON.stringify(checkData)
                //             }
                //         })
                //         layer.msg("添加成功")
                //         return true
                //     }
                // });
            }

            // function deviceArr(groupRule) {
            //     let device_arr = []
            //     groupRule.split(",").forEach(element => {
            //         if (element.indexOf("-") > -1) {
            //             let index_min_max = element.split("-")
            //             var qianZui = index_min_max[0].match(/^[^\d]*[0]*/)
            //             if (qianZui) {
            //                 console.log(qianZui);
            //                 qianZui = qianZui[0]
            //             } else {
            //                 qianZui = ""
            //             }
            //             let index_min = parseInt(index_min_max[0].match(/\d+/)[0])

            //             let index_max = parseInt(index_min_max[1].match(/\d+/)[0])
            //             //看下去前缀和0后的最小数的位数
            //             let count_initMin = index_min.toString().length

            //             for (let i = index_min; i <= index_max; i++) {
            //                 //每增加一位数,减少一个前缀0
            //                 let len_add = i.toString().length - count_initMin
            //                 let str0 = ""
            //                 let newQianZui = qianZui
            //                 for (let j = 0; j < len_add; j++) {
            //                     newQianZui = newQianZui.replace('0', '')
            //                 }
            //                 device_arr.push(newQianZui + i)
            //             }
            //         } else {
            //             device_arr.push(element)
            //         }
            //     });
            //     // console.log(device_arr)
            //     return device_arr
            // }


        });

    </script>
</body>

</html>