
    <!DOCTYPE html>
    <html>
    <head>
      <title>验证码: QWER</title>
      <style>
        body { 
          margin: 0; 
          padding: 20px; 
          font-family: Arial, sans-serif; 
          background: #f5f5f5;
        }
        .captcha-container {
          text-align: center;
          background: white;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          max-width: 300px;
          margin: 0 auto;
        }
        .captcha-image {
          border: 2px solid #ddd;
          border-radius: 4px;
          margin: 10px 0;
        }
        .info {
          color: #666;
          font-size: 14px;
          margin-top: 10px;
        }
      </style>
    </head>
    <body>
      <div class="captcha-container">
        <h2>验证码图片</h2>
        <div class="captcha-image">
          
    <svg width="100" height="40" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#F8F9FA"/>
      <line x1="88.7428367390779" y1="31.68887756732935" x2="9.37897199237434" y2="12.747995915973593" stroke="#FF6B6B" stroke-width="1" opacity="0.3"/><line x1="27.05555098886783" y1="28.930983354977435" x2="33.80722603182888" y2="29.888416843767757" stroke="#96CEB4" stroke-width="1" opacity="0.3"/><line x1="1.2001136147022473" y1="37.30742220178338" x2="81.40843201860551" y2="30.399804044043854" stroke="#F7DC6F" stroke-width="1" opacity="0.3"/>
      <circle cx="10.176045682646095" cy="35.84057602251377" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="92.14349241571611" cy="15.058106881105724" r="1" fill="#FFEAA7" opacity="0.5"/><circle cx="69.5435560848763" cy="27.04193834970514" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="76.91673105177546" cy="23.01854900298813" r="1" fill="#FFEAA7" opacity="0.5"/><circle cx="84.84152326977386" cy="21.617116181182993" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="66.62477678452589" cy="38.83004263367945" r="1" fill="#FFEAA7" opacity="0.5"/><circle cx="84.36135909400701" cy="32.545521837863106" r="1" fill="#FFEAA7" opacity="0.5"/><circle cx="11.450723489206904" cy="11.71256937530738" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="13.723397375597601" cy="11.467990894420339" r="1" fill="#4ECDC4" opacity="0.5"/><circle cx="44.60365777260278" cy="25.25761086149581" r="1" fill="#45B7D1" opacity="0.5"/><circle cx="25.61355448567204" cy="19.395701657618183" r="1" fill="#FF6B6B" opacity="0.5"/><circle cx="14.525918520376102" cy="35.888184411202" r="1" fill="#F7DC6F" opacity="0.5"/><circle cx="1.6247993872677835" cy="18.3409720270081" r="1" fill="#FF6B6B" opacity="0.5"/><circle cx="27.604415384170732" cy="13.718424144736119" r="1" fill="#4ECDC4" opacity="0.5"/><circle cx="28.305509237517246" cy="22.67732591754541" r="1" fill="#45B7D1" opacity="0.5"/><circle cx="69.57620975484582" cy="25.983644873713388" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="22.918617508782813" cy="27.042493522237248" r="1" fill="#96CEB4" opacity="0.5"/><circle cx="8.52375969775463" cy="28.622883922195015" r="1" fill="#45B7D1" opacity="0.5"/><circle cx="22.75132925850101" cy="36.5289857495745" r="1" fill="#FFEAA7" opacity="0.5"/><circle cx="37.38863732483415" cy="14.18010018381417" r="1" fill="#98D8C8" opacity="0.5"/>
      
      <text x="20" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#FFEAA7" 
            text-anchor="middle"
            transform="rotate(-12.091182774424297 20 25.333333333333332)">
        Q
      </text>
      <text x="40" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#45B7D1" 
            text-anchor="middle"
            transform="rotate(1.3039080486802712 40 25.333333333333332)">
        W
      </text>
      <text x="60" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#96CEB4" 
            text-anchor="middle"
            transform="rotate(-7.232202519565394 60 25.333333333333332)">
        E
      </text>
      <text x="80" y="25.333333333333332" 
            font-family="Arial, sans-serif" 
            font-size="16" 
            font-weight="bold"
            fill="#96CEB4" 
            text-anchor="middle"
            transform="rotate(11.481506223744898 80 25.333333333333332)">
        R
      </text>
    </svg>
  
        </div>
        <div class="info">
          <strong>文件名:</strong> captcha3.jpg<br>
          <strong>验证码:</strong> QWER<br>
          <strong>说明:</strong> 请将此图片保存为 captcha3.jpg
        </div>
      </div>
    </body>
    </html>
  