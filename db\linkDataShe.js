const mongoose = require('./db.js');

// 链接数据模型
const linkDataShe = new mongoose.Schema({
    phoneNumber: {
        type: String,
        required: true,
        index: true
    },
    username: {
        type: String,
        required: true
    },
    links: {
        type: String,
        required: true
    },
    userName: { // 关联用户
        type: String,
        required: true,
        index: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = linkDataShe;
