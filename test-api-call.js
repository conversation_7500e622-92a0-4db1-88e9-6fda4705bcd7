const axios = require('axios');

async function testAPICall() {
  console.log('🧪 测试API调用...');
  
  try {
    console.log('📤 发送请求到: http://localhost:15001/api/douyin/test-proxy-detection');
    console.log('📤 请求数据: {"sk5":"127.0.0.1:1080"}');
    
    const response = await axios.post('http://localhost:15001/api/douyin/test-proxy-detection', {
      sk5: '127.0.0.1:1080'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30秒超时
    });
    
    console.log('📡 响应状态:', response.status);
    console.log('📡 响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else if (error.request) {
      console.error('请求未收到响应');
    } else {
      console.error('请求配置错误:', error.message);
    }
  }
}

// 运行测试
if (require.main === module) {
  testAPICall();
}

module.exports = testAPICall;
