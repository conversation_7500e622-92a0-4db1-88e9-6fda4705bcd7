-- 为promotion_actions表添加IP省份字段
ALTER TABLE promotion_actions 
ADD COLUMN ip_province VARCHAR(50) COMMENT 'IP所属省份' AFTER ip_address;

-- 添加省份字段的索引
ALTER TABLE promotion_actions 
ADD INDEX idx_ip_province (ip_province);

-- 更新现有数据的省份信息（示例数据）
UPDATE promotion_actions SET ip_province = '广东省' WHERE ip_address LIKE '*************';
UPDATE promotion_actions SET ip_province = '北京市' WHERE ip_address LIKE '*************';
UPDATE promotion_actions SET ip_province = '上海市' WHERE ip_address LIKE '*************';
UPDATE promotion_actions SET ip_province = '江苏省' WHERE ip_address LIKE '*************';
UPDATE promotion_actions SET ip_province = '浙江省' WHERE ip_address LIKE '*************';

-- 为promotion_visits表也添加省份字段
ALTER TABLE promotion_visits 
ADD COLUMN ip_province VARCHAR(50) COMMENT 'IP所属省份' AFTER visitor_ip;

-- 添加省份字段的索引
ALTER TABLE promotion_visits 
ADD INDEX idx_ip_province (ip_province);

-- 更新现有访问数据的省份信息
UPDATE promotion_visits SET ip_province = '广东省' WHERE visitor_ip LIKE '*************';
UPDATE promotion_visits SET ip_province = '北京市' WHERE visitor_ip LIKE '*************';
UPDATE promotion_visits SET ip_province = '上海市' WHERE visitor_ip LIKE '*************';
UPDATE promotion_visits SET ip_province = '江苏省' WHERE visitor_ip LIKE '*************';
UPDATE promotion_visits SET ip_province = '浙江省' WHERE visitor_ip LIKE '*************';
