const mysql = require("mysql2/promise");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
  connectionLimit: 10,
  waitForConnections: true,
  queueLimit: 0,
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

/**
 * 推广统计跟踪器
 */
class PromotionTracker {
  
  /**
   * 记录访问
   * @param {string} promotionUserId 推广用户ID
   * @param {string} visitorIp 访问者IP
   * @param {string} userAgent 用户代理
   * @param {string} referer 来源页面
   */
  static async recordVisit(promotionUserId, visitorIp, userAgent = '', referer = '') {
    try {
      const connection = await pool.getConnection();
      
      // 记录访问
      await connection.execute(
        `INSERT INTO promotion_visits (promotion_user_id, visitor_ip, user_agent, referer) 
         VALUES (?, ?, ?, ?)`,
        [promotionUserId, visitorIp, userAgent, referer]
      );
      
      // 更新当天统计
      await this.updateDailyStats(connection, promotionUserId);
      
      connection.release();
      
      console.log(`✅ 记录访问: 推广用户${promotionUserId}, IP: ${visitorIp}`);
    } catch (error) {
      console.error('记录访问失败:', error);
    }
  }
  
  /**
   * 记录操作行为
   * @param {string} promotionUserId 推广用户ID
   * @param {string} actionType 操作类型: scan, login_success, login_fail, request_expire
   * @param {string} ipAddress IP地址
   * @param {string} userId 用户ID（可选）
   * @param {string} douyinName 抖音名（可选）
   * @param {string} douyinId 抖音号（可选）
   * @param {string} ckData CK数据（可选）
   * @param {string} userAgent 用户代理（可选）
   * @param {object} extraData 额外数据（可选）
   */
  static async recordAction(promotionUserId, actionType, ipAddress, options = {}) {
    try {
      const {
        userId = null,
        douyinName = null,
        douyinId = null,
        ckData = null,
        userAgent = null,
        extraData = null
      } = options;
      
      const connection = await pool.getConnection();
      
      // 记录操作
      await connection.execute(
        `INSERT INTO promotion_actions 
         (promotion_user_id, user_id, action_type, ip_address, douyin_name, douyin_id, ck_data, user_agent, extra_data) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          promotionUserId, 
          userId, 
          actionType, 
          ipAddress, 
          douyinName, 
          douyinId, 
          ckData, 
          userAgent, 
          extraData ? JSON.stringify(extraData) : null
        ]
      );
      
      // 更新当天统计
      await this.updateDailyStats(connection, promotionUserId);
      
      connection.release();
      
      console.log(`✅ 记录操作: 推广用户${promotionUserId}, 操作${actionType}, IP: ${ipAddress}`);
    } catch (error) {
      console.error('记录操作失败:', error);
    }
  }
  
  /**
   * 更新每日统计数据
   * @param {object} connection 数据库连接
   * @param {string} promotionUserId 推广用户ID
   */
  static async updateDailyStats(connection, promotionUserId) {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // 计算当天各项统计数据
      const [visitStats] = await connection.execute(
        `SELECT 
          COUNT(*) as visit_count,
          COUNT(DISTINCT visitor_ip) as unique_ip_count
         FROM promotion_visits 
         WHERE promotion_user_id = ? AND DATE(visit_time) = ?`,
        [promotionUserId, today]
      );
      
      const [actionStats] = await connection.execute(
        `SELECT 
          SUM(CASE WHEN action_type = 'scan' THEN 1 ELSE 0 END) as scan_count,
          SUM(CASE WHEN action_type = 'login_success' THEN 1 ELSE 0 END) as success_count,
          SUM(CASE WHEN action_type = 'login_fail' THEN 1 ELSE 0 END) as fail_count,
          SUM(CASE WHEN action_type = 'request_expire' THEN 1 ELSE 0 END) as expire_count
         FROM promotion_actions 
         WHERE promotion_user_id = ? AND DATE(action_time) = ?`,
        [promotionUserId, today]
      );
      
      const visitData = visitStats[0];
      const actionData = actionStats[0];
      
      // 更新或插入统计数据
      await connection.execute(
        `INSERT INTO promotion_daily_stats 
         (promotion_user_id, stat_date, visit_count, unique_ip_count, scan_count, success_count, fail_count, expire_count)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         visit_count = VALUES(visit_count),
         unique_ip_count = VALUES(unique_ip_count),
         scan_count = VALUES(scan_count),
         success_count = VALUES(success_count),
         fail_count = VALUES(fail_count),
         expire_count = VALUES(expire_count),
         updated_at = CURRENT_TIMESTAMP`,
        [
          promotionUserId,
          today,
          visitData.visit_count || 0,
          visitData.unique_ip_count || 0,
          actionData.scan_count || 0,
          actionData.success_count || 0,
          actionData.fail_count || 0,
          actionData.expire_count || 0
        ]
      );
      
    } catch (error) {
      console.error('更新每日统计失败:', error);
    }
  }
  
  /**
   * 从推广链接中提取推广用户ID
   * @param {string} url 推广链接或referer
   * @returns {string|null} 推广用户ID
   */
  static extractPromotionUserId(url) {
    try {
      if (!url) return null;
      
      const urlObj = new URL(url);
      const id = urlObj.searchParams.get('id');
      
      return id;
    } catch (error) {
      // 如果不是有效URL，尝试从查询字符串中提取
      const match = url.match(/[?&]id=([^&]+)/);
      return match ? match[1] : null;
    }
  }
  
  /**
   * 检查推广用户是否存在且有效
   * @param {string} promotionUserId 推广用户ID
   * @returns {boolean} 是否有效
   */
  static async isValidPromotionUser(promotionUserId) {
    try {
      const connection = await pool.getConnection();
      
      const [users] = await connection.execute(
        'SELECT COUNT(*) as count FROM promotion_users WHERE user_id = ? AND status = 1',
        [promotionUserId]
      );
      
      connection.release();
      
      return users[0].count > 0;
    } catch (error) {
      console.error('检查推广用户失败:', error);
      return false;
    }
  }
  
  /**
   * 获取客户端IP地址
   * @param {object} req Express请求对象
   * @returns {string} IP地址
   */
  static getClientIP(req) {
    return req.headers['x-forwarded-for'] || 
           req.headers['x-real-ip'] || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           req.ip ||
           '127.0.0.1';
  }
}

module.exports = PromotionTracker;
