<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; border-radius: 3px; margin: 10px 0; white-space: pre-wrap; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Session调试工具</h1>
    
    <div class="step">
        <h3>步骤1: 登录</h3>
        <button onclick="doLogin()">登录推广用户</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="step">
        <h3>步骤2: 检查Session</h3>
        <button onclick="checkSession()">检查Session状态</button>
        <div id="sessionResult" class="result"></div>
    </div>
    
    <div class="step">
        <h3>步骤3: 获取统计数据</h3>
        <button onclick="getStats()">获取统计数据</button>
        <div id="statsResult" class="result"></div>
    </div>
    
    <div class="step">
        <h3>步骤4: 手动设置Cookie测试</h3>
        <button onclick="manualCookieTest()">手动Cookie测试</button>
        <div id="manualResult" class="result"></div>
    </div>

    <script>
        let sessionCookie = null;
        
        // 登录
        async function doLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = '正在登录...';
            
            try {
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: '1001', password: '123456' })
                });
                
                const data = await response.json();
                
                // 获取响应头中的cookie
                const setCookieHeader = response.headers.get('set-cookie');
                console.log('Set-Cookie header:', setCookieHeader);
                
                // 获取当前页面的cookie
                const currentCookies = document.cookie;
                console.log('当前页面cookies:', currentCookies);
                
                // 提取session cookie
                if (currentCookies) {
                    const cookies = currentCookies.split(';');
                    const sessionCookieMatch = cookies.find(c => c.trim().startsWith('connect.sid='));
                    if (sessionCookieMatch) {
                        sessionCookie = sessionCookieMatch.trim();
                        console.log('提取的session cookie:', sessionCookie);
                    }
                }
                
                resultDiv.textContent = `登录响应:
状态: ${response.status}
数据: ${JSON.stringify(data, null, 2)}
当前cookies: ${currentCookies}
Session cookie: ${sessionCookie || '未找到'}`;
                
                if (data.code === 0) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `登录失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 检查Session
        async function checkSession() {
            const resultDiv = document.getElementById('sessionResult');
            resultDiv.textContent = '正在检查Session...';
            
            try {
                const response = await fetch('/api/promotion/promoter/session-test', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.textContent = `Session检查结果:
状态: ${response.status}
数据: ${JSON.stringify(data, null, 2)}
当前页面cookies: ${document.cookie}`;
                
                if (data.code === 0 && data.data.hasPromotionUser) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `Session检查失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 获取统计数据
        async function getStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.textContent = '正在获取统计数据...';
            
            try {
                const response = await fetch('/api/promotion/promoter/today-stats', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.textContent = `统计数据结果:
状态: ${response.status}
数据: ${JSON.stringify(data, null, 2)}`;
                
                if (data.code === 0) {
                    const stats = data.data;
                    const hasData = Object.values(stats).some(val => val > 0);
                    
                    resultDiv.textContent += `

数据分析:
访问次数: ${stats.visit_count}
独立IP数: ${stats.unique_ip_count}
成功数量: ${stats.success_count}
是否有非零数据: ${hasData}`;
                    
                    if (hasData) {
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent += '\n\n⚠️ 所有数据都为0！';
                    }
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `获取统计数据失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 手动Cookie测试
        async function manualCookieTest() {
            const resultDiv = document.getElementById('manualResult');
            
            if (!sessionCookie) {
                resultDiv.textContent = '请先登录以获取session cookie';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.textContent = '正在进行手动Cookie测试...';
            
            try {
                // 使用手动设置的cookie进行请求
                const response = await fetch('/api/promotion/promoter/today-stats', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cookie': sessionCookie
                    }
                });
                
                const data = await response.json();
                
                resultDiv.textContent = `手动Cookie测试结果:
使用的Cookie: ${sessionCookie}
状态: ${response.status}
数据: ${JSON.stringify(data, null, 2)}`;
                
                if (data.code === 0) {
                    const stats = data.data;
                    const hasData = Object.values(stats).some(val => val > 0);
                    
                    resultDiv.textContent += `

数据分析:
访问次数: ${stats.visit_count}
独立IP数: ${stats.unique_ip_count}
成功数量: ${stats.success_count}
是否有非零数据: ${hasData}`;
                    
                    if (hasData) {
                        resultDiv.className = 'result success';
                        resultDiv.textContent += '\n\n✅ 手动Cookie测试成功！问题在于浏览器的自动cookie处理。';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent += '\n\n❌ 即使手动设置cookie，数据仍为0。问题在于API或数据库。';
                    }
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `手动Cookie测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 页面加载时显示当前cookie状态
        window.onload = function() {
            console.log('页面加载完成');
            console.log('当前cookies:', document.cookie);
        };
    </script>
</body>
</html>
