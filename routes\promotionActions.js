const express = require("express");
const router = express.Router();
const mysql = require("mysql2/promise");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
};

/**
 * 根据IP地址获取省份信息（简化版本）
 * @param {string} ip - IP地址
 * @returns {string} 省份名称
 */
function getProvinceByIP(ip) {
  // 简化的IP地址到省份映射
  const ipProvinceMap = {
    "192.168.1": "北京",
    "192.168.2": "上海",
    "192.168.3": "广东",
    "192.168.4": "浙江",
    "192.168.5": "江苏",
    "10.0.0": "内网",
    "127.0.0": "本地",
  };

  // 提取IP前缀
  const ipPrefix = ip.split(".").slice(0, 3).join(".");

  // 查找匹配的省份
  for (const [prefix, province] of Object.entries(ipProvinceMap)) {
    if (ip.startsWith(prefix)) {
      return province;
    }
  }

  // 默认返回未知
  return "未知";
}

/**
 * 验证操作类型是否有效
 * @param {string} actionType - 操作类型
 * @returns {boolean} 是否有效
 */
function isValidActionType(actionType) {
  const validTypes = ["scan", "login_success", "login_fail", "request_expire"];
  return validTypes.includes(actionType);
}

/**
 * 验证IP地址格式
 * @param {string} ip - IP地址
 * @returns {boolean} 是否有效
 */
function isValidIP(ip) {
  const ipRegex =
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
}

/**
 * 更新每日统计数据到promotion_daily_stats表
 */
async function updateDailyStats(connection, promotionUserId, actionType) {
  try {
    const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD格式

    // 获取今日访问统计
    const [visitStats] = await connection.execute(
      `SELECT
        COUNT(*) as visit_count,
        COUNT(DISTINCT visitor_ip) as unique_ip_count
       FROM promotion_visits
       WHERE promotion_user_id = ? AND DATE(visit_time) = ?`,
      [promotionUserId, today]
    );

    // 获取今日操作统计
    const [actionStats] = await connection.execute(
      `SELECT
        SUM(CASE WHEN action_type = 'scan' THEN 1 ELSE 0 END) as scan_count,
        SUM(CASE WHEN action_type = 'login_success' THEN 1 ELSE 0 END) as success_count,
        SUM(CASE WHEN action_type = 'login_fail' THEN 1 ELSE 0 END) as fail_count,
        SUM(CASE WHEN action_type = 'request_expire' THEN 1 ELSE 0 END) as expire_count
       FROM promotion_actions
       WHERE promotion_user_id = ? AND DATE(action_time) = ?`,
      [promotionUserId, today]
    );

    const visitData = visitStats[0] || { visit_count: 0, unique_ip_count: 0 };
    const actionData = actionStats[0] || {
      scan_count: 0,
      success_count: 0,
      fail_count: 0,
      expire_count: 0,
    };

    // 更新或插入每日统计
    await connection.execute(
      `INSERT INTO promotion_daily_stats
       (promotion_user_id, stat_date, visit_count, unique_ip_count, scan_count, success_count, fail_count, expire_count)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)
       ON DUPLICATE KEY UPDATE
       visit_count = VALUES(visit_count),
       unique_ip_count = VALUES(unique_ip_count),
       scan_count = VALUES(scan_count),
       success_count = VALUES(success_count),
       fail_count = VALUES(fail_count),
       expire_count = VALUES(expire_count),
       updated_at = CURRENT_TIMESTAMP`,
      [
        promotionUserId,
        today,
        visitData.visit_count || 0,
        visitData.unique_ip_count || 0,
        actionData.scan_count || 0,
        actionData.success_count || 0,
        actionData.fail_count || 0,
        actionData.expire_count || 0,
      ]
    );

    console.log(`📊 更新推广用户 ${promotionUserId} 的每日统计成功`);
  } catch (error) {
    console.error("更新每日统计失败:", error);
    throw error;
  }
}

/**
 * 保存推广操作数据到多个表：promotion_visits, promotion_actions, promotion_daily_stats
 * POST /api/promotion-actions/save
 */
router.post("/save", async (req, res) => {
  console.log("📝 接收到保存推广操作数据请求:", req.body);

  try {
    const {
      promotion_user_id,
      action_type,
      ip_address,
      ip_province,
      douyin_name,
      douyin_id,
      ck_data,
      user_agent,
      extra_data,
    } = req.body;

    // 1. 参数验证
    if (!promotion_user_id) {
      return res.json({
        code: -1,
        msg: "推广用户ID不能为空",
        data: null,
      });
    }

    if (!action_type) {
      return res.json({
        code: -1,
        msg: "操作类型不能为空",
        data: null,
      });
    }

    // 处理action_type可能是数组的情况
    let finalActionType = action_type;
    if (Array.isArray(action_type)) {
      if (action_type.length === 0) {
        return res.json({
          code: -1,
          msg: "操作类型数组不能为空",
          data: null,
        });
      }
      // 如果是数组，取最后一个元素（通常是最终状态）
      finalActionType = action_type[action_type.length - 1];
      console.log(
        `⚠️  接收到数组格式的action_type: ${JSON.stringify(
          action_type
        )}, 使用最后一个: ${finalActionType}`
      );
    }

    if (!isValidActionType(finalActionType)) {
      return res.json({
        code: -1,
        msg: `无效的操作类型: ${finalActionType}，支持的类型: scan, login_success, login_fail, request_expire`,
        data: null,
      });
    }

    if (!ip_address) {
      return res.json({
        code: -1,
        msg: "IP地址不能为空",
        data: null,
      });
    }

    if (!isValidIP(ip_address)) {
      return res.json({
        code: -1,
        msg: "IP地址格式不正确",
        data: null,
      });
    }

    // 2. 自动解析IP省份（如果未提供）
    const finalIpProvince = ip_province || getProvinceByIP(ip_address);

    // 3. 获取用户代理（如果未提供，从请求头获取）
    const finalUserAgent = user_agent || req.headers["user-agent"] || "";

    // 4. 处理额外数据
    let finalExtraData = null;
    if (extra_data) {
      try {
        // 如果是字符串，尝试解析为JSON
        if (typeof extra_data === "string") {
          finalExtraData = JSON.parse(extra_data);
        } else {
          finalExtraData = extra_data;
        }
      } catch (error) {
        console.warn("⚠️  额外数据JSON解析失败:", error.message);
        finalExtraData = { raw: extra_data };
      }
    }

    // 5. 连接数据库并插入数据
    // 5. 解析session_id和其他数据
    let sessionId = null;
    let referer = null;

    if (finalExtraData && finalExtraData.session_id) {
      sessionId = finalExtraData.session_id;
    }

    // 从user_agent或其他地方获取referer信息
    if (finalExtraData && finalExtraData.referer) {
      referer = finalExtraData.referer;
    }

    // 6. 连接数据库并开始事务
    const connection = await mysql.createConnection(dbConfig);
    await connection.beginTransaction();

    try {
      // 6.1 保存到promotion_visits表（访问记录）
      console.log("💾 保存访问记录到promotion_visits表...");
      const visitInsertResult = await connection.execute(
        `INSERT INTO promotion_visits
         (promotion_user_id, visitor_ip, user_agent, referer)
         VALUES (?, ?, ?, ?)`,
        [
          promotion_user_id,
          ip_address,
          finalUserAgent,
          referer || `推广访问-${sessionId || "unknown"}`,
        ]
      );

      // 6.2 保存到promotion_actions表（操作记录）
      console.log("💾 保存操作记录到promotion_actions表...");
      const actionInsertResult = await connection.execute(
        `INSERT INTO promotion_actions
         (promotion_user_id, action_type, ip_address, ip_province,
          douyin_name, douyin_id, ck_data, user_agent, extra_data)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          promotion_user_id,
          finalActionType,
          ip_address,
          finalIpProvince,
          douyin_name || null,
          douyin_id || null,
          ck_data || null,
          finalUserAgent,
          finalExtraData ? JSON.stringify(finalExtraData) : null,
        ]
      );

      // 6.3 更新promotion_daily_stats表（每日统计）
      console.log("💾 更新每日统计到promotion_daily_stats表...");
      await updateDailyStats(connection, promotion_user_id, finalActionType);

      // 提交事务
      await connection.commit();
      await connection.end();

      console.log("✅ 推广数据保存成功到多个表:", {
        visit_id: visitInsertResult[0].insertId,
        action_id: actionInsertResult[0].insertId,
        promotion_user_id,
        action_type: finalActionType,
        original_action_type: Array.isArray(action_type)
          ? action_type
          : finalActionType,
        ip_address,
        ip_province: finalIpProvince,
        session_id: sessionId,
      });

      // 7. 返回成功响应
      res.json({
        code: 0,
        msg: "数据保存成功到多个表",
        data: {
          visit_id: visitInsertResult[0].insertId,
          action_id: actionInsertResult[0].insertId,
          promotion_user_id,
          action_type: finalActionType,
          original_action_type: Array.isArray(action_type)
            ? action_type
            : finalActionType,
          ip_address,
          ip_province: finalIpProvince,
          douyin_name: douyin_name || null,
          douyin_id: douyin_id || null,
          session_id: sessionId,
          save_time: new Date().toISOString(),
          tables_updated: [
            "promotion_visits",
            "promotion_actions",
            "promotion_daily_stats",
          ],
        },
      });
    } catch (transactionError) {
      // 回滚事务
      await connection.rollback();
      await connection.end();
      throw transactionError;
    }
  } catch (error) {
    console.error("❌ 保存推广操作数据失败:", error);

    // 处理数据库约束错误
    if (error.code === "ER_BAD_NULL_ERROR") {
      return res.json({
        code: -1,
        msg: "必填字段不能为空",
        data: null,
      });
    }

    if (error.code === "ER_DATA_TOO_LONG") {
      return res.json({
        code: -1,
        msg: "数据长度超出限制",
        data: null,
      });
    }

    res.json({
      code: -1,
      msg: "保存失败: " + error.message,
      data: null,
    });
  }
});

/**
 * 批量保存推广操作数据
 * POST /api/promotion-actions/batch-save
 */
router.post("/batch-save", async (req, res) => {
  console.log("📝 接收到批量保存推广操作数据请求");

  try {
    const { actions } = req.body;

    if (!actions || !Array.isArray(actions) || actions.length === 0) {
      return res.json({
        code: -1,
        msg: "actions参数必须是非空数组",
        data: null,
      });
    }

    if (actions.length > 100) {
      return res.json({
        code: -1,
        msg: "批量保存最多支持100条记录",
        data: null,
      });
    }

    const connection = await mysql.createConnection(dbConfig);
    const results = [];
    const errors = [];

    // 开始事务
    await connection.beginTransaction();

    try {
      for (let i = 0; i < actions.length; i++) {
        const action = actions[i];

        try {
          // 验证必填字段
          if (
            !action.promotion_user_id ||
            !action.action_type ||
            !action.ip_address
          ) {
            errors.push({
              index: i,
              error: "缺少必填字段: promotion_user_id, action_type, ip_address",
            });
            continue;
          }

          // 处理action_type可能是数组的情况
          let finalActionType = action.action_type;
          if (Array.isArray(action.action_type)) {
            if (action.action_type.length === 0) {
              errors.push({
                index: i,
                error: "操作类型数组不能为空",
              });
              continue;
            }
            finalActionType = action.action_type[action.action_type.length - 1];
          }

          // 验证操作类型
          if (!isValidActionType(finalActionType)) {
            errors.push({
              index: i,
              error: "无效的操作类型: " + finalActionType,
            });
            continue;
          }

          // 验证IP地址
          if (!isValidIP(action.ip_address)) {
            errors.push({
              index: i,
              error: "无效的IP地址: " + action.ip_address,
            });
            continue;
          }

          // 自动解析IP省份
          const finalIpProvince =
            action.ip_province || getProvinceByIP(action.ip_address);

          // 处理额外数据
          let finalExtraData = null;
          if (action.extra_data) {
            try {
              if (typeof action.extra_data === "string") {
                finalExtraData = JSON.parse(action.extra_data);
              } else {
                finalExtraData = action.extra_data;
              }
            } catch (error) {
              finalExtraData = { raw: action.extra_data };
            }
          }

          // 插入数据
          const insertResult = await connection.execute(
            `INSERT INTO promotion_actions 
             (promotion_user_id, action_type, ip_address, ip_province, 
              douyin_name, douyin_id, ck_data, user_agent, extra_data)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              action.promotion_user_id,
              finalActionType,
              action.ip_address,
              finalIpProvince,
              action.douyin_name || null,
              action.douyin_id || null,
              action.ck_data || null,
              action.user_agent || "",
              finalExtraData ? JSON.stringify(finalExtraData) : null,
            ]
          );

          results.push({
            index: i,
            id: insertResult[0].insertId,
            promotion_user_id: action.promotion_user_id,
            action_type: finalActionType,
            original_action_type: Array.isArray(action.action_type)
              ? action.action_type
              : finalActionType,
          });
        } catch (error) {
          errors.push({
            index: i,
            error: error.message,
          });
        }
      }

      // 提交事务
      await connection.commit();
      await connection.end();

      console.log(
        `✅ 批量保存完成: 成功${results.length}条, 失败${errors.length}条`
      );

      res.json({
        code: 0,
        msg: `批量保存完成: 成功${results.length}条, 失败${errors.length}条`,
        data: {
          success_count: results.length,
          error_count: errors.length,
          results: results,
          errors: errors,
        },
      });
    } catch (error) {
      // 回滚事务
      await connection.rollback();
      await connection.end();
      throw error;
    }
  } catch (error) {
    console.error("❌ 批量保存推广操作数据失败:", error);
    res.json({
      code: -1,
      msg: "批量保存失败: " + error.message,
      data: null,
    });
  }
});

module.exports = router;
