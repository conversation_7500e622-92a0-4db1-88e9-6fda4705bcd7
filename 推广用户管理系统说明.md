# 推广用户管理系统

## 🎯 功能概述

推广用户管理系统实现了完整的推广链接跟踪和数据统计功能，包括：

1. **后台管理端**: 添加推广用户，查看所有推广数据
2. **推广用户端**: 推广用户登录查看自己的推广统计
3. **数据统计**: 访问次数、独立IP、扫码数量、成功/失败数量等
4. **操作记录**: 详细的用户操作日志
5. **数据导出**: 支持CSV格式导出

## 🚀 快速开始

### 1. 初始化系统

```bash
# 运行初始化脚本
node init-promotion-system.js
```

### 2. 启动服务

```bash
# 启动主服务
node bin/ceshi
```

### 3. 访问系统

- **推广用户登录**: http://localhost:15001/page/promotion/promoter-login.html
- **管理员推广管理**: http://localhost:15001/page/promotion/admin-promotion.html

## 📋 功能详解

### 🔧 管理员功能

#### 1. 添加推广用户
- 访问管理员推广管理页面
- 点击"添加推广用户"按钮
- 填写用户ID、用户名、密码
- 推广链接自动生成：`http://3328.com?id=用户ID`

**示例**:
```
用户ID: 1001
用户名: promoter1001  
密码: 123456
推广链接: http://3328.com?id=1001
```

#### 2. 查看所有用户推广数据
- 选择统计日期
- 查看各推广用户的统计数据：
  - 访问次数
  - 独立IP数
  - 扫码数量
  - 成功数量
  - 失败数量

#### 3. 查看详细操作记录
- 筛选推广用户、操作类型
- 查看完整操作记录，包括：
  - 用户ID
  - 时间
  - IP地址
  - 操作状态（请求过期/登录成功/登录失败）
  - 抖音名
  - 抖音号
  - CK数据

#### 4. 数据导出
- 导出统计数据为CSV
- 导出操作记录为CSV
- 支持按日期、用户筛选导出

### 👤 推广用户功能

#### 1. 登录系统
- 使用推广用户ID和密码登录
- 查看个人推广仪表板

#### 2. 查看推广统计
- 当天实时统计数据：
  - 访问次数
  - 独立IP数
  - 扫码数量
  - 成功数量
  - 失败数量

#### 3. 查看操作记录
- 当天操作记录列表（不显示CK）：
  - 用户ID
  - 时间
  - IP地址
  - 操作状态

## 🔗 API接口

### 管理员接口

#### 登录
```
POST /api/promotion/admin/login
{
  "username": "admin",
  "password": "123456"
}
```

#### 添加推广用户
```
POST /api/promotion/admin/add-promotion-user
{
  "user_id": "1001",
  "username": "promoter1001",
  "password": "123456",
  "promotion_link": "http://3328.com?id=1001"
}
```

#### 获取推广用户列表
```
GET /api/promotion/admin/promotion-users?page=1&limit=10
```

#### 获取所有用户统计
```
GET /api/promotion/admin/all-users-stats?date=2025-06-20
```

#### 获取所有用户操作记录
```
GET /api/promotion/admin/all-users-actions?page=1&limit=50&date=2025-06-20
```

#### 导出数据
```
GET /api/promotion/admin/export-data?type=stats&date=2025-06-20
GET /api/promotion/admin/export-data?type=actions&date=2025-06-20
```

### 推广用户接口

#### 登录
```
POST /api/promotion/promoter/login
{
  "user_id": "1001",
  "password": "123456"
}
```

#### 获取今日统计
```
GET /api/promotion/promoter/today-stats
```

#### 获取今日操作记录
```
GET /api/promotion/promoter/today-actions?page=1&limit=20
```

## 📊 数据统计说明

### 统计指标

1. **访问次数**: 通过推广链接访问的总次数
2. **独立IP数**: 访问的不重复IP地址数量
3. **扫码数量**: 用户扫码操作的次数
4. **成功数量**: 登录成功的次数
5. **失败数量**: 登录失败的次数
6. **过期数量**: 请求过期的次数

### 操作类型

- `scan`: 扫码操作
- `login_success`: 登录成功
- `login_fail`: 登录失败
- `request_expire`: 请求过期

## 🗄️ 数据库结构

### 主要数据表

1. **promotion_users**: 推广用户表
2. **promotion_visits**: 推广访问记录表
3. **promotion_actions**: 推广操作记录表
4. **promotion_daily_stats**: 推广每日统计表
5. **admin_users**: 管理员用户表

## 🔧 集成说明

### 在现有系统中集成推广跟踪

1. **添加推广跟踪中间件**:
```javascript
const { promotionTrackingMiddleware } = require('./middleware/promotionMiddleware');

// 在需要跟踪的路由上添加中间件
app.use(promotionTrackingMiddleware);
```

2. **记录用户操作**:
```javascript
const { recordPromotionAction } = require('./middleware/promotionMiddleware');

// 在用户登录成功后记录
await recordPromotionAction(req, 'login_success', {
  userId: user.id,
  douyinName: user.douyinName,
  douyinId: user.douyinId,
  ckData: user.ck
});
```

3. **处理推广链接访问**:
```javascript
// 用户通过推广链接访问时，URL包含 ?id=推广用户ID
// 中间件会自动记录访问并存储推广用户ID到session
```

## 🔑 默认账户

### 管理员账户
- 用户名: `admin`
- 密码: `123456`

### 推广用户示例
- 用户ID: `1001`
- 密码: `123456`
- 推广链接: `http://3328.com?id=1001`

## 📈 使用流程

### 管理员操作流程
1. 登录管理员账户
2. 添加推广用户（设置用户ID、用户名、密码）
3. 将推广链接提供给推广用户
4. 查看推广数据统计
5. 导出数据报表

### 推广用户操作流程
1. 获得推广链接和登录账户
2. 分享推广链接给目标用户
3. 登录推广用户系统查看数据
4. 查看访问统计和操作记录

### 用户访问流程
1. 用户点击推广链接访问系统
2. 系统自动记录访问（推广用户ID、IP、时间等）
3. 用户进行扫码、登录等操作
4. 系统记录具体操作结果
5. 实时更新统计数据

## 🛠️ 技术特性

- **实时统计**: 数据实时更新，支持自动刷新
- **高性能**: 使用连接池，支持高并发访问
- **数据完整**: 完整记录用户行为轨迹
- **安全性**: 推广用户只能查看自己的数据
- **可扩展**: 模块化设计，易于扩展功能

## 📞 技术支持

如有问题，请检查：
1. MySQL数据库连接是否正常
2. 数据库表是否正确创建
3. 服务器日志中的错误信息
4. 浏览器控制台中的错误信息
