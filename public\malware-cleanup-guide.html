<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>恶意代码清理指南</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #dc3545;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .alert {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .step {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #007cba;
        }
        
        .step h3 {
            margin-top: 0;
            color: #007cba;
        }
        
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        ul {
            padding-left: 20px;
        }
        
        li {
            margin: 8px 0;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 恶意代码清理指南</h1>
        
        <div class="alert">
            <strong>⚠️ 检测到恶意代码！</strong><br>
            您的浏览器中检测到恶意脚本 <code>nodes.js</code>，该脚本正在尝试向外部恶意域名发送请求：
            <code>https://dh.summer5188.com/ele5.php</code>
        </div>
        
        <div class="success">
            <strong>✅ 好消息：</strong> 我们的系统已经成功阻止了这些恶意请求，您的数据是安全的。
        </div>
        
        <h2>🔍 问题分析</h2>
        <p>这个恶意代码很可能来自以下来源之一：</p>
        <ul>
            <li><strong>浏览器扩展</strong> - 某个恶意或被劫持的浏览器扩展</li>
            <li><strong>恶意软件</strong> - 计算机上的恶意软件或广告软件</li>
            <li><strong>网络劫持</strong> - ISP或路由器被劫持</li>
            <li><strong>DNS劫持</strong> - DNS服务器被篡改</li>
        </ul>
        
        <h2>🛠️ 清理步骤</h2>
        
        <div class="step">
            <h3>步骤1: 检查浏览器扩展</h3>
            <p><strong>Microsoft Edge:</strong></p>
            <ol>
                <li>打开 Edge 浏览器</li>
                <li>点击右上角的三个点 ⋯ → 扩展</li>
                <li>查看所有已安装的扩展</li>
                <li>禁用或删除可疑的扩展，特别是：
                    <ul>
                        <li>最近安装的扩展</li>
                        <li>来源不明的扩展</li>
                        <li>名称奇怪的扩展</li>
                    </ul>
                </li>
            </ol>
            
            <p><strong>Google Chrome:</strong></p>
            <ol>
                <li>打开 Chrome 浏览器</li>
                <li>地址栏输入 <code>chrome://extensions/</code></li>
                <li>检查并删除可疑扩展</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>步骤2: 运行杀毒软件扫描</h3>
            <p>使用以下任一杀毒软件进行全盘扫描：</p>
            <ul>
                <li><strong>Windows Defender</strong> (内置)</li>
                <li><strong>Malwarebytes</strong> (专门清理恶意软件)</li>
                <li><strong>AdwCleaner</strong> (专门清理广告软件)</li>
                <li><strong>360安全卫士</strong></li>
                <li><strong>火绒安全</strong></li>
            </ul>
        </div>
        
        <div class="step">
            <h3>步骤3: 清理浏览器数据</h3>
            <p>清理浏览器缓存和数据：</p>
            <ol>
                <li>按 <code>Ctrl + Shift + Delete</code></li>
                <li>选择"所有时间"</li>
                <li>勾选所有选项</li>
                <li>点击"清除数据"</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>步骤4: 检查DNS设置</h3>
            <p>确保DNS设置正确：</p>
            <ol>
                <li>打开"控制面板" → "网络和Internet" → "网络和共享中心"</li>
                <li>点击当前网络连接</li>
                <li>点击"属性" → "Internet协议版本4(TCP/IPv4)" → "属性"</li>
                <li>选择"自动获得DNS服务器地址"或使用安全的DNS：
                    <ul>
                        <li>阿里DNS: *********, *********</li>
                        <li>腾讯DNS: ************</li>
                        <li>Google DNS: *******, *******</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <div class="step">
            <h3>步骤5: 重置浏览器</h3>
            <p>如果问题仍然存在，重置浏览器到默认设置：</p>
            <p><strong>Edge:</strong> 设置 → 重置设置 → 将设置还原为默认值</p>
            <p><strong>Chrome:</strong> 设置 → 高级 → 重置和清理 → 将设置还原为原始默认设置</p>
        </div>
        
        <div class="warning">
            <strong>⚠️ 注意：</strong> 重置浏览器会清除所有扩展、主题、保存的密码等数据。请提前备份重要信息。
        </div>
        
        <h2>🔒 预防措施</h2>
        <ul>
            <li>只从官方商店安装浏览器扩展</li>
            <li>定期更新操作系统和浏览器</li>
            <li>安装可靠的杀毒软件并保持实时保护</li>
            <li>不要点击可疑链接或下载不明文件</li>
            <li>定期检查浏览器扩展列表</li>
        </ul>
        
        <h2>✅ 验证清理效果</h2>
        <p>清理完成后，重新访问系统：</p>
        <ol>
            <li>重启浏览器</li>
            <li>访问 <a href="/clean-promoter-dashboard.html">干净版仪表板</a></li>
            <li>打开开发者工具(F12)查看控制台</li>
            <li>如果不再出现 <code>nodes.js:25</code> 和 <code>summer5188.com</code> 错误，说明清理成功</li>
        </ol>
        
        <div class="success">
            <strong>💡 提示：</strong> 我们已经在干净版仪表板中加入了恶意代码检测和阻止功能，即使有恶意代码也会被自动阻止，确保您的数据安全。
        </div>
        
        <p style="text-align: center; margin-top: 30px;">
            <a href="/clean-promoter-dashboard.html" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">返回安全仪表板</a>
        </p>
    </div>
</body>
</html>
