const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

async function testAPIResponseFormats() {
    console.log(colors.bold('🧪 API返回数据格式测试'));
    console.log('='.repeat(70));
    console.log(colors.cyan('目的: 展示各个API的实际返回数据格式\n'));

    try {
        // 1. 测试获取省份列表
        console.log(colors.blue('📋 1. 获取所有可用省份列表'));
        console.log(colors.cyan('API: GET /api/douyin/get-available-provinces'));
        
        const provincesResponse = await axios.get(`${BASE_URL}/get-available-provinces`);
        console.log(colors.green('✅ 响应数据:'));
        console.log(JSON.stringify(provincesResponse.data, null, 2));

        // 2. 测试获取单个代理（成功情况）
        console.log(colors.blue('\n🎯 2. 获取单个代理（浙江省）'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxy-by-province?province=浙江省'));
        
        const singleProxyResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=浙江省`);
        console.log(colors.green('✅ 响应数据:'));
        console.log(JSON.stringify(singleProxyResponse.data, null, 2));

        // 3. 测试获取单个代理（失败情况）
        console.log(colors.blue('\n❌ 3. 获取单个代理（不存在的省份）'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxy-by-province?province=不存在的省份'));
        
        try {
            const failResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=不存在的省份`);
            console.log(colors.yellow('⚠️  响应数据:'));
            console.log(JSON.stringify(failResponse.data, null, 2));
        } catch (error) {
            if (error.response) {
                console.log(colors.yellow('⚠️  响应数据:'));
                console.log(JSON.stringify(error.response.data, null, 2));
            }
        }

        // 4. 测试批量获取代理
        console.log(colors.blue('\n📦 4. 批量获取代理（北京市，限制3个）'));
        console.log(colors.cyan('API: GET /api/douyin/get-proxies-by-province?province=北京市&limit=3'));
        
        const batchResponse = await axios.get(`${BASE_URL}/get-proxies-by-province?province=北京市&limit=3`);
        console.log(colors.green('✅ 响应数据:'));
        console.log(JSON.stringify(batchResponse.data, null, 2));

        // 5. 测试报告代理状态（成功）
        console.log(colors.blue('\n📊 5. 报告代理状态（成功）'));
        console.log(colors.cyan('API: POST /api/douyin/report-proxy-status'));
        
        // 先获取一个代理用于测试
        const testProxy = singleProxyResponse.data.data;
        if (testProxy) {
            const reportData = {
                sk5: testProxy.sk5,
                status: 'success'
            };
            
            console.log(colors.yellow('📤 请求数据:'));
            console.log(JSON.stringify(reportData, null, 2));
            
            const reportResponse = await axios.post(`${BASE_URL}/report-proxy-status`, reportData);
            console.log(colors.green('✅ 响应数据:'));
            console.log(JSON.stringify(reportResponse.data, null, 2));
        }

        // 6. 测试报告代理状态（失败）
        console.log(colors.blue('\n❌ 6. 报告代理状态（失败）'));
        console.log(colors.cyan('API: POST /api/douyin/report-proxy-status'));
        
        const failReportData = {
            sk5: testProxy.sk5,
            status: 'failed',
            error_msg: '连接超时'
        };
        
        console.log(colors.yellow('📤 请求数据:'));
        console.log(JSON.stringify(failReportData, null, 2));
        
        const failReportResponse = await axios.post(`${BASE_URL}/report-proxy-status`, failReportData);
        console.log(colors.yellow('⚠️  响应数据:'));
        console.log(JSON.stringify(failReportResponse.data, null, 2));

        // 7. 测试参数错误情况
        console.log(colors.blue('\n🚫 7. 参数错误测试'));
        console.log(colors.cyan('API: POST /api/douyin/report-proxy-status (缺少必填参数)'));
        
        try {
            const errorReportResponse = await axios.post(`${BASE_URL}/report-proxy-status`, {
                status: 'success'  // 缺少 proxy_id 或 sk5
            });
            console.log(colors.red('❌ 响应数据:'));
            console.log(JSON.stringify(errorReportResponse.data, null, 2));
        } catch (error) {
            if (error.response) {
                console.log(colors.red('❌ 响应数据:'));
                console.log(JSON.stringify(error.response.data, null, 2));
            }
        }

        console.log(colors.bold('\n🎉 API返回数据格式测试完成!'));
        console.log('='.repeat(70));
        
        // 总结数据格式
        console.log(colors.bold('\n📊 数据格式总结:'));
        console.log('✅ 所有API都遵循统一的响应格式');
        console.log('✅ 成功时 code=0，失败时 code=-1');
        console.log('✅ msg 字段包含描述信息');
        console.log('✅ data 字段包含具体数据');
        console.log('✅ 代理对象包含 id, sk5, ip, province, usage_count');
        console.log('✅ 省份统计包含 province, available_count');
        console.log('✅ 状态报告包含完整的更新信息');

    } catch (error) {
        console.error(colors.red('❌ 测试过程中发生错误:'));
        console.error('错误信息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
console.log(colors.bold('🚀 API返回数据格式测试脚本'));
console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
console.log('\n' + colors.yellow('⏳ 3秒后开始测试...'));

setTimeout(() => {
    testAPIResponseFormats().catch(error => {
        console.error(colors.red('💥 测试脚本执行失败:'), error.message);
        process.exit(1);
    });
}, 3000);
