<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CK复制功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: #f5f5f5;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        th, td {
            border: 1px solid #dee2e6;
            padding: 12px 8px;
            text-align: left;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background: #f9f9f9;
        }

        /* CK数据相关样式 */
        .ck-cell {
            max-width: 200px;
            position: relative;
        }

        .ck-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .ck-preview:hover {
            background: #e9ecef;
            border-color: #007bff;
        }

        .ck-preview.empty {
            color: #6c757d;
            font-style: italic;
        }

        .ck-copy-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 10px;
            cursor: pointer;
            margin-left: 8px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .ck-preview:hover .ck-copy-btn {
            opacity: 1;
        }

        .ck-copy-btn:hover {
            background: #0056b3;
        }

        .ck-copy-btn:active {
            background: #28a745;
        }

        /* 复制成功提示 */
        .copy-success {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 9999;
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 CK复制功能测试</h1>

        <div class="test-section">
            <h3>📋 测试不同格式的CK数据</h3>
            <p>以下表格模拟推广操作记录中的CK数据显示，测试复制功能：</p>
            
            <table>
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>推广用户</th>
                        <th>操作类型</th>
                        <th>CK数据</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody id="testTableBody">
                    <!-- 测试数据将在这里生成 -->
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🔧 功能说明</h3>
            <ul>
                <li><strong>快速复制</strong>：鼠标悬停在CK预览框上，点击右侧的📋按钮快速复制</li>
                <li><strong>详情查看</strong>：点击CK预览框查看完整的CK数据</li>
                <li><strong>格式识别</strong>：自动识别JSON格式的CK数据并显示字符数</li>
                <li><strong>复制反馈</strong>：复制成功后会显示绿色提示和按钮状态变化</li>
                <li><strong>兼容性</strong>：支持现代浏览器的Clipboard API和旧浏览器的兼容方案</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎯 测试操作</h3>
            <button class="btn" onclick="generateTestData()">🔄 重新生成测试数据</button>
            <button class="btn" onclick="testAllCopy()">📋 测试批量复制</button>
            <button class="btn" onclick="clearTestResults()">🗑️ 清除测试结果</button>
        </div>
    </div>

    <script>
        // 测试CK数据
        const testCKData = [
            '', // 空数据
            'sessionid=abc123def456', // 简单cookie
            'sessionid=abc123; uid=user456; token=xyz789', // 多个cookie
            '[{"name":"sessionid","value":"abc123def456","domain":".douyin.com"}]', // JSON格式
            '[{"name":"bd_ticket_guard_client_data","value":"eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRmdwL05PZ0VIV1VFemdrN1Z4aXZXY21SS0Ura3E4bCtpaENXMlJacXFCcTU1ZDAzVVRaQ3pUNFlpZlRSOG1UUks2aktTVEhpUzZqSklVcC9HekNuOUk9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D","domain":".douyin.com","path":"/","expires":1755586224.760081}]', // 复杂JSON
            'very_long_cookie_data_that_should_be_truncated_in_preview_but_fully_copyable_when_clicked_or_quick_copied_using_the_copy_button_functionality_test_data_123456789', // 长数据
        ];

        const testUsers = ['1001', '1002', '1003', '1004', '1005', '1006'];
        const testActions = ['扫码', '登录成功', '登录失败', '请求过期', '扫码', '登录成功'];
        const testDescriptions = [
            '空CK数据测试',
            '简单Cookie格式',
            '多个Cookie格式',
            '标准JSON格式',
            '复杂JSON格式（真实数据）',
            '超长数据截断测试'
        ];

        // 生成测试数据
        function generateTestData() {
            const tbody = document.getElementById('testTableBody');
            tbody.innerHTML = '';

            testCKData.forEach((ckData, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>推广用户${testUsers[index]}</td>
                    <td>${testActions[index]}</td>
                    <td class="ck-cell">${renderCKCell(ckData, index)}</td>
                    <td>${testDescriptions[index]}</td>
                `;
                tbody.appendChild(row);
            });

            console.log('✅ 测试数据已生成');
        }

        // 渲染CK单元格（复制自主页面的函数）
        function renderCKCell(ckData, index) {
            if (!ckData || ckData === '-' || ckData.trim() === '') {
                return '<div class="ck-preview empty">暂无CK数据</div>';
            }

            // 生成预览文本（显示前30个字符）
            let previewText = ckData.length > 30 ? ckData.substring(0, 30) + '...' : ckData;
            
            // 尝试解析JSON格式的CK数据
            let isJSON = false;
            try {
                JSON.parse(ckData);
                isJSON = true;
                previewText = `JSON格式 (${ckData.length}字符)`;
            } catch (e) {
                // 不是JSON格式，使用原始预览
            }

            return `
                <div class="ck-preview" onclick="alert('CK数据:\\n\\n' + '${escapeHtml(ckData)}')" title="点击查看完整CK数据">
                    <span>${previewText}</span>
                    <button class="ck-copy-btn" onclick="event.stopPropagation(); quickCopyCK('${escapeHtml(ckData)}', this)" title="快速复制">
                        📋
                    </button>
                </div>
            `;
        }

        // HTML转义函数
        function escapeHtml(text) {
            if (!text) return '';
            return text
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;')
                .replace(/\n/g, '\\n')
                .replace(/\r/g, '\\r');
        }

        // HTML反转义函数
        function unescapeHtml(text) {
            if (!text) return '';
            return text
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'")
                .replace(/\\n/g, '\n')
                .replace(/\\r/g, '\r');
        }

        // 快速复制CK数据
        function quickCopyCK(ckData, button) {
            const originalCKData = unescapeHtml(ckData);
            
            if (copyToClipboard(originalCKData)) {
                // 显示复制成功效果
                const originalText = button.innerHTML;
                button.innerHTML = '✅';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.style.background = '#007bff';
                }, 1000);
                
                showCopySuccess(`CK数据已复制 (${originalCKData.length}字符)`);
                console.log('📋 复制的CK数据:', originalCKData);
            } else {
                showCopySuccess('复制失败，请手动复制', 'error');
            }
        }

        // 复制到剪贴板函数
        function copyToClipboard(text) {
            try {
                // 现代浏览器使用Clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(text);
                    return true;
                }
                
                // 兼容旧浏览器
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                const result = document.execCommand('copy');
                document.body.removeChild(textArea);
                return result;
            } catch (error) {
                console.error('复制失败:', error);
                return false;
            }
        }

        // 显示复制成功提示
        function showCopySuccess(message, type = 'success') {
            // 移除现有的提示
            const existing = document.querySelector('.copy-success');
            if (existing) {
                existing.remove();
            }
            
            const toast = document.createElement('div');
            toast.className = 'copy-success';
            toast.textContent = message;
            
            if (type === 'error') {
                toast.style.background = '#dc3545';
            }
            
            document.body.appendChild(toast);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        // 测试批量复制
        function testAllCopy() {
            const copyButtons = document.querySelectorAll('.ck-copy-btn');
            let index = 0;
            
            function copyNext() {
                if (index < copyButtons.length) {
                    const button = copyButtons[index];
                    if (button.style.display !== 'none') {
                        button.click();
                        index++;
                        setTimeout(copyNext, 500); // 每500ms复制一个
                    } else {
                        index++;
                        copyNext();
                    }
                } else {
                    showCopySuccess('批量复制测试完成！');
                }
            }
            
            copyNext();
        }

        // 清除测试结果
        function clearTestResults() {
            const existing = document.querySelector('.copy-success');
            if (existing) {
                existing.remove();
            }
            showCopySuccess('测试结果已清除', 'success');
        }

        // 页面加载时生成测试数据
        document.addEventListener('DOMContentLoaded', function() {
            generateTestData();
            console.log('🧪 CK复制功能测试页面已加载');
        });
    </script>
</body>
</html>
