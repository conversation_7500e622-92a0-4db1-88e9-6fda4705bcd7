const mysql = require("mysql2/promise");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
};

// 测试推广用户管理系统的表数据
async function testPromotionTables() {
  console.log('🧪 测试推广用户管理系统的表数据...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    // 1. 检查promotion_users表
    console.log('\n📋 1. 检查promotion_users表:');
    
    try {
      const [users] = await connection.execute('SELECT * FROM promotion_users ORDER BY created_at DESC LIMIT 5');
      console.log(`✅ promotion_users表存在，共有 ${users.length} 条记录（显示前5条）:`);
      
      users.forEach((user, index) => {
        console.log(`  ${index + 1}. 用户ID: ${user.user_id}, 用户名: ${user.username}, 状态: ${user.status}, 创建时间: ${user.created_at}`);
      });
      
      if (users.length === 0) {
        console.log('⚠️  promotion_users表为空，创建测试数据...');
        await createTestPromotionUsers(connection);
      }
    } catch (error) {
      console.log('❌ promotion_users表不存在或查询失败:', error.message);
      console.log('🔧 尝试创建promotion_users表...');
      await createPromotionUsersTable(connection);
    }
    
    // 2. 检查promotion_visits表
    console.log('\n📋 2. 检查promotion_visits表:');
    
    try {
      const [visits] = await connection.execute('SELECT * FROM promotion_visits ORDER BY visit_time DESC LIMIT 5');
      console.log(`✅ promotion_visits表存在，共有 ${visits.length} 条记录（显示前5条）:`);
      
      visits.forEach((visit, index) => {
        console.log(`  ${index + 1}. ID: ${visit.id}, 推广用户: ${visit.promotion_user_id}, IP: ${visit.visitor_ip}, 时间: ${visit.visit_time}`);
      });
      
      if (visits.length === 0) {
        console.log('⚠️  promotion_visits表为空，创建测试数据...');
        await createTestPromotionVisits(connection);
      }
    } catch (error) {
      console.log('❌ promotion_visits表不存在或查询失败:', error.message);
      console.log('🔧 尝试创建promotion_visits表...');
      await createPromotionVisitsTable(connection);
    }
    
    // 3. 测试API端点
    console.log('\n📋 3. 测试API端点:');
    await testAPIEndpoints();
    
    await connection.end();
    
    console.log('\n🎉 推广用户管理系统表数据测试完成！');
    
  } catch (error) {
    console.error('💥 测试失败:', error);
  }
}

// 创建promotion_users表
async function createPromotionUsersTable(connection) {
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS promotion_users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id VARCHAR(50) NOT NULL UNIQUE COMMENT '推广用户ID',
      username VARCHAR(100) NOT NULL COMMENT '用户名',
      password VARCHAR(255) NOT NULL COMMENT '密码',
      promotion_link VARCHAR(500) COMMENT '推广链接',
      status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      INDEX idx_user_id (user_id),
      INDEX idx_status (status),
      INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广用户表';
  `;
  
  await connection.execute(createTableSQL);
  console.log('✅ promotion_users表创建成功');
  
  // 创建测试数据
  await createTestPromotionUsers(connection);
}

// 创建promotion_visits表
async function createPromotionVisitsTable(connection) {
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS promotion_visits (
      id INT AUTO_INCREMENT PRIMARY KEY,
      promotion_user_id VARCHAR(50) NOT NULL COMMENT '推广用户ID',
      visitor_ip VARCHAR(45) NOT NULL COMMENT '访问者IP',
      user_agent TEXT COMMENT '用户代理',
      referer VARCHAR(500) COMMENT '来源页面',
      visit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
      INDEX idx_promotion_user_id (promotion_user_id),
      INDEX idx_visitor_ip (visitor_ip),
      INDEX idx_visit_time (visit_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广访问记录表';
  `;
  
  await connection.execute(createTableSQL);
  console.log('✅ promotion_visits表创建成功');
  
  // 创建测试数据
  await createTestPromotionVisits(connection);
}

// 创建测试推广用户数据
async function createTestPromotionUsers(connection) {
  const testUsers = [
    { user_id: '1001', username: '推广用户1', password: '123456', promotion_link: 'http://localhost:15001/promo/1001', status: 1 },
    { user_id: '1002', username: '推广用户2', password: '123456', promotion_link: 'http://localhost:15001/promo/1002', status: 1 },
    { user_id: '1003', username: '推广用户3', password: '123456', promotion_link: 'http://localhost:15001/promo/1003', status: 0 },
    { user_id: '1004', username: '推广用户4', password: '123456', promotion_link: 'http://localhost:15001/promo/1004', status: 1 },
    { user_id: '1005', username: '推广用户5', password: '123456', promotion_link: 'http://localhost:15001/promo/1005', status: 1 }
  ];
  
  for (const user of testUsers) {
    try {
      await connection.execute(
        'INSERT INTO promotion_users (user_id, username, password, promotion_link, status) VALUES (?, ?, ?, ?, ?)',
        [user.user_id, user.username, user.password, user.promotion_link, user.status]
      );
      console.log(`  ✅ 创建测试用户: ${user.user_id} - ${user.username}`);
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        console.log(`  ⚠️  用户 ${user.user_id} 已存在，跳过`);
      } else {
        console.log(`  ❌ 创建用户 ${user.user_id} 失败:`, error.message);
      }
    }
  }
}

// 创建测试访问数据
async function createTestPromotionVisits(connection) {
  const testVisits = [
    { promotion_user_id: '1001', visitor_ip: '*************', user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', referer: 'https://www.google.com' },
    { promotion_user_id: '1001', visitor_ip: '*************', user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0)', referer: 'https://www.baidu.com' },
    { promotion_user_id: '1002', visitor_ip: '*************', user_agent: 'Mozilla/5.0 (Android 10; Mobile)', referer: 'https://www.weibo.com' },
    { promotion_user_id: '1002', visitor_ip: '*************', user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', referer: 'https://www.douyin.com' },
    { promotion_user_id: '1003', visitor_ip: '*************', user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', referer: 'https://www.tiktok.com' },
    { promotion_user_id: '1004', visitor_ip: '*************', user_agent: 'Mozilla/5.0 (Linux; Android 11)', referer: 'https://www.qq.com' },
    { promotion_user_id: '1001', visitor_ip: '*************', user_agent: 'Mozilla/5.0 (iPad; CPU OS 14_0)', referer: 'https://www.sina.com.cn' },
    { promotion_user_id: '1005', visitor_ip: '*************', user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', referer: 'https://www.zhihu.com' }
  ];
  
  for (const visit of testVisits) {
    try {
      await connection.execute(
        'INSERT INTO promotion_visits (promotion_user_id, visitor_ip, user_agent, referer) VALUES (?, ?, ?, ?)',
        [visit.promotion_user_id, visit.visitor_ip, visit.user_agent, visit.referer]
      );
      console.log(`  ✅ 创建访问记录: 用户${visit.promotion_user_id} - IP${visit.visitor_ip}`);
    } catch (error) {
      console.log(`  ❌ 创建访问记录失败:`, error.message);
    }
  }
}

// 测试API端点
async function testAPIEndpoints() {
  const axios = require('axios');
  const baseURL = 'http://localhost:15001';
  
  try {
    // 测试推广用户列表API
    console.log('  📡 测试推广用户列表API...');
    const usersResponse = await axios.get(`${baseURL}/api/promotion/admin/promotion-users`);
    if (usersResponse.data.code === 0) {
      console.log(`  ✅ 推广用户API正常，返回 ${usersResponse.data.data.list.length} 条用户数据`);
    } else {
      console.log(`  ❌ 推广用户API失败: ${usersResponse.data.msg}`);
    }
    
    // 测试推广访问数据API
    console.log('  📡 测试推广访问数据API...');
    const visitsResponse = await axios.get(`${baseURL}/api/promotion/admin/promotion-visits`);
    if (visitsResponse.data.code === 0) {
      console.log(`  ✅ 推广访问API正常，返回 ${visitsResponse.data.data.list.length} 条访问数据`);
      console.log(`  📊 统计数据: 总访问${visitsResponse.data.data.stats.total_visits}次, 独立IP${visitsResponse.data.data.stats.unique_ips}个`);
    } else {
      console.log(`  ❌ 推广访问API失败: ${visitsResponse.data.msg}`);
    }
    
    // 测试推广访问汇总API
    console.log('  📡 测试推广访问汇总API...');
    const summaryResponse = await axios.get(`${baseURL}/api/promotion/admin/promotion-visits-summary`);
    if (summaryResponse.data.code === 0) {
      console.log(`  ✅ 推广访问汇总API正常，返回 ${summaryResponse.data.data.summary.length} 条汇总数据`);
    } else {
      console.log(`  ❌ 推广访问汇总API失败: ${summaryResponse.data.msg}`);
    }
    
  } catch (error) {
    console.log(`  ❌ API测试失败: ${error.message}`);
    console.log('  💡 请确保服务器正在运行 (node bin/ceshi)');
  }
}

// 运行测试
if (require.main === module) {
  testPromotionTables();
}

module.exports = testPromotionTables;
