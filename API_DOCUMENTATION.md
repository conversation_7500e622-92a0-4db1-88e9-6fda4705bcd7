# API文档

## 设备日志管理
[原有内容保持不变...]

## 链接数据管理
[原有内容保持不变...]

## 设备管理
[原有内容保持不变...]

---

## 任务管理

### 1. 添加任务
#### 请求
- 方法: `POST`
- 路径: `/dyTask/addTask`
- 请求头:
  - `Content-Type: application/json`

#### 请求体参数
| 参数名 | 类型 | 必填 | 描述 | 前端对应字段 | 示例值 |
|--------|------|------|------|--------------|--------|
| device_code | array | 是 | 设备编码数组 | groups_check | ["device1", "device2"] |
| task_name | string | 是 | 任务名称 | selectTask | "养号任务" |
| keywords | array | 否 | 关键词数组 | keywordsArray | ["关键词1", "关键词2"] |
| province | array | 否 | 省份数组 | - | ["北京", "上海"] |
| max_followers | number | 否 | 最大粉丝数 | - | 5000 |
| account_duration | number | 否 | 账号时长(天) | - | 30 |
| like_rate | number | 否 | 点赞率(0-100) | - | 80 |
| comment_rate | number | 否 | 评论率(0-100) | - | 50 |
| comment_content | array | 否 | 评论内容数组 | - | ["评论1", "评论2"] |
| device_status | string | 否 | 设备状态 | - | "running" |

#### 请求示例
```json
{
    "device_code": ["device1", "device2"],
    "task_name": "养号任务",
    "keywords": ["关键词1", "关键词2"],
    "province": ["北京"],
    "max_followers": 5000,
    "account_duration": 30,
    "like_rate": 80,
    "comment_rate": 50,
    "comment_content": ["评论1"],
    "device_status": "pending"
}
```

#### 响应
##### 成功响应 (HTTP 201)
```json
{
    "message": "任务添加成功",
    "taskId": 123
}
```

##### 错误响应 (HTTP 400/500)
```json
{
    "error": "设备编码和任务名称为必填项"
}
```

### 2. 删除任务
#### 请求
- 方法: `DELETE`
- 路径: `/dyTask/deleteTask/:task_id`
- URL参数:
  - task_id: 任务ID

#### 请求示例
`DELETE /dyTask/deleteTask/123`

#### 响应
##### 成功响应 (HTTP 200)
```json
{
    "message": "任务删除成功"
}
```

##### 错误响应 (HTTP 404/500)
```json
{
    "error": "任务不存在"
}
```

### 3. 获取任务列表
#### 请求
- 方法: `GET`
- 路径: `/dyTask/getTasks`
- 查询参数:
  - page: 页码(默认1)
  - limit: 每页数量(默认10)

#### 请求示例
`GET /dyTask/getTasks?page=1&limit=10`

#### 响应
##### 成功响应 (HTTP 200)
```json
{
    "code": 0,
    "msg": "获取任务成功",
    "count": 100,
    "data": [
        {
            "task_id": 123,
            "task_name": "养号任务",
            "device_status": "running",
            "created_at": "2025-06-01T08:00:00.000Z"
        }
    ]
}
```

### 4. 更新任务状态
#### 请求
- 方法: `PUT`
- 路径: `/dyTask/updateTaskStatus/:task_id`
- URL参数:
  - task_id: 任务ID
- 请求体:
  - device_status: 新状态

#### 请求示例
```json
{
    "device_status": "completed"
}
```

#### 响应
##### 成功响应 (HTTP 200)
```json
{
    "success": true,
    "message": "任务状态更新成功"
}
```

##### 错误响应 (HTTP 400/500)
```json
{
    "error": "设备状态不能为空"
}
```

### 错误码
| 错误码 | 描述 |
|--------|------|
| 1001 | device_code不能为空 |
| 1002 | task_name不能为空 |
| 1003 | device_status必须是: pending/running/completed/failed |
| 1004 | 任务不存在 |
| 1005 | 设备分组不能为空 |
