const express = require('express');
const app = express();

// 添加中间件
app.use(express.json());

console.log('开始调试所有路由...');

try {
  // 加载账号路由
  console.log('1. 加载账号路由...');
  const douyinAccountRouter = require('./routes/douyinAccount');
  app.use('/api/douyin', douyinAccountRouter);
  console.log('✓ 账号路由加载成功');

  // 加载IP路由
  console.log('2. 加载IP路由...');
  const douyinIPRouter = require('./routes/douyinIP');
  app.use('/api/douyin', douyinIPRouter);
  console.log('✓ IP路由加载成功');

  // 检查路由栈
  console.log('\n3. 检查注册的路由:');
  app._router.stack.forEach((middleware, index) => {
    if (middleware.route) {
      console.log(`  ${index}: ${middleware.route.path} [${Object.keys(middleware.route.methods).join(', ')}]`);
    } else if (middleware.name === 'router') {
      console.log(`  ${index}: ${middleware.regexp} (router middleware)`);
      if (middleware.handle && middleware.handle.stack) {
        middleware.handle.stack.forEach((route, routeIndex) => {
          if (route.route) {
            console.log(`    ${routeIndex}: ${route.route.path} [${Object.keys(route.route.methods).join(', ')}]`);
          }
        });
      }
    }
  });

  // 启动测试服务器
  console.log('\n4. 启动测试服务器...');
  const PORT = 3002;
  const server = app.listen(PORT, () => {
    console.log(`✓ 测试服务器启动成功: http://localhost:${PORT}`);
    
    // 测试API调用
    setTimeout(async () => {
      try {
        const axios = require('axios');
        
        console.log('\n5. 测试API调用...');
        
        // 测试账号API
        console.log('测试账号API...');
        const accountResponse = await axios.get(`http://localhost:${PORT}/api/douyin/accounts`);
        console.log('✓ 账号API调用成功:', accountResponse.status);
        
        // 测试IP API
        console.log('测试IP API...');
        const ipResponse = await axios.get(`http://localhost:${PORT}/api/douyin/proxies`);
        console.log('✓ IP API调用成功:', ipResponse.status);
        
      } catch (error) {
        console.error('✗ API调用失败:', error.response?.status, error.response?.data || error.message);
      }
      
      server.close();
      process.exit(0);
    }, 2000);
  });

} catch (error) {
  console.error('✗ 调试失败:', error.message);
  console.error('错误堆栈:', error.stack);
  process.exit(1);
}
