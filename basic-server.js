const express = require("express");
const path = require("path");

console.log("🚀 启动基础服务器...");

const app = express();
const port = 15001;

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 简单的内存Session（生产环境应使用Redis等）
const sessions = new Map();

// 简单的Cookie解析中间件
app.use((req, res, next) => {
  const cookieHeader = req.headers.cookie;
  req.cookies = {};

  if (cookieHeader) {
    cookieHeader.split(";").forEach((cookie) => {
      const [name, value] = cookie.trim().split("=");
      if (name && value) {
        req.cookies[name] = decodeURIComponent(value);
      }
    });
  }
  next();
});

// 简单的session中间件
app.use((req, res, next) => {
  const sessionId =
    req.cookies?.sessionId ||
    req.headers["x-session-id"] ||
    req.query.sessionId;
  if (sessionId && sessions.has(sessionId)) {
    req.session = sessions.get(sessionId);
  } else {
    req.session = {};
  }
  next();
});

// 静态文件服务
app.use(express.static(path.join(__dirname, "public")));

// 主页路由 - 重定向到登录页面
app.get("/", (req, res) => {
  console.log("访问主页，Session:", req.session);
  console.log("Cookies:", req.cookies);

  // 检查是否已登录（简单的session检查）
  if (req.session && req.session.user) {
    // 已登录，显示主页
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>系统主页</title>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
          .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          h1 { color: #28a745; text-align: center; }
          .user-info { background: #e9ecef; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
          ul { list-style-type: none; padding: 0; }
          li { margin: 10px 0; }
          a { color: #007bff; text-decoration: none; padding: 8px 12px; display: inline-block; border: 1px solid #007bff; border-radius: 4px; }
          a:hover { background: #007bff; color: white; }
          .logout-btn { background: #dc3545; color: white; border: 1px solid #dc3545; }
          .logout-btn:hover { background: #c82333; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🏠 系统主页</h1>
          <div class="user-info">
            <strong>欢迎，${req.session.user.username || "用户"}！</strong>
            <span style="float: right;">登录时间: ${new Date(
              req.session.user.loginTime || Date.now()
            ).toLocaleString()}</span>
          </div>

          <h3>📋 功能菜单:</h3>
          <ul>
            <li>👥 <a href="/page/promotion/admin-promotion-simple.html">推广用户管理</a></li>
            <li>📊 <a href="/test-captcha.html">验证码测试</a></li>
            <li>🧪 <a href="/api/test">API测试</a></li>
          </ul>

          <h3>🔧 系统功能:</h3>
          <ul>
            <li>🔄 <a href="/api/captcha/get-captcha">获取验证码</a></li>
            <li>📊 <a href="/api/captcha/captcha-stats">验证码统计</a></li>
          </ul>

          <div style="text-align: center; margin-top: 30px;">
            <a href="/logout" class="logout-btn">🚪 退出登录</a>
          </div>
        </div>
      </body>
      </html>
    `);
  } else {
    // 未登录，重定向到登录页面
    console.log("用户未登录，重定向到登录页面");
    res.redirect("/page/login/login.html");
  }
});

// 登录路由
app.post("/login", (req, res) => {
  const { txtUserName, txtUserPwd, captcha } = req.body;

  console.log("登录尝试:", { txtUserName, captcha });

  // 简单的用户验证（实际项目中应该查询数据库）
  const validUsers = {
    admin: "123456",
    cjroot: "123456",
    test: "123456",
  };

  if (!txtUserName || !txtUserPwd) {
    return res.json({
      code: -1,
      msg: "用户名和密码不能为空",
    });
  }

  if (!captcha) {
    return res.json({
      code: -1,
      msg: "验证码不能为空",
    });
  }

  // 简单的验证码验证（接受任何4位字母）
  if (captcha.length !== 4 || !/^[A-Za-z]{4}$/.test(captcha)) {
    return res.json({
      code: -1,
      msg: "验证码格式错误，请输入4位字母",
    });
  }

  // 验证用户名密码
  if (validUsers[txtUserName] && validUsers[txtUserName] === txtUserPwd) {
    // 创建session
    const sessionId =
      Date.now().toString() + Math.random().toString(36).substr(2);
    const userSession = {
      user: {
        username: txtUserName,
        loginTime: Date.now(),
      },
    };

    sessions.set(sessionId, userSession);

    // 设置cookie
    res.cookie("sessionId", sessionId, {
      maxAge: 24 * 60 * 60 * 1000, // 24小时
      httpOnly: true,
    });

    res.json({
      code: 0,
      msg: "登录成功",
      data: {
        username: txtUserName,
        sessionId: sessionId,
      },
    });
  } else {
    res.json({
      code: -1,
      msg: "用户名或密码错误",
    });
  }
});

// 登出路由
app.get("/logout", (req, res) => {
  const sessionId = req.cookies?.sessionId;
  if (sessionId && sessions.has(sessionId)) {
    sessions.delete(sessionId);
  }

  res.clearCookie("sessionId");
  res.redirect("/page/login/login.html");
});

// 获取应用名称API（登录页面需要）
app.get("/getAppName", (req, res) => {
  res.send("推广用户管理系统");
});

app.get("/page/login/getAppName", (req, res) => {
  res.send("推广用户管理系统");
});

// API测试路由
app.get("/api/test", (req, res) => {
  res.json({
    code: 0,
    message: "服务器运行正常",
    timestamp: new Date().toISOString(),
    port: port,
    session: req.session,
  });
});

// 验证码API路由
try {
  const captchaRouter = require("./routes/captcha");
  app.use("/api/captcha", captchaRouter);
  console.log("✅ 验证码路由已加载");
} catch (error) {
  console.log("⚠️  验证码路由加载失败:", error.message);

  // 提供基本的验证码API
  app.get("/api/captcha/get-captcha", (req, res) => {
    res.json({
      code: 0,
      msg: "获取验证码成功",
      data: {
        image: "/images/captcha1.jpg",
        timestamp: Date.now(),
      },
    });
  });
}

// 404处理
app.use((req, res) => {
  console.log("404 请求:", req.method, req.url);
  res.status(404).json({
    code: 404,
    message: "未找到页面或内容",
    url: req.url,
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error("服务器错误:", err);
  res.status(500).json({
    code: 500,
    message: "服务器内部错误",
    error: err.message,
  });
});

// 启动服务器
const server = app.listen(port, () => {
  console.log("✅ 服务器启动成功！");
  console.log(`📍 本地地址: http://localhost:${port}`);
  console.log(`📍 网络地址: http://127.0.0.1:${port}`);
  console.log("");
  console.log("🔗 可用页面:");
  console.log(`- 主页: http://localhost:${port}/`);
  console.log(`- 验证码测试: http://localhost:${port}/test-captcha.html`);
  console.log(`- 登录页面: http://localhost:${port}/page/login/login.html`);
  console.log("");
  console.log("💡 按 Ctrl+C 停止服务器");
});

// 错误处理
server.on("error", (error) => {
  console.error("❌ 服务器启动失败:", error.message);

  if (error.code === "EADDRINUSE") {
    console.log(`💡 端口 ${port} 被占用，请尝试以下解决方案:`);
    console.log("1. 关闭占用端口的程序");
    console.log("2. 使用其他端口");
    console.log("3. 运行: netstat -ano | findstr :15001");
  }

  process.exit(1);
});

// 优雅关闭
process.on("SIGINT", () => {
  console.log("\n🛑 正在关闭服务器...");
  server.close(() => {
    console.log("✅ 服务器已关闭");
    process.exit(0);
  });
});

process.on("SIGTERM", () => {
  console.log("\n🛑 收到终止信号，正在关闭服务器...");
  server.close(() => {
    console.log("✅ 服务器已关闭");
    process.exit(0);
  });
});

module.exports = app;
