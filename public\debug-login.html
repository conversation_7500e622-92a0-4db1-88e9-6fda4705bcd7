<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        button { padding: 12px 24px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
        button:hover { background: #005a87; }
        .result { margin: 20px 0; padding: 15px; border-radius: 5px; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 推广用户登录调试</h1>
        
        <div class="form-group">
            <label for="userId">用户ID:</label>
            <input type="text" id="userId" value="1001">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="123456">
        </div>
        
        <button onclick="debugLogin()">调试登录</button>
        <button onclick="clearResult()">清空结果</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function debugLogin() {
            const userId = document.getElementById('userId').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            // 清空之前的结果
            resultDiv.innerHTML = '';
            
            // 显示开始测试
            showResult('🔄 开始登录调试...', 'info');
            
            try {
                console.log('🔄 发送登录请求:', { user_id: userId, password: '***' });
                
                const startTime = Date.now();
                
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: userId, password: password })
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                const data = await response.json();
                
                console.log('📥 登录API响应:', {
                    status: response.status,
                    data: data,
                    cookies: document.cookie,
                    responseTime: responseTime
                });
                
                // 显示详细结果
                const resultText = `登录调试结果:

🌐 请求信息:
- 用户ID: ${userId}
- 密码: ${password ? '***' : '空'}
- 请求时间: ${responseTime}ms

📡 响应信息:
- HTTP状态码: ${response.status}
- 响应成功: ${response.ok}
- Content-Type: ${response.headers.get('content-type')}

📋 响应数据:
${JSON.stringify(data, null, 2)}

🍪 Cookie信息:
- 请求前: ${document.cookie || '无'}
- 请求后: ${document.cookie || '无'}

🔍 详细分析:
- API响应代码: ${data.code}
- API响应消息: ${data.msg}
- 是否登录成功: ${data.code === 0}
- 返回的用户数据: ${data.data ? JSON.stringify(data.data, null, 2) : '无'}`;
                
                if (data.code === 0) {
                    showResult(resultText + '\n\n✅ 登录成功！应该会跳转到仪表板。', 'success');
                    
                    // 模拟跳转逻辑测试
                    setTimeout(() => {
                        showResult(resultText + '\n\n✅ 登录成功！\n🔄 3秒后模拟跳转...', 'success');
                        setTimeout(() => {
                            showResult(resultText + '\n\n✅ 登录成功！\n🔄 正在跳转到仪表板...', 'success');
                            // 实际跳转
                            window.location.href = '/promoter-dashboard';
                        }, 3000);
                    }, 1000);
                    
                } else {
                    showResult(resultText + '\n\n❌ 登录失败！这就是为什么页面不跳转的原因。', 'error');
                    
                    // 分析失败原因
                    let failureReason = '\n🔍 失败原因分析:\n';
                    if (data.msg.includes('用户ID') || data.msg.includes('密码')) {
                        failureReason += '- 用户ID或密码错误\n';
                        failureReason += '- 请检查数据库中的用户数据\n';
                    } else if (data.msg.includes('数据库') || data.msg.includes('连接')) {
                        failureReason += '- 数据库连接问题\n';
                    } else {
                        failureReason += '- 其他服务器错误\n';
                    }
                    
                    showResult(resultText + '\n\n❌ 登录失败！' + failureReason, 'error');
                }
                
            } catch (error) {
                console.error('❌ 登录请求失败:', error);
                const errorText = `登录调试失败:

❌ 网络错误:
- 错误类型: ${error.name}
- 错误消息: ${error.message}
- 错误堆栈: ${error.stack}

🔍 可能原因:
- 服务器未启动
- 网络连接问题
- API路径错误
- CORS问题`;
                
                showResult(errorText, 'error');
            }
        }
        
        function showResult(text, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = text;
            resultDiv.className = `result ${type}`;
        }
        
        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }
        
        // 页面加载时显示初始状态
        window.onload = function() {
            console.log('📋 登录调试页面已加载');
            console.log('当前cookies:', document.cookie);
            showResult('页面已加载，点击"调试登录"开始测试。\n当前cookies: ' + (document.cookie || '无'), 'info');
        };
    </script>
</body>
</html>
