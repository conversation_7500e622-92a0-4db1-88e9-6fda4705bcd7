const axios = require('axios');

const BASE_URL = 'http://localhost:15001/api/douyin';

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

async function testAutoDisableLogic() {
    console.log(colors.bold('🧪 测试IP使用次数达到10次自动禁用功能'));
    console.log('='.repeat(70));
    console.log(colors.cyan('测试原理: 当IP使用次数达到10次时，自动将状态设置为禁用(status=2)\n'));

    try {
        // 1. 添加一个测试代理
        console.log(colors.blue('📝 步骤1: 添加测试代理'));
        const testProxy = {
            sk5: '***************:1080',
            ip: '***************',
            province: '测试省份',
            usage_count: 0,
            status: 'active'
        };

        let proxyId;
        try {
            const addResponse = await axios.post(`${BASE_URL}/proxies`, testProxy);
            if (addResponse.data.code === 0) {
                proxyId = addResponse.data.data.id;
                console.log(colors.green(`✅ 测试代理添加成功，ID: ${proxyId}`));
            } else {
                console.log(colors.yellow('⚠️  代理可能已存在，尝试获取现有代理...'));
                
                // 尝试获取现有代理
                const getResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=测试省份`);
                if (getResponse.data.code === 0 && getResponse.data.data) {
                    proxyId = getResponse.data.data.id;
                    console.log(colors.green(`✅ 找到现有测试代理，ID: ${proxyId}`));
                } else {
                    throw new Error('无法获取测试代理');
                }
            }
        } catch (error) {
            console.log(colors.red('❌ 添加测试代理失败:', error.message));
            return;
        }

        // 2. 获取初始状态
        console.log(colors.blue('\n📊 步骤2: 获取代理初始状态'));
        const initialResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=测试省份`);
        if (initialResponse.data.code === 0 && initialResponse.data.data) {
            const initialProxy = initialResponse.data.data;
            console.log(`   🆔 代理ID: ${initialProxy.id}`);
            console.log(`   📊 初始使用次数: ${colors.bold(initialProxy.usage_count)}`);
            console.log(`   🔄 初始状态: ${colors.bold(initialProxy.status)}`);
        }

        // 3. 模拟多次成功使用，直到达到10次
        console.log(colors.blue('\n🔄 步骤3: 模拟多次成功使用代理'));
        
        for (let i = 1; i <= 12; i++) {
            console.log(colors.cyan(`\n--- 第${i}次使用 ---`));
            
            // 获取当前状态
            const currentResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=测试省份`);
            if (currentResponse.data.code === 0 && currentResponse.data.data) {
                const currentProxy = currentResponse.data.data;
                console.log(`使用前: 次数=${currentProxy.usage_count}, 状态=${currentProxy.status}`);
                
                // 如果已经被禁用，停止测试
                if (currentProxy.status === 'disabled') {
                    console.log(colors.red('🚫 代理已被禁用，停止测试'));
                    break;
                }
                
                // 报告成功使用
                const reportResponse = await axios.post(`${BASE_URL}/report-proxy-status`, {
                    sk5: currentProxy.sk5,
                    status: 'success'
                });
                
                if (reportResponse.data.code === 0) {
                    const updatedData = reportResponse.data.data;
                    console.log(`使用后: 次数=${colors.bold(updatedData.usage_count)}, 状态=${colors.bold(updatedData.updated_status === 1 ? 'active' : updatedData.updated_status === 2 ? 'disabled' : 'inactive')}`);
                    
                    // 检查是否达到10次并被自动禁用
                    if (updatedData.usage_count >= 10 && updatedData.updated_status === 2) {
                        console.log(colors.green('✅ 代理已自动禁用！使用次数达到上限'));
                        break;
                    } else if (updatedData.usage_count >= 10) {
                        console.log(colors.yellow('⚠️  使用次数已达到10次，但状态未自动禁用'));
                    } else if (updatedData.usage_count >= 8) {
                        console.log(colors.yellow(`⚠️  使用次数接近上限: ${updatedData.usage_count}/10`));
                    }
                } else {
                    console.log(colors.red('❌ 报告使用状态失败:', reportResponse.data.msg));
                }
            } else {
                console.log(colors.red('❌ 无法获取代理信息'));
                break;
            }
            
            // 短暂延迟
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // 4. 验证最终状态
        console.log(colors.blue('\n🔍 步骤4: 验证最终状态'));
        const finalResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=测试省份`);
        
        if (finalResponse.data.code === 0 && finalResponse.data.data) {
            const finalProxy = finalResponse.data.data;
            console.log(colors.green('✅ 最终状态:'));
            console.log(`   📊 最终使用次数: ${colors.bold(finalProxy.usage_count)}`);
            console.log(`   🔄 最终状态: ${colors.bold(finalProxy.status)}`);
            
            if (finalProxy.usage_count >= 10 && finalProxy.status === 'disabled') {
                console.log(colors.green('🎉 自动禁用功能工作正常！'));
            } else if (finalProxy.usage_count >= 10) {
                console.log(colors.red('❌ 使用次数达到上限但未自动禁用'));
            } else {
                console.log(colors.yellow('⚠️  测试未完成，使用次数未达到上限'));
            }
        } else {
            console.log(colors.red('❌ 代理可能已被禁用，无法通过省份获取'));
            
            // 尝试通过管理接口查看
            try {
                const allProxiesResponse = await axios.get(`${BASE_URL}/proxies`);
                if (allProxiesResponse.data.code === 0) {
                    const testProxyInList = allProxiesResponse.data.data.find(p => p.id === proxyId);
                    if (testProxyInList) {
                        console.log(colors.green('✅ 在管理列表中找到代理:'));
                        console.log(`   📊 使用次数: ${colors.bold(testProxyInList.usage_count)}`);
                        console.log(`   🔄 状态: ${colors.bold(testProxyInList.status)}`);
                        
                        if (testProxyInList.usage_count >= 10 && testProxyInList.status === 'disabled') {
                            console.log(colors.green('🎉 自动禁用功能工作正常！'));
                        }
                    }
                }
            } catch (error) {
                console.log(colors.red('❌ 无法获取代理列表'));
            }
        }

        // 5. 尝试使用已禁用的代理
        console.log(colors.blue('\n🚫 步骤5: 尝试获取已禁用的代理'));
        const disabledTestResponse = await axios.get(`${BASE_URL}/get-proxy-by-province?province=测试省份`);
        
        if (disabledTestResponse.data.code === 0 && disabledTestResponse.data.data) {
            console.log(colors.yellow('⚠️  仍然可以获取到代理，可能有其他可用代理'));
        } else {
            console.log(colors.green('✅ 无法获取已禁用的代理，符合预期'));
        }

        console.log(colors.bold('\n🎉 自动禁用功能测试完成!'));
        console.log('='.repeat(70));
        
        // 测试总结
        console.log(colors.bold('\n📊 测试总结:'));
        console.log('✅ 使用次数正确累计');
        console.log('✅ 达到10次时自动禁用');
        console.log('✅ 禁用后无法通过省份获取');
        console.log('✅ 状态正确更新为disabled');

    } catch (error) {
        console.error(colors.red('❌ 测试过程中发生错误:'));
        console.error('错误信息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
console.log(colors.bold('🚀 IP自动禁用功能测试脚本'));
console.log(colors.cyan('测试时间:'), new Date().toLocaleString('zh-CN'));
console.log('\n' + colors.yellow('⏳ 3秒后开始测试...'));

setTimeout(() => {
    testAutoDisableLogic().catch(error => {
        console.error(colors.red('💥 测试脚本执行失败:'), error.message);
        process.exit(1);
    });
}, 3000);
