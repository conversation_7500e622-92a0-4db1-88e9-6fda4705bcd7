const express = require('express')
const router = express.Router();
let multiparty = require("multiparty")
let fs = require("fs")
const { publicPath } = require('../serverOpt');

// 上传文件并更新version文件版本信息
router.post('/localUpload', function (req, res) {
    // console.log(req.headers);
    // console.log(req.body);
    let form = new multiparty.Form();
    // 设置文件存储路径，以当前编辑的文件为相对路径
    form.uploadDir = publicPath + "/static";
    if (!fs.existsSync(publicPath + "/static")) {
        fs.mkdirSync(publicPath + "/static");
    }
    // 设置文件大小限制
    form.maxFilesSize = 2000 * 1024 * 1024;

    // 确保static目录存在且有权限
    try {
        if (!fs.existsSync(publicPath + "/static")) {
            fs.mkdirSync(publicPath + "/static", { recursive: true });
        }
        // 测试目录权限
        fs.accessSync(publicPath + "/static", fs.constants.R_OK | fs.constants.W_OK);
    } catch (dirErr) {
        console.error('目录权限错误:', dirErr);
        return res.status(500).json({
            status: 'error',
            msg: '服务器目录配置错误: ' + dirErr.message
        });
    }

    form.parse(req, function (err, fields, files) {
        try {
            console.log('收到上传请求，文件信息:', files);
            
            if (!files || !files.files || files.files.length === 0) {
                throw new Error('未接收到上传文件');
            }

            let results = [];
            let versionUpdated = false;
            let oldVersion = '0'; // 将oldVersion定义移到外层
            
            // 处理每个上传的文件
            for (let i = 0; i < files.files.length; i++) {
                let inputFile = files.files[i];
                console.log('处理文件:', inputFile.originalFilename);
                
                let newPath = form.uploadDir + "/" + inputFile.originalFilename;
                
                // 确保文件移动成功
                fs.renameSync(inputFile.path, newPath);
                if (!fs.existsSync(newPath)) {
                    throw new Error(`文件 ${inputFile.originalFilename} 保存失败`);
                }

                // 构建可直接访问的完整URL
                const serverBaseUrl = 'https://14.103.181.241:16415';
                const fileUrl = `${serverBaseUrl}/static/${inputFile.originalFilename}`;
                
                results.push({
                    filename: inputFile.originalFilename,
                    path: newPath,
                    url: fileUrl, // 返回完整服务器路径
                    localPath: `/static/${inputFile.originalFilename}` ,// 保留相对路径
                    status: 'success'
                });
            }

            // 更新版本号(只更新一次)
            if (files.files.length > 0) {
                const versionFile = publicPath + '/static/version.txt';
                try {
                    if (fs.existsSync(versionFile)) {
                        oldVersion = fs.readFileSync(versionFile, 'utf-8').trim() || '0';
                    }
                    fs.writeFileSync(versionFile, (Number(oldVersion) + 1) + "");
                    versionUpdated = true;
                } catch (versionErr) {
                    console.error('版本文件错误:', versionErr);
                    // 版本更新失败不影响文件上传
                    versionUpdated = false;
                }
            }
            
            // 返回完整路径但统一格式
            const filePaths = results.map(file => 
                file.path.replace(/\\/g, '/') // 统一使用正斜杠
                         .replace(publicPath.replace(/\\/g, '/'), '') // 保留从项目根目录开始的路径
            );
            res.json({
                status: versionUpdated ? 'success' : 'partial_success',
                paths: filePaths, // 纯数组格式
                count: filePaths.length
            });
        } catch (error) {
            console.error('上传失败:', error);
            res.status(500).json({
                status: 'error',
                msg: '上传失败: ' + error.message
            });
        }
    });
})

module.exports = router;