const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 直接用SQL修复日期问题
async function fixDateSQL() {
  console.log('🔧 直接用SQL修复日期问题...');
  
  const testUserId = '1001';
  
  console.log(`\n📋 用户ID: ${testUserId}`);
  console.log('─'.repeat(60));
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查当前时区和日期
    console.log('\n1️⃣ 检查当前时区和日期...');
    const [timeInfo] = await connection.execute(`SELECT NOW() as current_time, CURDATE() as current_date`);
    console.log('数据库当前时间:', timeInfo[0]);
    
    // 2. 删除所有旧的统计数据
    console.log('\n2️⃣ 删除所有旧的统计数据...');
    const [deleteResult] = await connection.execute(
      `DELETE FROM promotion_daily_stats WHERE promotion_user_id = ?`,
      [testUserId]
    );
    console.log(`删除了 ${deleteResult.affectedRows} 条旧记录`);
    
    // 3. 使用CURDATE()创建今日数据
    console.log('\n3️⃣ 使用CURDATE()创建今日数据...');
    
    // 获取历史总数据
    const [totalVisitStats] = await connection.execute(
      `SELECT
        COUNT(*) as visit_count,
        COUNT(DISTINCT visitor_ip) as unique_ip_count
       FROM promotion_visits
       WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    const [totalActionStats] = await connection.execute(
      `SELECT
        COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
        COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
        COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count,
        COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as expire_count
       FROM promotion_actions
       WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    const visitCount = totalVisitStats[0]?.visit_count || 0;
    const uniqueIpCount = totalVisitStats[0]?.unique_ip_count || 0;
    const scanCount = totalActionStats[0]?.scan_count || 0;
    const successCount = totalActionStats[0]?.success_count || 0;
    const failCount = totalActionStats[0]?.fail_count || 0;
    const expireCount = totalActionStats[0]?.expire_count || 0;
    
    console.log('准备插入的数据:', {
      visitCount, uniqueIpCount, scanCount, successCount, failCount, expireCount
    });
    
    // 使用CURDATE()插入今日数据
    const [insertResult] = await connection.execute(
      `INSERT INTO promotion_daily_stats 
       (promotion_user_id, stat_date, visit_count, unique_ip_count, 
        scan_count, success_count, fail_count, expire_count, created_at, updated_at)
       VALUES (?, CURDATE(), ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [testUserId, visitCount, uniqueIpCount, scanCount, successCount, failCount, expireCount]
    );
    
    console.log(`✅ 今日统计数据已创建，插入ID: ${insertResult.insertId}`);
    
    // 4. 验证创建结果
    console.log('\n4️⃣ 验证创建结果...');
    const [verifyStats] = await connection.execute(
      `SELECT *, DATE(stat_date) as date_only FROM promotion_daily_stats 
       WHERE promotion_user_id = ? AND stat_date = CURDATE()`,
      [testUserId]
    );
    
    if (verifyStats.length > 0) {
      console.log('✅ 验证成功！今日统计数据:');
      console.log('   数据:', JSON.stringify(verifyStats[0], null, 2));
      
      console.log('\n📊 统计摘要:');
      console.log(`   日期: ${verifyStats[0].date_only}`);
      console.log(`   访问次数: ${verifyStats[0].visit_count}`);
      console.log(`   独立IP数: ${verifyStats[0].unique_ip_count}`);
      console.log(`   扫码数量: ${verifyStats[0].scan_count}`);
      console.log(`   成功数量: ${verifyStats[0].success_count}`);
      console.log(`   失败数量: ${verifyStats[0].fail_count}`);
      console.log(`   过期数量: ${verifyStats[0].expire_count}`);
      
    } else {
      console.log('❌ 验证失败，今日统计数据不存在');
    }
    
    // 5. 测试API查询逻辑
    console.log('\n5️⃣ 测试API查询逻辑...');
    
    // 模拟API中的查询（使用当前日期）
    const today = new Date().toISOString().split('T')[0];
    console.log(`API查询日期: ${today}`);
    
    const [apiTestStats] = await connection.execute(
      `SELECT
        visit_count,
        unique_ip_count,
        scan_count,
        success_count,
        fail_count,
        expire_count
       FROM promotion_daily_stats
       WHERE promotion_user_id = ? AND stat_date = ?`,
      [testUserId, today]
    );
    
    if (apiTestStats.length > 0) {
      console.log('✅ API查询测试成功:');
      const apiData = apiTestStats[0];
      console.log(`   API返回数据: {`);
      console.log(`     visit_count: ${parseInt(apiData.visit_count) || 0},`);
      console.log(`     unique_ip_count: ${parseInt(apiData.unique_ip_count) || 0},`);
      console.log(`     scan_count: ${parseInt(apiData.scan_count) || 0},`);
      console.log(`     success_count: ${parseInt(apiData.success_count) || 0},`);
      console.log(`     fail_count: ${parseInt(apiData.fail_count) || 0},`);
      console.log(`     expire_count: ${parseInt(apiData.expire_count) || 0}`);
      console.log(`   }`);
    } else {
      console.log('❌ API查询测试失败，尝试其他查询方式...');
      
      // 尝试使用CURDATE()查询
      const [apiTestStats2] = await connection.execute(
        `SELECT
          visit_count,
          unique_ip_count,
          scan_count,
          success_count,
          fail_count,
          expire_count
         FROM promotion_daily_stats
         WHERE promotion_user_id = ? AND stat_date = CURDATE()`,
        [testUserId]
      );
      
      if (apiTestStats2.length > 0) {
        console.log('✅ 使用CURDATE()查询成功:');
        const apiData = apiTestStats2[0];
        console.log(`   数据: {`);
        console.log(`     visit_count: ${parseInt(apiData.visit_count) || 0},`);
        console.log(`     unique_ip_count: ${parseInt(apiData.unique_ip_count) || 0},`);
        console.log(`     scan_count: ${parseInt(apiData.scan_count) || 0},`);
        console.log(`     success_count: ${parseInt(apiData.success_count) || 0},`);
        console.log(`     fail_count: ${parseInt(apiData.fail_count) || 0},`);
        console.log(`     expire_count: ${parseInt(apiData.expire_count) || 0}`);
        console.log(`   }`);
        
        console.log('\n💡 建议修改API查询使用CURDATE()而不是字符串日期');
      } else {
        console.log('❌ 所有查询方式都失败了');
      }
    }
    
    // 6. 显示所有数据用于调试
    console.log('\n6️⃣ 显示所有数据用于调试...');
    const [allStats] = await connection.execute(
      `SELECT id, promotion_user_id, stat_date, DATE(stat_date) as date_only,
              visit_count, unique_ip_count, scan_count, success_count, fail_count, expire_count
       FROM promotion_daily_stats 
       WHERE promotion_user_id = ?
       ORDER BY stat_date DESC`,
      [testUserId]
    );
    
    console.log('所有统计数据:');
    allStats.forEach((stat, index) => {
      console.log(`   ${index + 1}. ID=${stat.id}, 日期=${stat.date_only}, 访问=${stat.visit_count}, 成功=${stat.success_count}`);
    });
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
  
  console.log('\n🎉 日期修复完成！');
  
  console.log('\n🌐 现在可以测试:');
  console.log('1. 访问推广用户登录页面: http://localhost:15001/page/promotion/promoter-login.html');
  console.log('2. 使用用户ID: 1001, 密码: 123456 登录');
  console.log('3. 应该能看到正确的统计数据');
  console.log('4. 如果仍然显示0，可能需要修改API查询逻辑');
}

// 运行修复
if (require.main === module) {
  fixDateSQL().catch(console.error);
}

module.exports = fixDateSQL;
