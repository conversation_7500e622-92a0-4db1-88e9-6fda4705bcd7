const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 修复数据库数据
async function fixDatabaseData() {
  console.log('🔧 修复数据库数据...');
  
  const testUserId = '1001';
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查当前数据
    console.log('\n1️⃣ 检查当前数据...');
    const [currentStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    console.log(`找到 ${currentStats.length} 条记录`);
    
    // 2. 删除所有旧数据
    console.log('\n2️⃣ 清理旧数据...');
    const [deleteResult] = await connection.execute(
      `DELETE FROM promotion_daily_stats WHERE promotion_user_id = ?`,
      [testUserId]
    );
    console.log(`删除了 ${deleteResult.affectedRows} 条记录`);
    
    // 3. 插入今日数据
    console.log('\n3️⃣ 插入今日数据...');
    const [insertResult] = await connection.execute(
      `INSERT INTO promotion_daily_stats 
       (promotion_user_id, stat_date, visit_count, unique_ip_count, 
        scan_count, success_count, fail_count, expire_count)
       VALUES (?, CURDATE(), 10, 3, 2, 8, 1, 0)`,
      [testUserId]
    );
    
    console.log(`✅ 插入成功，ID: ${insertResult.insertId}`);
    
    // 4. 验证数据
    console.log('\n4️⃣ 验证插入的数据...');
    const [verifyStats] = await connection.execute(
      `SELECT 
        visit_count,
        unique_ip_count,
        scan_count,
        success_count,
        fail_count,
        expire_count
       FROM promotion_daily_stats
       WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    if (verifyStats.length > 0) {
      console.log('✅ 验证成功，数据:');
      const stats = verifyStats[0];
      console.log(`   访问次数: ${stats.visit_count}`);
      console.log(`   独立IP数: ${stats.unique_ip_count}`);
      console.log(`   扫码数量: ${stats.scan_count}`);
      console.log(`   成功数量: ${stats.success_count}`);
      console.log(`   失败数量: ${stats.fail_count}`);
      console.log(`   过期数量: ${stats.expire_count}`);
      
      // 检查是否有非零数据
      const hasData = Object.values(stats).some(val => val > 0);
      console.log(`   有非零数据: ${hasData}`);
      
    } else {
      console.log('❌ 验证失败，没有找到数据');
    }
    
    // 5. 测试API查询
    console.log('\n5️⃣ 测试API查询逻辑...');
    const [apiResult] = await connection.execute(
      `SELECT
        visit_count,
        unique_ip_count,
        scan_count,
        success_count,
        fail_count,
        expire_count
       FROM promotion_daily_stats
       WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    if (apiResult.length > 0) {
      console.log('✅ API查询成功:');
      const apiStats = apiResult[0];
      console.log('   API返回数据:', {
        visit_count: parseInt(apiStats.visit_count) || 0,
        unique_ip_count: parseInt(apiStats.unique_ip_count) || 0,
        scan_count: parseInt(apiStats.scan_count) || 0,
        success_count: parseInt(apiStats.success_count) || 0,
        fail_count: parseInt(apiStats.fail_count) || 0,
        expire_count: parseInt(apiStats.expire_count) || 0,
      });
    } else {
      console.log('❌ API查询失败，没有找到数据');
    }
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
  
  console.log('\n🎉 数据修复完成！');
  console.log('\n🌐 现在请重新测试推广用户登录界面，应该能看到正确的数据了：');
  console.log('   访问次数: 10');
  console.log('   独立IP数: 3');
  console.log('   成功数量: 8');
}

// 运行修复
fixDatabaseData().catch(console.error);
