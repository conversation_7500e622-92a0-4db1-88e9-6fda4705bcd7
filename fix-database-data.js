const mysql = require("mysql2/promise");

// 数据库配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
};

// 检查和修复推广数据
async function fixDatabaseData() {
  console.log("🔧 检查和修复推广数据...");

  const testUserId = "1001";

  let connection;

  try {
    connection = await mysql.createConnection(dbConfig);
    console.log("✅ 数据库连接成功");

    // 1. 检查promotion_daily_stats表
    console.log("\n1️⃣ 检查promotion_daily_stats表...");
    const [dailyStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats WHERE promotion_user_id = ? ORDER BY stat_date DESC`,
      [testUserId]
    );

    console.log(`找到 ${dailyStats.length} 条每日统计记录:`);
    dailyStats.forEach((stat, index) => {
      console.log(
        `   ${index + 1}. 日期=${stat.stat_date}, 访问=${
          stat.visit_count
        }, 成功=${stat.success_count}, IP=${stat.unique_ip_count}`
      );
    });

    // 2. 检查promotion_visits表
    console.log("\n2️⃣ 检查promotion_visits表...");
    const [visits] = await connection.execute(
      `SELECT COUNT(*) as total, COUNT(DISTINCT visitor_ip) as unique_ips,
              MIN(visit_time) as first_visit, MAX(visit_time) as last_visit
       FROM promotion_visits WHERE promotion_user_id = ?`,
      [testUserId]
    );

    console.log("访问统计:");
    if (visits.length > 0) {
      const visitData = visits[0];
      console.log(`   总访问次数: ${visitData.total}`);
      console.log(`   独立IP数: ${visitData.unique_ips}`);
      console.log(`   首次访问: ${visitData.first_visit}`);
      console.log(`   最后访问: ${visitData.last_visit}`);
    }

    // 3. 检查promotion_actions表
    console.log("\n3️⃣ 检查promotion_actions表...");
    const [actions] = await connection.execute(
      `SELECT action_type, COUNT(*) as count
       FROM promotion_actions
       WHERE promotion_user_id = ?
       GROUP BY action_type`,
      [testUserId]
    );

    console.log("操作统计:");
    actions.forEach((action) => {
      console.log(`   ${action.action_type}: ${action.count}次`);
    });

    // 4. 如果没有数据，创建测试数据
    if (dailyStats.length === 0 && visits[0].total === 0) {
      console.log("\n4️⃣ 没有数据，创建测试数据...");

      // 插入访问记录
      await connection.execute(
        `INSERT INTO promotion_visits (promotion_user_id, visitor_ip, ip_province, user_agent, referer, visit_time)
         VALUES
         (?, '*************', '北京市', 'Mozilla/5.0', '', NOW() - INTERVAL 1 HOUR),
         (?, '*************', '上海市', 'Mozilla/5.0', '', NOW() - INTERVAL 2 HOUR),
         (?, '*************', '广东省', 'Mozilla/5.0', '', NOW() - INTERVAL 3 HOUR),
         (?, '*************', '北京市', 'Mozilla/5.0', '', NOW() - INTERVAL 30 MINUTE)`,
        [testUserId, testUserId, testUserId, testUserId]
      );

      // 插入操作记录
      await connection.execute(
        `INSERT INTO promotion_actions (promotion_user_id, action_type, ip_address, ip_province, action_time)
         VALUES
         (?, 'scan', '*************', '北京市', NOW() - INTERVAL 1 HOUR),
         (?, 'login_success', '*************', '北京市', NOW() - INTERVAL 50 MINUTE),
         (?, 'scan', '*************', '上海市', NOW() - INTERVAL 2 HOUR),
         (?, 'login_success', '*************', '上海市', NOW() - INTERVAL 110 MINUTE),
         (?, 'login_fail', '*************', '广东省', NOW() - INTERVAL 3 HOUR)`,
        [testUserId, testUserId, testUserId, testUserId, testUserId]
      );

      console.log("✅ 基础数据创建完成");
    }

    // 5. 更新或创建今日统计数据
    console.log("\n5️⃣ 更新今日统计数据...");

    // 实时计算今日数据
    const [todayVisits] = await connection.execute(
      `SELECT COUNT(*) as visit_count, COUNT(DISTINCT visitor_ip) as unique_ip_count
       FROM promotion_visits
       WHERE promotion_user_id = ? AND DATE(visit_time) = CURDATE()`,
      [testUserId]
    );

    const [todayActions] = await connection.execute(
      `SELECT
        COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
        COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
        COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count,
        COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as expire_count
       FROM promotion_actions
       WHERE promotion_user_id = ? AND DATE(action_time) = CURDATE()`,
      [testUserId]
    );

    const visitStats = todayVisits[0];
    const actionStats = todayActions[0];

    // 插入或更新今日统计
    await connection.execute(
      `INSERT INTO promotion_daily_stats
       (promotion_user_id, stat_date, visit_count, unique_ip_count, scan_count, success_count, fail_count, expire_count)
       VALUES (?, CURDATE(), ?, ?, ?, ?, ?, ?)
       ON DUPLICATE KEY UPDATE
       visit_count = VALUES(visit_count),
       unique_ip_count = VALUES(unique_ip_count),
       scan_count = VALUES(scan_count),
       success_count = VALUES(success_count),
       fail_count = VALUES(fail_count),
       expire_count = VALUES(expire_count),
       updated_at = NOW()`,
      [
        testUserId,
        visitStats.visit_count,
        visitStats.unique_ip_count,
        actionStats.scan_count,
        actionStats.success_count,
        actionStats.fail_count,
        actionStats.expire_count,
      ]
    );

    console.log("✅ 今日统计数据已更新");

    // 6. 验证最终数据
    console.log("\n6️⃣ 验证最终数据...");
    const [finalStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );

    if (finalStats.length > 0) {
      const stats = finalStats[0];
      console.log("✅ 最终数据验证:");
      console.log(`   访问次数: ${stats.visit_count}`);
      console.log(`   独立IP数: ${stats.unique_ip_count}`);
      console.log(`   扫码数量: ${stats.scan_count}`);
      console.log(`   成功数量: ${stats.success_count}`);
      console.log(`   失败数量: ${stats.fail_count}`);
      console.log(`   过期数量: ${stats.expire_count}`);

      const hasData = Object.values(stats).some((val) => val > 0);
      console.log(`   有非零数据: ${hasData}`);
    }
  } catch (error) {
    console.error("❌ 修复失败:", error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }

  console.log("\n🎉 数据检查和修复完成！");
}

// 运行修复
fixDatabaseData().catch(console.error);
