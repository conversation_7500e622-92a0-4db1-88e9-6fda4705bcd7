<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>


<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input
                            id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>

                <br>

                <fieldset>
                    <legend>【养号】功能</legend>


                    <div class="layui-form-item">
                        <label class="layui-form-label">评论关键词</label>
                        <div class="layui-input-block">
                            <textarea name="云评论关键词" placeholder="例如:你好|在吗|干嘛呢" class="layui-textarea"></textarea>
                            <tip>多句用|分割</tip>
                        </div>
                    </div>


                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="抖音养号任务"
                        lay-filter="tijiao">dy养号任务</button>
                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="停止" lay-filter="tijiao">停止</button>
                </fieldset>

                <br>

                <fieldset>
                    <legend>【一键清后台】功能</legend>

                    <button class="layui-btn  layui-btn-sm" lay-submit="" value="一键清后台"
                        lay-filter="tijiao">一键清后台</button>
                </fieldset>



            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong
            tongYong.tongYong1()
        });
    </script>
</body>

</html>