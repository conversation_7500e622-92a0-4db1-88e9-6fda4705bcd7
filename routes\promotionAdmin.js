const express = require("express");
const router = express.Router();
const mysql = require("mysql2/promise");
const multer = require("multer");
const fs = require("fs");

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
  connectionLimit: 10,
  waitForConnections: true,
  queueLimit: 0,
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 检查管理员登录状态中间件
function requireAdminAuth(req, res, next) {
  if (!req.session.adminUser) {
    return res.json({
      code: -1,
      msg: "请先登录管理员账户",
    });
  }
  next();
}

// 配置文件上传
const upload = multer({
  dest: "uploads/",
  fileFilter: (req, file, cb) => {
    if (file.mimetype === "text/csv" || file.originalname.endsWith(".csv")) {
      cb(null, true);
    } else {
      cb(new Error("只支持CSV文件格式"));
    }
  },
});

// ==================== 管理员查看所有用户数据 ====================

// 获取所有推广用户的统计汇总（移除管理员认证要求）
router.get("/admin/all-users-stats", async (req, res) => {
  try {
    const { date } = req.query;
    const targetDate = date || new Date().toISOString().split("T")[0];

    const connection = await pool.getConnection();

    // 获取所有推广用户的统计数据（从user表读取）
    const [stats] = await connection.execute(
      `SELECT
        promoter_id as user_id,
        username,
        COALESCE(visit_count, 0) as visit_count,
        COALESCE(unique_ip_count, 0) as unique_ip_count,
        COALESCE(scan_count, 0) as scan_count,
        COALESCE(success_count, 0) as success_count,
        COALESCE(fail_count, 0) as fail_count,
        COALESCE(expire_count, 0) as expire_count
       FROM user
       WHERE user_type = 'promoter' AND status = 1
       ORDER BY promoter_id`
    );

    connection.release();

    res.json({
      code: 0,
      msg: "获取成功",
      data: {
        date: targetDate,
        stats: stats,
      },
    });
  } catch (error) {
    console.error("获取所有用户统计失败:", error);
    res.json({
      code: -1,
      msg: "获取失败: " + error.message,
    });
  }
});

// 获取所有推广用户的详细操作记录（包含CK）（移除管理员认证要求）
router.get("/admin/all-users-actions", async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      date,
      promotion_user_id,
      action_type,
    } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const targetDate = date || new Date().toISOString().split("T")[0];

    let whereClause = "WHERE DATE(pa.action_time) = ?";
    let params = [targetDate];

    if (promotion_user_id) {
      whereClause += " AND pa.promotion_user_id = ?";
      params.push(promotion_user_id);
    }

    if (action_type) {
      whereClause += " AND pa.action_type = ?";
      params.push(action_type);
    }

    const connection = await pool.getConnection();

    const [actions] = await connection.execute(
      `SELECT
        pa.promotion_user_id,
        pa.action_type,
        pa.ip_address,
        pa.ip_province,
        pa.douyin_name,
        pa.douyin_id,
        pa.ck_data,
        DATE_FORMAT(pa.action_time, '%H:%i:%s') as action_time,
        u.username as promoter_name
       FROM promotion_actions pa
       LEFT JOIN user u ON pa.promotion_user_id = u.promoter_id AND u.user_type = 'promoter'
       ${whereClause}
       ORDER BY pa.action_time DESC
       LIMIT ? OFFSET ?`,
      [...params, parseInt(limit), offset]
    );

    const [countResult] = await connection.execute(
      `SELECT COUNT(*) as total FROM promotion_actions pa ${whereClause}`,
      params
    );

    connection.release();

    // 转换action_type为中文
    const actionTypeMap = {
      scan: "扫码",
      login_success: "登录成功",
      login_fail: "登录失败",
      request_expire: "请求过期",
    };

    const formattedActions = actions.map((action) => ({
      promotion_user_id: action.promotion_user_id,
      promoter_name: action.promoter_name,
      time: action.action_time,
      ip: action.ip_address,
      ip_province: action.ip_province || "-",
      status: actionTypeMap[action.action_type] || action.action_type,
      douyin_name: action.douyin_name || "-",
      douyin_id: action.douyin_id || "-",
      ck: action.ck_data || "-",
    }));

    res.json({
      code: 0,
      msg: "获取成功",
      data: {
        date: targetDate,
        list: formattedActions,
        total: countResult[0].total,
        page: parseInt(page),
        limit: parseInt(limit),
      },
    });
  } catch (error) {
    console.error("获取所有用户操作记录失败:", error);
    res.json({
      code: -1,
      msg: "获取失败: " + error.message,
    });
  }
});

// 导出数据为CSV
router.get("/admin/export-data", requireAdminAuth, async (req, res) => {
  try {
    const { date, type = "actions" } = req.query;
    const targetDate = date || new Date().toISOString().split("T")[0];

    const connection = await pool.getConnection();

    if (type === "stats") {
      // 导出统计数据
      const [stats] = await connection.execute(
        `SELECT 
          pu.user_id as '推广用户ID',
          pu.username as '推广用户名',
          COALESCE(pds.visit_count, 0) as '访问次数',
          COALESCE(pds.unique_ip_count, 0) as '独立IP数',
          COALESCE(pds.scan_count, 0) as '扫码数量',
          COALESCE(pds.success_count, 0) as '成功数量',
          COALESCE(pds.fail_count, 0) as '失败数量',
          COALESCE(pds.expire_count, 0) as '过期数量'
         FROM promotion_users pu
         LEFT JOIN promotion_daily_stats pds ON pu.user_id = pds.promotion_user_id AND pds.stat_date = ?
         WHERE pu.status = 1
         ORDER BY pu.user_id`,
        [targetDate]
      );

      connection.release();

      // 生成CSV内容
      const csvHeader = Object.keys(stats[0] || {}).join(",") + "\n";
      const csvContent = stats
        .map((row) => Object.values(row).join(","))
        .join("\n");

      res.setHeader("Content-Type", "text/csv; charset=utf-8");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=promotion_stats_${targetDate}.csv`
      );
      res.send("\uFEFF" + csvHeader + csvContent); // 添加BOM以支持中文
    } else {
      // 导出操作记录
      const [actions] = await connection.execute(
        `SELECT
          pa.promotion_user_id as '推广用户ID',
          u.username as '推广用户名',
          pa.target_user_id as '用户ID',
          DATE_FORMAT(pa.action_time, '%H:%i:%s') as '时间',
          pa.ip_address as 'IP',
          COALESCE(pa.ip_province, '-') as 'IP省份',
          CASE pa.action_type
            WHEN 'scan' THEN '扫码'
            WHEN 'login_success' THEN '登录成功'
            WHEN 'login_fail' THEN '登录失败'
            WHEN 'request_expire' THEN '请求过期'
            ELSE pa.action_type
          END as '状态',
          COALESCE(pa.douyin_name, '-') as '抖音名',
          COALESCE(pa.douyin_id, '-') as '抖音号',
          COALESCE(pa.ck_data, '-') as 'CK'
         FROM promotion_actions pa
         LEFT JOIN user u ON pa.promotion_user_id = u.promoter_id AND u.user_type = 'promoter'
         WHERE DATE(pa.action_time) = ?
         ORDER BY pa.action_time DESC`,
        [targetDate]
      );

      connection.release();

      // 生成CSV内容
      const csvHeader = Object.keys(actions[0] || {}).join(",") + "\n";
      const csvContent = actions
        .map((row) => Object.values(row).join(","))
        .join("\n");

      res.setHeader("Content-Type", "text/csv; charset=utf-8");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=promotion_actions_${targetDate}.csv`
      );
      res.send("\uFEFF" + csvHeader + csvContent); // 添加BOM以支持中文
    }
  } catch (error) {
    console.error("导出数据失败:", error);
    res.json({
      code: -1,
      msg: "导出失败: " + error.message,
    });
  }
});

module.exports = router;
