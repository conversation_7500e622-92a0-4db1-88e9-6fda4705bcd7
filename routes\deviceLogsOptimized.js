const express = require('express');
const router = express.Router();
const { pool } = require('../db/mysql');
const debug = require('debug')('a-sokio-yun:device-logs');
const crypto = require('crypto');

// 短期缓存配置
const cache = new Map();
const CACHE_TTL = 5000; // 5秒缓存

// 响应格式中间件
router.use((req, res, next) => {
    res.apiSuccess = (data, message = 'success') => {
        res.json({ code: 0, msg: message, data });
    };
    res.apiError = (message = 'error', code = -1) => {
        res.status(500).json({ code, msg: message, data: null });
    };
    next();
});

/**
 * 设备日志查询
 * GET /deviceLogs
 * 参数:
 *   - page: 页码(默认1)
 *   - limit: 每页数量(默认20)
 *   - device_id: 设备ID(可选)
 */
router.get('/', async (req, res) => {
    const { page = 1, limit = 20, device_id } = req.query;
    const offset = (Math.max(1, page) - 1) * limit;
    
    // 生成缓存键
    const cacheKey = `logs:${device_id || 'all'}:${page}:${limit}`;
    
    // 检查缓存
    if (cache.has(cacheKey)) {
        const cached = cache.get(cacheKey);
        if (Date.now() - cached.timestamp < CACHE_TTL) {
            debug('从缓存返回数据');
            res.set('ETag', cached.etag);
            return res.status(304).end();
        }
    }
    
    try {
        // 获取数据库连接
        const connection = await pool.getConnection();
        debug('获取数据库连接成功');
        
        try {
            // 构建查询条件
            const queryParams = [];
            let whereClause = '';
            
            if (device_id) {
                whereClause += ' AND device_id = ?';
                queryParams.push(device_id);
            }
            
            // 并行执行查询
            const [dataQuery, countQuery] = await Promise.all([
                connection.query(
                    `SELECT 
                        id,
                        device_id,
                        status,
                        execution_data,
                        created_at
                    FROM device_logs
                    ${whereClause ? 'WHERE ' + whereClause.substring(5) : ''}
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?`,
                    [...queryParams, parseInt(limit), offset]
                ),
                connection.query(
                    `SELECT COUNT(*) AS total FROM device_logs
                    ${whereClause ? 'WHERE ' + whereClause.substring(5) : ''}`,
                    queryParams
                )
            ]);
            
            const [rows] = dataQuery;
            const [[{ total }]] = countQuery;
            
            debug(`查询到${rows.length}条记录，总数: ${total}`);
            
            // 生成ETag
            const etag = crypto
                .createHash('md5')
                .update(JSON.stringify(rows))
                .digest('hex');
            
            // 设置缓存
            cache.set(cacheKey, {
                data: rows,
                total,
                etag,
                timestamp: Date.now()
            });
            
            // 设置响应头
            res.set('ETag', etag);
            
            // 返回结果
            res.apiSuccess({
                list: rows,
                pagination: {
                    total: Number(total),
                    page: Number(page),
                    limit: Number(limit),
                    pages: Math.ceil(total / limit)
                }
            });
        } finally {
            connection.release();
            debug('释放数据库连接');
        }
    } catch (err) {
        debug('查询失败:', err);
        res.apiError(err.message);
    }
});

module.exports = router;
