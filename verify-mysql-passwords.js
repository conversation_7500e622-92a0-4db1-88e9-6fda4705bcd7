const fs = require('fs');
const path = require('path');

// 需要检查的文件列表
const filesToCheck = [
    'config.json',
    'db/mysql.js',
    'routes/douyinAccount.js',
    'routes/douyinIP.js',
    'test-douyin-api.js',
    'test-mysql-connection.js'
];

console.log('🔍 验证MySQL密码配置');
console.log('='.repeat(50));

let allCorrect = true;

filesToCheck.forEach(filePath => {
    console.log(`\n📁 检查文件: ${filePath}`);
    
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`   ⚠️  文件不存在`);
            return;
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 查找密码配置
        const passwordMatches = content.match(/password['":\s]*['"](.*?)['"]/gi);
        
        if (passwordMatches) {
            passwordMatches.forEach(match => {
                console.log(`   🔑 找到密码配置: ${match}`);
                
                if (match.includes('123456')) {
                    console.log(`   ✅ 密码正确: 123456`);
                } else if (match.includes('""') || match.includes("''")) {
                    console.log(`   ⚠️  密码为空`);
                    allCorrect = false;
                } else {
                    console.log(`   ❌ 密码不是123456`);
                    allCorrect = false;
                }
            });
        } else {
            console.log(`   💡 未找到密码配置`);
        }
        
    } catch (error) {
        console.log(`   ❌ 读取文件失败: ${error.message}`);
        allCorrect = false;
    }
});

console.log('\n' + '='.repeat(50));
if (allCorrect) {
    console.log('✅ 所有MySQL密码配置都正确设置为123456');
} else {
    console.log('❌ 发现密码配置问题，请检查上述文件');
}

console.log('\n📋 当前配置总结:');
console.log('- config.json: 主配置文件');
console.log('- db/mysql.js: 主数据库配置');
console.log('- routes/douyinAccount.js: 抖音账号API');
console.log('- routes/douyinIP.js: 抖音IP代理API');
console.log('- 所有密码应该都是: 123456');

console.log('\n🚀 如果配置正确，现在可以启动服务:');
console.log('   node bin/ceshi');
