<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接登录测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; border-radius: 3px; margin: 10px 0; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🧪 直接登录测试</h1>
    
    <div class="test-section">
        <h3>登录测试</h3>
        <div>
            <input type="text" id="userId" placeholder="用户ID" value="1001">
            <input type="password" id="password" placeholder="密码" value="123456">
            <button onclick="testDirectLogin()">直接登录测试</button>
        </div>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>登录后测试用户信息</h3>
        <button onclick="testUserInfo()">测试用户信息</button>
        <div id="userInfoResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>登录后测试统计数据</h3>
        <button onclick="testStats()">测试统计数据</button>
        <div id="statsResult" class="result"></div>
    </div>

    <script>
        // 直接登录测试
        async function testDirectLogin() {
            const resultDiv = document.getElementById('loginResult');
            const userId = document.getElementById('userId').value;
            const password = document.getElementById('password').value;
            
            resultDiv.textContent = '正在测试登录...';
            
            try {
                console.log('🔄 发送登录请求:', { user_id: userId, password: password });
                
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: userId, password: password })
                });
                
                const data = await response.json();
                
                console.log('📥 登录API响应:', {
                    status: response.status,
                    data: data,
                    cookies: document.cookie
                });
                
                resultDiv.textContent = `登录测试结果:
状态码: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}
当前cookies: ${document.cookie}`;
                
                if (data.code === 0) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent += '\n\n✅ 登录成功！现在可以测试其他API了。';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent += `\n\n❌ 登录失败: ${data.msg}`;
                }
                
            } catch (error) {
                console.error('❌ 登录请求失败:', error);
                resultDiv.textContent = `登录测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试用户信息
        async function testUserInfo() {
            const resultDiv = document.getElementById('userInfoResult');
            resultDiv.textContent = '正在测试用户信息...';
            
            try {
                const response = await fetch('/api/promotion/promoter/user-info', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.textContent = `用户信息测试结果:
状态码: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}`;
                
                if (data.code === 0) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `用户信息测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试统计数据
        async function testStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.textContent = '正在测试统计数据...';
            
            try {
                const response = await fetch('/api/promotion/promoter/today-stats', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.textContent = `统计数据测试结果:
状态码: ${response.status}
响应数据: ${JSON.stringify(data, null, 2)}`;
                
                if (data.code === 0) {
                    const stats = data.data;
                    const hasData = Object.values(stats).some(val => val > 0);
                    
                    resultDiv.textContent += `

数据分析:
访问次数: ${stats.visit_count}
独立IP数: ${stats.unique_ip_count}
成功数量: ${stats.success_count}
是否有非零数据: ${hasData}`;
                    
                    if (hasData) {
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent += '\n\n⚠️ 所有数据都为0！';
                    }
                } else {
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `统计数据测试失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 页面加载时显示初始状态
        window.onload = function() {
            console.log('📋 直接登录测试页面已加载');
            console.log('当前cookies:', document.cookie);
        };
    </script>
</body>
</html>
