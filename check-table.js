const mysql = require('mysql2/promise');

async function checkTable() {
  try {
    const pool = mysql.createPool({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'douyin',
      charset: 'utf8mb4'
    });

    const connection = await pool.getConnection();
    
    // 查看表结构
    const [columns] = await connection.query('DESCRIBE user');
    console.log('user表结构:');
    console.table(columns);
    
    // 查看表数据
    const [rows] = await connection.query('SELECT * FROM user LIMIT 5');
    console.log('\nuser表数据:');
    console.table(rows);
    
    connection.release();
    await pool.end();
    
  } catch (err) {
    console.error('检查表失败:', err.message);
  }
}

checkTable();
