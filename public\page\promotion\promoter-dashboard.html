<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广数据统计</title>
    <link rel="stylesheet" href="../../lib/layui/css/layui.css">
    <style>
        body {
            background: #f5f5f5;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header .user-info {
            margin-top: 10px;
            font-size: 14px;
            opacity: 0.9;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .data-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .refresh-btn:hover {
            background: #5a6fd8;
        }

        .logout-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .today-date {
            text-align: center;
            margin: 20px 0;
            font-size: 16px;
            color: #666;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>推广数据统计</h1>
        <div class="user-info">
            <span id="userInfo">推广用户: 加载中...</span>
            <span style="margin-left: 20px;">今日数据</span>
        </div>
        <button class="logout-btn" onclick="logout()">退出登录</button>
    </div>

    <div class="container">
        <div class="today-date">
            <strong>统计日期: <span id="todayDate"></span></strong>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="visitCount">0</div>
                <div class="stat-label">访问次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uniqueIpCount">0</div>
                <div class="stat-label">独立IP数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="scanCount">0</div>
                <div class="stat-label">扫码数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successCount">0</div>
                <div class="stat-label">成功数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failCount">0</div>
                <div class="stat-label">失败数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="expireCount">0</div>
                <div class="stat-label">过期数量</div>
            </div>
        </div>

        <!-- 操作记录表格 -->
        <div class="data-table">
            <div class="table-header">
                <div class="table-title">今日操作记录</div>
                <button class="refresh-btn" onclick="loadActionList()">刷新数据</button>
            </div>
            <table class="layui-table" lay-skin="nob">
                <thead>
                    <tr>
                        <th>用户ID</th>
                        <th>时间</th>
                        <th>IP地址</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody id="actionTableBody">
                    <tr>
                        <td colspan="4" style="text-align: center; padding: 40px; color: #999;">
                            加载中...
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- 分页 -->
            <div id="pagination" style="text-align: center; padding: 20px;"></div>
        </div>
    </div>

    <script src="../../lib/layui/layui.js"></script>
    <script>
        layui.use(['layer', 'laypage'], function () {
            var layer = layui.layer;
            var laypage = layui.laypage;

            let currentPage = 1;
            const pageSize = 20;

            // 页面加载时初始化
            document.addEventListener('DOMContentLoaded', function () {
                console.log('🚀 推广用户仪表板页面加载完成，开始初始化数据...');

                // 显示今日日期
                const today = new Date();
                document.getElementById('todayDate').textContent = today.toLocaleDateString('zh-CN');

                // 加载用户信息
                console.log('📋 开始加载用户信息...');
                loadUserInfo();

                // 加载数据
                console.log('📊 开始加载统计数据...');
                loadStats();

                console.log('📝 开始加载操作记录...');
                loadActionList();

                // 设置定时刷新（每30秒）
                setInterval(() => {
                    loadStats();
                    loadActionList();
                }, 30000);
            });

            // 加载用户信息
            function loadUserInfo() {
                fetch('/api/promotion/promoter/user-info', {
                    method: 'GET',
                    credentials: 'include', // 确保包含cookies
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                    .then(response => response.json())
                    .then(result => {
                        console.log('用户信息API响应:', result);
                        if (result.code === 0) {
                            const userInfo = result.data;
                            document.getElementById('userInfo').textContent =
                                `推广用户: ${userInfo.username} (ID: ${userInfo.user_id})`;
                        } else if (result.code === -1 && result.msg.includes('登录')) {
                            // 未登录，跳转到登录页
                            console.log('用户未登录，跳转到登录页');
                            window.location.href = '/page/promotion/promoter-login.html';
                        } else {
                            console.error('获取用户信息失败:', result.msg);
                        }
                    })
                    .catch(error => {
                        console.error('加载用户信息失败:', error);
                    });
            }

            // 加载统计数据
            function loadStats() {
                fetch('/api/promotion/promoter/today-stats', {
                    method: 'GET',
                    credentials: 'include', // 确保包含cookies
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                    .then(response => response.json())
                    .then(result => {
                        console.log('统计数据API响应:', result);
                        if (result.code === 0) {
                            const data = result.data;
                            document.getElementById('visitCount').textContent = data.visit_count;
                            document.getElementById('uniqueIpCount').textContent = data.unique_ip_count;
                            document.getElementById('scanCount').textContent = data.scan_count;
                            document.getElementById('successCount').textContent = data.success_count;
                            document.getElementById('failCount').textContent = data.fail_count;
                            document.getElementById('expireCount').textContent = data.expire_count;
                            console.log('统计数据加载成功:', data);
                        } else if (result.code === -1 && result.msg.includes('登录')) {
                            // 未登录，跳转到登录页
                            console.log('用户未登录，跳转到登录页');
                            window.location.href = '/page/promotion/promoter-login.html';
                        } else {
                            console.error('获取统计数据失败:', result.msg);
                        }
                    })
                    .catch(error => {
                        console.error('加载统计数据失败:', error);
                    });
            }

            // 加载操作记录列表
            window.loadActionList = function (page = 1) {
                currentPage = page;

                fetch(`/api/promotion/promoter/today-actions?page=${page}&limit=${pageSize}`, {
                    method: 'GET',
                    credentials: 'include', // 确保包含cookies
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                    .then(response => response.json())
                    .then(result => {
                        console.log('操作记录API响应:', result);
                        if (result.code === 0) {
                            const data = result.data;
                            renderActionTable(data.list);
                            renderPagination(data.total, page);
                            console.log('操作记录加载成功，记录数:', data.list.length);
                        } else if (result.code === -1 && result.msg.includes('登录')) {
                            // 未登录，跳转到登录页
                            console.log('用户未登录，跳转到登录页');
                            window.location.href = '/page/promotion/promoter-login.html';
                        } else {
                            console.error('获取操作记录失败:', result.msg);
                            document.getElementById('actionTableBody').innerHTML =
                                '<tr><td colspan="4" style="text-align: center; padding: 40px; color: #999;">暂无数据</td></tr>';
                        }
                    })
                    .catch(error => {
                        console.error('加载操作记录失败:', error);
                        document.getElementById('actionTableBody').innerHTML =
                            '<tr><td colspan="4" style="text-align: center; padding: 40px; color: #f56c6c;">加载失败</td></tr>';
                    });
            }

            // 渲染操作记录表格
            function renderActionTable(list) {
                const tbody = document.getElementById('actionTableBody');

                if (list.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 40px; color: #999;">暂无数据</td></tr>';
                    return;
                }

                const html = list.map(item => `
                    <tr>
                        <td>${item.user_id}</td>
                        <td>${item.time}</td>
                        <td>${item.ip}</td>
                        <td>
                            <span class="layui-badge ${getStatusClass(item.status)}">${item.status}</span>
                        </td>
                    </tr>
                `).join('');

                tbody.innerHTML = html;
            }

            // 获取状态样式类
            function getStatusClass(status) {
                switch (status) {
                    case '登录成功': return 'layui-bg-green';
                    case '登录失败': return 'layui-bg-red';
                    case '请求过期': return 'layui-bg-orange';
                    case '扫码': return 'layui-bg-blue';
                    default: return '';
                }
            }

            // 渲染分页
            function renderPagination(total, current) {
                if (total <= pageSize) {
                    document.getElementById('pagination').innerHTML = '';
                    return;
                }

                laypage.render({
                    elem: 'pagination',
                    count: total,
                    limit: pageSize,
                    curr: current,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            loadActionList(obj.curr);
                        }
                    }
                });
            }

            // 退出登录
            window.logout = function () {
                layer.confirm('确定要退出登录吗？', { icon: 3, title: '提示' }, function (index) {
                    fetch('/api/promotion/promoter/logout', {
                        method: 'POST',
                        credentials: 'include', // 确保包含cookies
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                        .then(response => response.json())
                        .then(result => {
                            layer.close(index);
                            console.log('退出登录响应:', result);
                            layer.msg('已退出登录', { icon: 1 }, function () {
                                window.location.href = '/page/promotion/promoter-login.html';
                            });
                        })
                        .catch(error => {
                            layer.close(index);
                            console.error('退出登录失败:', error);
                            window.location.href = '/page/promotion/promoter-login.html';
                        });
                });
            }
        });
    </script>
</body>

</html>