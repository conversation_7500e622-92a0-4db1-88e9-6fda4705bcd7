const mysql = require('mysql2/promise');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4"
};

async function verifyPromotionData() {
  console.log('🔍 验证推广用户数据...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 检查user表中的推广用户
    console.log('\n📋 1. 检查user表中的推广用户:');
    const [promoters] = await connection.execute(
      `SELECT id, username, promoter_id, promotion_link, user_type, status, 
              visit_count, unique_ip_count, scan_count, success_count, fail_count, expire_count,
              created_at, updated_at
       FROM user 
       WHERE user_type = 'promoter' 
       ORDER BY created_at DESC`
    );
    
    console.log(`找到 ${promoters.length} 个推广用户:`);
    promoters.forEach((user, index) => {
      console.log(`\n${index + 1}. 推广用户详情:`);
      console.log(`   ID: ${user.id}`);
      console.log(`   用户名: ${user.username}`);
      console.log(`   推广用户ID: ${user.promoter_id}`);
      console.log(`   推广链接: ${user.promotion_link}`);
      console.log(`   状态: ${user.status ? '启用' : '禁用'}`);
      console.log(`   统计数据: 访问${user.visit_count} | IP${user.unique_ip_count} | 扫码${user.scan_count} | 成功${user.success_count} | 失败${user.fail_count} | 过期${user.expire_count}`);
      console.log(`   创建时间: ${user.created_at}`);
    });
    
    // 2. 测试API查询格式
    console.log('\n📋 2. 测试API查询格式:');
    const [apiFormat] = await connection.execute(
      `SELECT promoter_id as user_id, username, promotion_link, status, created_at, updated_at 
       FROM user 
       WHERE user_type = 'promoter' 
       ORDER BY created_at DESC`
    );
    
    console.log('API返回格式预览:');
    console.log(JSON.stringify(apiFormat, null, 2));
    
    // 3. 检查表结构
    console.log('\n📋 3. 检查user表结构:');
    const [columns] = await connection.execute('DESCRIBE user');
    console.log('user表字段:');
    columns.forEach(col => {
      if (col.Field.includes('promoter') || col.Field.includes('promotion') || col.Field.includes('user_type') || col.Field.includes('count')) {
        console.log(`✅ ${col.Field} (${col.Type}) - ${col.Comment || '无注释'}`);
      }
    });
    
    // 4. 测试登录验证
    console.log('\n📋 4. 测试登录验证:');
    const [loginTest] = await connection.execute(
      "SELECT promoter_id, username, promotion_link FROM user WHERE promoter_id = ? AND password = ? AND status = 1 AND user_type = 'promoter'",
      ['1001', '123456']
    );
    
    if (loginTest.length > 0) {
      console.log('✅ 推广用户1001登录验证成功');
      console.log(`   返回数据: ${JSON.stringify(loginTest[0], null, 2)}`);
    } else {
      console.log('❌ 推广用户1001登录验证失败');
    }
    
    // 5. 检查是否有重复数据
    console.log('\n📋 5. 检查数据完整性:');
    const [duplicates] = await connection.execute(
      "SELECT promoter_id, COUNT(*) as count FROM user WHERE user_type = 'promoter' GROUP BY promoter_id HAVING COUNT(*) > 1"
    );
    
    if (duplicates.length > 0) {
      console.log('⚠️  发现重复的推广用户ID:');
      duplicates.forEach(dup => {
        console.log(`   推广用户ID ${dup.promoter_id} 有 ${dup.count} 条记录`);
      });
    } else {
      console.log('✅ 没有发现重复的推广用户ID');
    }
    
    await connection.end();
    
    console.log('\n🎉 推广用户数据验证完成！');
    
    if (promoters.length > 0) {
      console.log('\n📋 可用的推广用户账户:');
      promoters.forEach(user => {
        console.log(`- 用户ID: ${user.promoter_id}, 密码: 123456, 用户名: ${user.username}`);
      });
    } else {
      console.log('\n⚠️  没有找到推广用户数据，请运行 modify-user-table-simple.js 创建示例数据');
    }
    
  } catch (error) {
    console.error('💥 验证失败:', error.message);
    process.exit(1);
  }
}

verifyPromotionData();
