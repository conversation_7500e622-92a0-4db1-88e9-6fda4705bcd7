const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
    device_id: {
        type: String,
        required: true,
        index: true  // 为设备ID创建索引提高查询性能
    },
    status: {
        type: String,
        required: true,
        enum: ['pending', 'running', 'failed', 'completed'] // 限定四种状态
    },
    execution_data: {
        type: String,
        required: true
    },
    created_at: {
        type: Date,
        default: Date.now  // 自动记录创建时间
    }
});

// 添加复合索引优化查询
messageSchema.index({ device_id: 1, created_at: -1 });

module.exports = messageSchema;
