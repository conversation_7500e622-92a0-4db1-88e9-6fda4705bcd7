<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <!-- <fieldset class="table-search-fieldset"> -->
            <legend>搜索信息(三个条件可多选可单选,例如不过滤设备,设备编号就不用输入)</legend>
            <!-- <div style="margin: 10px 10px 10px 10px"> -->
            <form class="layui-form layui-form-pane" action="">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">设备名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="deviceName" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <input type="text" name="taskStatus" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button type="submit" class="layui-btn layui-btn-primary" lay-submit
                            lay-filter="data-search-btn"><i class="layui-icon"></i> 搜 索</button>
                    </div>
                </div>
            </form>
            <!-- </div> -->
            <!-- </fieldset> -->
            <script type="text/html" id="toolbarDemo">
                <!-- <div class="layui-btn-container"> -->

            <button class="layui-btn layui-btn-danger layui-btn-sm data-add-btn" lay-event="delList"> 删除勾选设备 </button>
            <button class="layui-btn layui-btn-warm layui-btn-sm data-add-btn" lay-event="assignDevice"> 分配设备 </button>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 100px;">投屏延迟:</label>
                <div class="layui-input-inline" style="width: 60px;">
                    <input type="number" id="reTime" value="2000" autocomplete="off" class="layui-input">
                </div>
            </div>

            <!-- <button class="layui-btn layui-btn-warp layui-btn-sm data-add-btn" lay-event="screencast5"> 一屏多控 </button> -->
            <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="screencast1"> 极速预览 </button>
            <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="screencast2"> 标清预览 </button>
            <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="screencast3"> 高清预览 </button>
            <!-- <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="screencast4"> 超清预览 </button> -->

            <!-- <div class="layui-input-inline" style="width: 80px;">
                <input type="checkbox" name="群控" title="群控" />
            </div> -->
            <!-- </div> -->
            </script>
            <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
        </div>
    </div>
    <script src="/lib/jquery-3.4.1/jquery-3.4.1.min.js"></script>
    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/js/deviceAssignment.js" charset="utf-8"></script>
    <script>
        let daiLi_userName = null
        setTimeout(function () {
            layui.use(['form', 'table'], function () {
                var $ = layui.jquery,
                    form = layui.form,
                    table = layui.table

                let tableIns = table.render({
                    elem: '#currentTableId',
                    url: '/indexDevice',
                    toolbar: '#toolbarDemo',
                    method: 'get',
                    where: {
                        daiLi_userName: daiLi_userName
                    },
                    // defaultToolbar: ['filter', 'exports', 'print', {
                    //     title: '提示',
                    //     layEvent: 'LAYTABLE_TIPS',
                    //     icon: 'layui-icon-tips'
                    // }],
                    cols: [[
                        { type: "checkbox", width: 50 },
                        { field: 'deviceName', width: 90, title: '设备名', sort: true },
                        { field: 'taskName', width: 110, title: '当前任务', sort: true },
                        { 
                            field: 'assignedUsers', 
                            width: 120, 
                            title: '分配状态', 
                            templet: function(d) {
                                if (d.assignedUsers && d.assignedUsers.length > 0) {
                                    return '<span style="color: #5FB878;font-weight:bold">' + d.assignedUsers.join(', ') + '</span>';
                                }
                                return '<span style="color: #999">未分配</span>';
                            },
                            sort: true
                        },

                        {
                            field: 'taskStatus', width: 80, title: '设备状态', templet: function (d) {
                                console.log('设备状态数据:', d.taskStatus); // 调试日志
                                
                                // 有效状态列表
                                const validStatuses = ["掉线", "离线", "空闲", "忙碌"];
                                
                                // 如果有有效状态，直接显示
                                if (d.taskStatus && validStatuses.includes(d.taskStatus)) {
                                    switch(d.taskStatus.toLowerCase()) {
                                        case "掉线":
                                        case "离线":
                                            return '<span style="color: #F00;font-weight:bold">'+d.taskStatus+'</span>';
                                        case "空闲":
                                            return '<span style="color: #00CCFF;font-weight:bold">空闲</span>';
                                        case "忙碌":
                                            return '<span style="color: #FFCC00;font-weight:bold">忙碌</span>';
                                    }
                                }
                                
                                // 无有效状态时显示最后活跃时间
                                if (d.updateTime) {
                                    return '<span style="color: #999">最后在线: '+d.updateTime+'</span>';
                                }
                                
                                // 默认情况
                                return '<span style="color: #999">等待状态更新</span>';
                            }, sort: true
                        },
                        {
                            field: 'device_width',
                            width: 100, title: '分辨率', sort: true, templet: function (d) {
                                return d.device_width + "*" + d.device_height
                            }
                        },

                        // { field: 'device_UUID', width: 80, title: '设备ID' },
                        // { field: 'groupId', width: 90, title: '分组ID', sort: true },
                        {
                            field: 'groupId',
                            width: 90, title: '分组ID', templet: function (d) {
                                if (d.groupId) {
                                    return d.groupId
                                } else {
                                    return '<span style="color: #F00;font-weight:bold">未分组</span>'
                                }
                            }, sort: true
                        },
                        { field: 'socketId', width: 100, title: '账号属', sort: true },
                        { field: 'deviceMsg', minWidth: 150, title: '最新消息', sort: true },
                        { field: 'updateTime', width: 140, title: '更新时间', sort: true }
                    ]],
                    limits: [10, 50, 200, 1000, 2000, 5000],
                    limit: 10,
                    page: true,
                    skin: 'line'
                });


                /**
                 * toolbar监听事件
                 */
                table.on('toolbar(currentTableFilter)', function (obj) {
                    if (obj.event === 'delList') {  // 监听批量删除操作
                        var checkStatus = table.checkStatus('currentTableId')
                            , data = checkStatus.data;
                        layer.confirm('真的删除这些设备么?', function (index) {
                            // obj.del();
                            tableIns.reload({
                                url: '/indexDevice/delList',
                                method: 'post',
                                where: {
                                    devicesData: JSON.stringify(data),
                                    daiLi_userName: daiLi_userName
                                }
                            })
                            layer.close(index);
                        });
                    } else if (obj.event === 'assignDevice') {
                        var checkStatus = table.checkStatus('currentTableId');
                        var selectedDevices = checkStatus.data;
                        
                        if (selectedDevices.length === 0) {
                            layer.msg('请先选择要分配的设备');
                            return;
                        }
                        
                        // 加载用户列表
                        $.get('/user/allUser', function(res) {
                            if (res.code !== 0) {
                                layer.msg('获取用户列表失败: ' + res.msg);
                                return;
                            }
                            
                            // 清空并填充select选项
                            var select = $('#assignDeviceModal select[name="userNames"]');
                            select.empty();
                            res.data.forEach(function(user) {
                                select.append('<option value="'+user.userName+'">'+user.userName+'</option>');
                            });
                            // 重新渲染表单
                            form.render('select');
                            
                            // 打开分配模态框
                            layer.open({
                                type: 1,
                                title: '分配设备给用户',
                                area: ['500px', '300px'],
                                content: $('#assignDeviceModal'),
                                success: function() {
                                    form.render();
                                }
                            });
                        });
                    } else {
                        let input = document.getElementById("reTime")
                        let reTime = input.value;
                        if (isNaN(reTime) || reTime < 100 || reTime > 10000) {
                            input.value = "";
                            layer.msg("输入不符合要求，100-10000")
                            return false
                        }
                        reTime = Number(reTime)
                        if (obj.event === "screencast1") {
                            yanZheng({
                                touPingHeight: "320px",
                                imgQuality: 5,//100图片质量
                                imgScale: 0.4,//1图片缩放
                                isOpenGray: false,//是否灰度化,打开后图片稍微变小
                                isOpenThreshold: false,//是否打开阈值,打开后图片变大
                                imgThreshold: 60,//图像阈值
                                reTime: reTime//截图速度,
                            })
                        } else if (obj.event === "screencast2") {//标清
                            yanZheng({
                                touPingHeight: "480px",
                                imgQuality: 5,//100图片质量
                                imgScale: 0.8,//1图片缩放
                                isOpenGray: false,//是否灰度化
                                isOpenThreshold: false,//是否打开阈值
                                imgThreshold: 60,//图像阈值
                                reTime: reTime//截图速度
                            })
                        } else if (obj.event === "screencast3") {//高清
                            yanZheng({
                                touPingHeight: "640px",
                                imgQuality: 8,//100图片质量
                                imgScale: 0.8,//1图片缩放
                                isOpenGray: false,//是否灰度化
                                isOpenThreshold: false,//是否打开阈值
                                imgThreshold: 60,//图像阈值
                                reTime: reTime//截图速度
                            })
                        } else if (obj.event === "screencast4") {//超清
                            yanZheng({
                                touPingHeight: "640px",
                                imgQuality: 10,//100图片质量
                                imgScale: 1,//1图片缩放
                                isOpenGray: false,//是否灰度化
                                isOpenThreshold: false,//是否打开阈值
                                imgThreshold: 60,//图像阈值
                                reTime: reTime//截图速度
                            })
                        } else if (obj.event === "screencast5") {//一屏多控
                            yanZheng({
                                touPingHeight: "480px",
                                imgQuality: 5,//100图片质量
                                imgScale: 0.8,//1图片缩放
                                isOpenGray: false,//是否灰度化
                                isOpenThreshold: false,//是否打开阈值
                                imgThreshold: 60,//图像阈值
                                reTime: reTime,//截图速度
                                yiPingKong: true
                            })
                        }

                    }

                });

                form.on('submit(data-search-btn)', function (data) {
                    let datas = data.field
                    // console.log(datas);
                    tableIns.reload({
                        url: "/indexDevice",
                        method: "get",
                        where: {
                            deviceName: (datas.deviceName + "").replace(/\s/g, ""),
                            taskStatus: (datas.taskStatus + "").replace(/\s/g, ""),
                            daiLi_userName: daiLi_userName
                        }
                    })
                    return false;
                })

                function yanZheng(touPingDic) {
                    // alert(window.localStorage.getItem("userName"))
                    if (window.localStorage.getItem("userName") == "cjroot") {//这里改版 可能不是cjroot
                        screencast(touPingDic)
                    } else {
                        $.get('/indexUsers/touPing', function (resdata) {
                            if (resdata.code == 1) {
                                screencast(touPingDic)
                            } else {
                                layer.msg("no:" + resdata.msg);
                            }
                        })
                    }
                }

                function screencast(touPingDic) {
                    var checkStatus = table.checkStatus('currentTableId')
                    // window.socketIds = [];
                    window.touPingDic = touPingDic
                    window.socketIds = new Map();
                    let checkStatu_datas = checkStatus.data
                    checkStatu_datas.forEach(dic => {
                        // window.socketIds.push(dic.socketId)
                        window.socketIds.set(dic.socketId, { device_width: dic.device_width || 540, device_height: dic.device_height || 960 });
                    });

                    // if (window.socketIds.length == 0) {
                    if (window.socketIds.size == 0) {
                        layer.msg("请先勾选设备")
                        return false
                    }
                    layer.open({
                        type: 2,//可传入的值有：0（信息框，默认）1（页面层）2（iframe层）3（加载层）4（tips层）
                        shade: 0.6,
                        // maxmin: true,
                        offset: 'lt',
                        anim: 2,//动画
                        title: '投屏预览',
                        area: ["95%", '800px'],
                        // area: ["95%", "95%"],
                        // skin: 'layui-layer-nobg', //没有背景色
                        shadeClose: true,//其他区域关闭
                        content: "/page/devices/screencast.html",
                        // success: function (layero, index) {
                        //     // layer.msg("加载成功")
                        //     let iframeWin = $(layero).find('iframe')[0].contentWindow
                        //     iframeWin.chuanCan2(daiLi_userName)
                        // },
                        end: function () {
                            layer.msg("关闭投屏")
                            return false;
                        }
                    });
                }


            });

        }, 300)

        function chuanCan(userName) {
            // console.log("传参过来了:" + userName);
            daiLi_userName = userName
            // console.log("daiLi_userName" + daiLi_userName);
        }
    </script>

    <!-- 分配设备模态框 -->
    <div id="assignDeviceModal" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="assignDeviceForm">
            <div class="layui-form-item">
                <label class="layui-form-label">选择用户</label>
                <div class="layui-input-block">
                    <select name="userNames" lay-search multiple>
                        <!-- 用户列表将通过JS动态加载 -->
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="assignDeviceSubmit">确认分配</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>

    <!-- 分配设备逻辑已移至deviceAssignment.js -->
</body>

</html>