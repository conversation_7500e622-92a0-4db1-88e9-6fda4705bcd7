const axios = require('axios');

// 调试cookie和session传递
async function debugCookieSession() {
  console.log('🔍 调试cookie和session传递...');
  
  const baseURL = 'http://localhost:15001';
  const testUser = { user_id: '1001', password: '123456' };
  
  try {
    // 方法1: 使用axios.create with jar
    console.log('\n1️⃣ 方法1: 使用axios.create...');
    
    const client = axios.create({
      baseURL: baseURL,
      withCredentials: true,
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    
    // 登录
    const loginResponse = await client.post('/api/promotion/promoter/login', testUser);
    console.log('登录响应:', {
      status: loginResponse.status,
      code: loginResponse.data.code,
      msg: loginResponse.data.msg,
      setCookie: loginResponse.headers['set-cookie']
    });
    
    if (loginResponse.data.code === 0) {
      // 获取cookie
      const cookies = loginResponse.headers['set-cookie'];
      console.log('收到的cookies:', cookies);
      
      // 测试统计数据
      const statsResponse = await client.get('/api/promotion/promoter/today-stats');
      console.log('统计数据响应:', {
        status: statsResponse.status,
        code: statsResponse.data.code,
        msg: statsResponse.data.msg
      });
      
      if (statsResponse.data.code !== 0) {
        console.log('\n❌ 方法1失败，尝试方法2...');
        
        // 方法2: 手动设置cookie
        console.log('\n2️⃣ 方法2: 手动设置cookie...');
        
        if (cookies && cookies.length > 0) {
          const sessionCookie = cookies.find(cookie => cookie.includes('connect.sid'));
          
          if (sessionCookie) {
            console.log('提取的session cookie:', sessionCookie);
            
            // 创建新的client，手动设置cookie
            const client2 = axios.create({
              baseURL: baseURL,
              timeout: 10000,
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Cookie': sessionCookie.split(';')[0] // 只取cookie值部分
              }
            });
            
            // 重新测试统计数据
            const statsResponse2 = await client2.get('/api/promotion/promoter/today-stats');
            console.log('手动cookie统计数据响应:', {
              status: statsResponse2.status,
              code: statsResponse2.data.code,
              msg: statsResponse2.data.msg,
              data: statsResponse2.data.data
            });
            
            if (statsResponse2.data.code === 0) {
              console.log('\n✅ 方法2成功！');
              const stats = statsResponse2.data.data;
              
              console.log('\n📊 统计数据详情:');
              console.log(`   visit_count: ${stats.visit_count}`);
              console.log(`   unique_ip_count: ${stats.unique_ip_count}`);
              console.log(`   scan_count: ${stats.scan_count}`);
              console.log(`   success_count: ${stats.success_count}`);
              console.log(`   fail_count: ${stats.fail_count}`);
              console.log(`   expire_count: ${stats.expire_count}`);
              
              // 检查数据是否为0
              const hasNonZeroData = Object.values(stats).some(val => val > 0);
              console.log(`\n📈 是否有非零数据: ${hasNonZeroData}`);
              
              if (!hasNonZeroData) {
                console.log('\n⚠️  所有数据都为0，可能的原因:');
                console.log('   1. 数据库中没有对应用户的数据');
                console.log('   2. API查询逻辑有问题');
                console.log('   3. 日期匹配问题');
                
                // 测试用户信息API
                console.log('\n3️⃣ 测试用户信息API...');
                const userResponse = await client2.get('/api/promotion/promoter/user-info');
                console.log('用户信息响应:', {
                  status: userResponse.status,
                  code: userResponse.data.code,
                  data: userResponse.data.data
                });
                
                // 测试操作记录API
                console.log('\n4️⃣ 测试操作记录API...');
                const actionsResponse = await client2.get('/api/promotion/promoter/today-actions?page=1&limit=5');
                console.log('操作记录响应:', {
                  status: actionsResponse.status,
                  code: actionsResponse.data.code,
                  msg: actionsResponse.data.msg,
                  total: actionsResponse.data.data?.total,
                  listLength: actionsResponse.data.data?.list?.length
                });
              }
              
            } else {
              console.log('❌ 方法2也失败了');
            }
          } else {
            console.log('❌ 没有找到session cookie');
          }
        } else {
          console.log('❌ 登录响应中没有cookies');
        }
      } else {
        console.log('✅ 方法1成功！');
        const stats = statsResponse.data.data;
        console.log('统计数据:', stats);
      }
    } else {
      console.log('❌ 登录失败');
    }
    
  } catch (error) {
    console.log('❌ 请求失败:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
  }
  
  console.log('\n🎉 调试完成！');
  
  console.log('\n💡 解决方案:');
  console.log('1. 如果方法2成功，说明需要在前端手动处理cookie');
  console.log('2. 如果数据都为0，需要检查数据库和API逻辑');
  console.log('3. 可以在浏览器中直接测试，浏览器会自动处理cookie');
}

// 运行调试
debugCookieSession().catch(console.error);
