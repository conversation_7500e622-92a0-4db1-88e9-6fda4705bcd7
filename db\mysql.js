const mysql = require("mysql2/promise"); // 使用 promise 版本
const express = require("express");
const bodyParser = require("body-parser");
const cors = require("cors");
const app = express();
// 开发环境允许所有来源
// 获取本机IP地址
const os = require("os");
// 获取纯IP地址用于监听
function getServerIPs() {
  const interfaces = os.networkInterfaces();
  const ips = [];
  for (const name in interfaces) {
    for (const net of interfaces[name]) {
      if (net.family === "IPv4" && !net.internal) {
        ips.push(net.address); // 只返回IP地址
      }
    }
  }
  return ips;
}

// 获取完整URL用于CORS检查
function getAllowedOrigins() {
  const ips = getServerIPs();
  const origins = [];

  // 添加本地IP
  ips.forEach((ip) => {
    origins.push(`http://${ip}:15001`); // 主应用端口
    origins.push(`http://${ip}:3101`); // API端口
  });

  // 添加云服务器IP
  const cloudIPs = ["**************"]; // 云服务器IP列表
  cloudIPs.forEach((ip) => {
    origins.push(`http://${ip}:15001`);
    origins.push(`http://${ip}:3101`);
  });

  console.log("当前允许的跨域来源:", origins);
  return origins;
}

// 动态CORS配置
let allowedOrigins = [...getAllowedOrigins()]; // 包含本机IP
const updateCorsConfig = () => {
  allowedOrigins = [...getServerIPs()];
  console.log("当前允许的访问源:", allowedOrigins);
};

// 每小时检查一次IP变化
setInterval(updateCorsConfig, 3600000);

// 增强的CORS配置
console.log("初始化CORS中间件...");
app.use((req, res, next) => {
  console.log(`收到请求: ${req.method} ${req.url}`);
  console.log("请求头:", JSON.stringify(req.headers));
  console.log("请求来源:", req.headers.origin || "无来源");
  next();
});

// 调试模式CORS配置
app.use(
  cors({
    origin: true, // 允许所有来源
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true,
    optionsSuccessStatus: 200,
  })
);

console.log("CORS中间件已配置完成");

// 彻底重构的CORS处理中间件
app.use((req, res, next) => {
  // 记录详细请求信息
  console.log("--- 开始处理CORS ---");
  console.log("请求方法:", req.method);
  console.log("请求路径:", req.path);
  console.log("请求来源:", req.headers.origin);
  console.log("请求头:", req.headers);

  // 设置强制的CORS头
  const allowedOrigins = [
    "http://**************:15001",
    "http://localhost:15001",
  ];

  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin)) {
    res.setHeader("Access-Control-Allow-Origin", origin);
  }

  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  res.setHeader(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, X-Requested-With"
  );
  res.setHeader("Access-Control-Allow-Credentials", "true");
  res.setHeader("Vary", "Origin");

  // 处理预检请求
  if (req.method === "OPTIONS") {
    console.log("处理OPTIONS预检请求");
    res.setHeader("Content-Length", "0");
    return res.status(204).end();
  }

  console.log("CORS头已设置:", {
    "Access-Control-Allow-Origin": res.getHeader("Access-Control-Allow-Origin"),
    "Access-Control-Allow-Methods": res.getHeader(
      "Access-Control-Allow-Methods"
    ),
    "Access-Control-Allow-Headers": res.getHeader(
      "Access-Control-Allow-Headers"
    ),
  });

  next();
});

// 全局错误处理
app.use((err, req, res, next) => {
  console.error("CORS错误:", err.message);
  res.status(500).json({ error: err.message });
});

// 数据库连接检查函数
async function checkDatabaseConnection() {
  if (!isPoolReady) {
    console.log("数据库连接池尚未就绪，跳过检查");
    return false;
  }

  try {
    const conn = await pool.getConnection();
    await conn.ping();
    conn.release();
    console.log("数据库连接正常");
    return true;
  } catch (err) {
    console.error("数据库连接失败:", err);
    return false;
  }
}

// 初始化完成后检查连接
function setupPostInitializationChecks() {
  // 延迟执行首次连接检查
  setTimeout(() => {
    checkDatabaseConnection().then((success) => {
      if (!success) {
        console.error("请检查数据库配置和服务状态");
      }
    });
  }, 2000);

  // 定期健康检查
  setInterval(() => {
    if (isPoolReady) {
      checkDatabaseConnection();
    }
  }, 300000); // 每5分钟检查一次
}
app.use(bodyParser.json());
const moment = require("moment");

// 连接池配置
// 增强的数据库配置
// 加载配置文件
const fs = require("fs");
const path = require("path");

// 增强的数据库配置
console.log("初始化数据库配置...");
let mysqlConfig = {
  host: "localhost",
  user: "root",
  password: "123456", // MySQL密码
  database: "douyin",
  connectionLimit: 10,
  // 添加权限配置
  multipleStatements: true,
  insecureAuth: true,
};

// 尝试从配置文件读取配置
try {
  const configPath = path.join(__dirname, "..", "config.json");
  if (fs.existsSync(configPath)) {
    const fileConfig = JSON.parse(fs.readFileSync(configPath, "utf8"));
    if (fileConfig.mysql) {
      mysqlConfig = { ...mysqlConfig, ...fileConfig.mysql };
      console.log("✅ 已加载MySQL配置文件");
      console.log(
        `📋 数据库配置: ${mysqlConfig.user}@${mysqlConfig.host}:${mysqlConfig.database}`
      );
    }
  } else {
    console.log("⚠️  未找到config.json，使用默认配置");
  }
} catch (error) {
  console.log("⚠️  配置文件读取失败，使用默认配置:", error.message);
}

// 验证数据库连接权限
async function verifyDatabasePermissions() {
  try {
    const testConn = await mysql.createConnection(mysqlConfig);
    const [rows] = await testConn.query(
      `
            SELECT * FROM mysql.user WHERE User = ?
        `,
      [mysqlConfig.user]
    );
    testConn.end();

    if (rows.length === 0) {
      console.error("数据库用户不存在或权限不足");
      console.log("建议执行以下SQL:");
      console.log(
        `GRANT ALL PRIVILEGES ON ${mysqlConfig.database}.* TO '${mysqlConfig.user}'@'%' IDENTIFIED BY '${mysqlConfig.password}';`
      );
      console.log("FLUSH PRIVILEGES;");
    }
  } catch (err) {
    console.error("数据库权限验证失败:", err.message);
  }
}

verifyDatabasePermissions();

// 尝试加载外部配置文件
const configPath = path.join(__dirname, "../../config.json");
try {
  if (fs.existsSync(configPath)) {
    const externalConfig = JSON.parse(fs.readFileSync(configPath));
    mysqlConfig = { ...mysqlConfig, ...externalConfig.mysql };
    console.log("[MySQL] 使用外部配置文件");
  } else {
    console.warn("[MySQL] 未找到config.json，使用默认配置");
  }
} catch (err) {
  console.error("[MySQL] 配置文件加载失败:", err.message);
}

console.log("[MySQL] 使用配置:", {
  host: mysqlConfig.host,
  user: mysqlConfig.user,
  database: mysqlConfig.database,
});

// 验证配置
if (!mysqlConfig.password) {
  console.error("[MySQL] 严重警告: 未配置MySQL密码!");
}

// 配置验证工具
function validateMySQLConfig() {
  return new Promise(async (resolve) => {
    try {
      const testConn = await mysql.createConnection({
        host: mysqlConfig.host,
        user: mysqlConfig.user,
        password: mysqlConfig.password,
      });

      // 检查数据库是否存在
      const [rows] = await testConn.query(
        `SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?`,
        [mysqlConfig.database]
      );

      if (rows.length === 0) {
        console.error(`[MySQL] 错误: 数据库 ${mysqlConfig.database} 不存在`);
      }

      // 检查用户权限
      const [privs] = await testConn.query(
        `SELECT * FROM mysql.user WHERE User = ? AND Host = ?`,
        [mysqlConfig.user, "%"]
      );

      testConn.end();

      if (privs.length === 0) {
        console.error(`[MySQL] 错误: 用户 ${mysqlConfig.user} 无访问权限`);
      }

      resolve(true);
    } catch (err) {
      console.error("[MySQL] 配置验证失败:", {
        code: err.code,
        message: err.message,
      });
      resolve(false);
    }
  });
}

// 启动时验证配置
validateMySQLConfig().then((success) => {
  if (!success) {
    console.error("[MySQL] 配置验证未通过，请检查以下内容:");
    console.error("1. MySQL服务是否运行");
    console.error("2. 用户名密码是否正确");
    console.error("3. 数据库是否存在");
    console.error("4. 用户是否有足够权限");
  }
});

const poolConfig = {
  ...mysqlConfig,
  waitForConnections: true,
  queueLimit: 0,
  connectTimeout: 10000,
  idleTimeout: 600000,
  timezone: "+08:00",
  charset: "utf8mb4",
};

// 创建config.json示例文件
const sampleConfig = {
  mysql: {
    host: "localhost",
    user: "root",
    password: "123456",
    database: "douyin",
    connectionLimit: 20,
  },
};

// 检查是否需要创建示例配置文件
if (!fs.existsSync(configPath)) {
  fs.writeFileSync(
    configPath + ".example",
    JSON.stringify(sampleConfig, null, 2)
  );
  console.log("[MySQL] 已创建示例配置文件: config.json.example");
}

// 连接池相关变量
let pool;
let isPoolReady = false;
let retryCount = 0;
const maxRetries = 3;

async function initializePool() {
  // 确保变量已初始化
  if (typeof retryCount === "undefined") {
    retryCount = 0;
  }

  try {
    console.log(`[MySQL] 初始化连接池 (尝试 ${retryCount + 1}/${maxRetries})`);
    pool = mysql.createPool(poolConfig);

    // 测试连接
    const testConn = await pool.getConnection();
    await testConn.ping();
    testConn.release();

    console.log("[MySQL] 连接池初始化成功");
    retryCount = 0;
    isPoolReady = true;
    return true;
  } catch (err) {
    retryCount++;
    console.error(
      `[MySQL] 连接失败 (尝试 ${retryCount}/${maxRetries}):`,
      err.message
    );

    if (retryCount < maxRetries) {
      const delaySeconds = 3;
      console.log(`[MySQL] ${delaySeconds}秒后重试...`);
      await new Promise((resolve) => setTimeout(resolve, delaySeconds * 1000));
      return initializePool();
    } else {
      console.error("[MySQL] 达到最大重试次数，请检查配置:");
      console.error("当前配置:", poolConfig);
      isPoolReady = false;
      throw new Error("无法建立数据库连接");
    }
  }
}

// 立即执行初始化
initializePool().catch((err) => {
  console.error("[MySQL] 致命错误:", err);
  process.exit(1);
});

// 增强的连接池监控
pool.on("connection", (connection) => {
  console.log(`[MySQL] 新连接建立 ID:${connection.threadId}`);
});

pool.on("acquire", (connection) => {
  console.log(`[MySQL] 连接被获取 ID:${connection.threadId}`);
});

pool.on("release", (connection) => {
  console.log(`[MySQL] 连接释放 ID:${connection.threadId}`);
});

pool.on("enqueue", () => {
  console.warn("[MySQL] 等待可用连接，考虑增加连接池大小");
});

// 连接池健康检查
setInterval(async () => {
  try {
    const conn = await pool.getConnection();
    await conn.ping();
    conn.release();
    console.log("[MySQL] 连接池健康检查通过");
  } catch (err) {
    console.error("[MySQL] 连接池健康检查失败:", err);
  }
}, 300000); // 每5分钟检查一次

// 优雅关闭处理
process.on("SIGINT", async () => {
  console.log("[MySQL] 正在关闭连接池...");
  await pool.end();
  console.log("[MySQL] 连接池已关闭");
  process.exit(0);
});
async function createWsLinkDataTable() {
  try {
    const connection = await pool.getConnection();
    await connection.query(`
            CREATE TABLE IF NOT EXISTS ws_link_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                phoneNumber VARCHAR(50) NOT NULL,
                username VARCHAR(100),
                links TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE INDEX idx_phoneNumber (phoneNumber)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);
    connection.release();
    console.log("ws_link_data表已创建或已存在");
  } catch (err) {
    console.error("创建ws_link_data表失败:", err);
  }
}

async function createTasksTable() {
  try {
    const connection = await pool.getConnection();
    await connection.query(`
            CREATE TABLE IF NOT EXISTS tasks (
                task_id INT AUTO_INCREMENT PRIMARY KEY,
                device_code JSON NOT NULL,
                task_name VARCHAR(255) NOT NULL,
                keywords JSON,
                province JSON,
                max_followers INT,
                account_duration INT,
                like_rate INT,
                comment_rate INT,
                comment_content JSON,
                device_status VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);
    connection.release();
    console.log("tasks表已创建或已存在");
  } catch (err) {
    console.error("创建tasks表失败:", err);
  }
}

async function createLinkDataTable() {
  try {
    const connection = await pool.getConnection();
    await connection.query(`
            CREATE TABLE IF NOT EXISTS link_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                link_url VARCHAR(512) NOT NULL,
                link_type VARCHAR(50),
                status VARCHAR(50) DEFAULT 'active',
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_status (status),
                INDEX idx_create_time (create_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);
    connection.release();
    console.log("link_data表已创建或已存在");
  } catch (err) {
    console.error("创建link_data表失败:", err);
  }
}

createWsLinkDataTable(); // 应用启动时执行
createTasksTable(); // 创建tasks表
createLinkDataTable(); // 创建link_data表

console.log("MySQL 连接池已创建");

// 创建设备日志表
async function createDeviceLogsTable() {
  try {
    const connection = await pool.getConnection();
    await connection.query(`
            CREATE TABLE IF NOT EXISTS device_logs (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                device_id VARCHAR(64) NOT NULL,
                status ENUM('pending', 'running', 'completed', 'failed') NOT NULL,
                execution_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_device_id (device_id),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);
    connection.release();
    console.log("device_logs表已创建或已存在");
  } catch (err) {
    console.error("创建device_logs表失败:", err);
  }
}

// 🔹 保存商品和关键词
app.post("/saveData", async (req, res) => {
  const { productName, keyword } = req.body;

  if (!productName || !keyword) {
    return res.status(400).json({ message: "商品名称和关键词不能为空" });
  }

  const products = productName
    .split("|")
    .map((p) => p.trim())
    .filter((p) => p);
  const keywords = keyword
    .split("|")
    .map((k) => k.trim())
    .filter((k) => k);

  if (products.length === 0 || keywords.length === 0) {
    return res.status(400).json({ message: "商品名称和关键词不能为空" });
  }

  const values = products.flatMap((product) =>
    keywords.map((kw) => [product, kw, new Date()])
  );

  const sql =
    "INSERT INTO shopping (productName, keyword, created_at) VALUES ?";

  try {
    const connection = await pool.getConnection();
    await connection.beginTransaction();
    await connection.query(sql, [values]);
    await connection.commit();
    connection.release();
    res.json({ message: "数据存储成功", insertedRows: values.length });
  } catch (err) {
    console.error("数据插入失败:", err);
    res.status(500).json({ message: "数据存储失败" });
  }
});

// 🔹 获取商品数据
app.get("/getData", async (req, res) => {
  console.log("获取商品数据");

  let { page = 1, limit = 10, startTime, endTime } = req.query;
  page = parseInt(page);
  limit = parseInt(limit);
  const offset = (page - 1) * limit;

  if (!startTime || !endTime) {
    startTime = moment().subtract(7, "days").format("YYYY-MM-DD 00:00:00");
    endTime = moment().format("YYYY-MM-DD 23:59:59");
  }

  const sql = `
        SELECT id, productName, keyword, 
               DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') AS created_at
        FROM shopping 
        WHERE created_at BETWEEN ? AND ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?;
    `;

  const countSql = `SELECT COUNT(*) AS total FROM shopping WHERE created_at BETWEEN ? AND ?;`;

  try {
    const [results] = await pool.query(sql, [
      startTime,
      endTime,
      limit,
      offset,
    ]);
    const [countResult] = await pool.query(countSql, [startTime, endTime]);

    res.json({
      message: "数据查询成功",
      total: countResult[0].total,
      data: results,
    });
  } catch (err) {
    console.error("数据查询失败:", err);
    res.status(500).json({ message: "数据查询失败" });
  }
});

// 🔹 手机端上报执行数据
app.post("/reportExecution", async (req, res) => {
  const { deviceId, successCount, failureCount } = req.body;

  if (!deviceId || successCount === undefined || failureCount === undefined) {
    return res.status(400).json({ message: "参数不能为空" });
  }

  const sql = `
        INSERT INTO execution_results (deviceId, successCount, failureCount, created_at)
        VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE 
            successCount = successCount + VALUES(successCount), 
            failureCount = failureCount + VALUES(failureCount),
            created_at = NOW()
    `;

  try {
    const connection = await pool.getConnection();
    await connection.beginTransaction();
    await connection.query(sql, [deviceId, successCount, failureCount]);
    await connection.commit();
    connection.release();
    res.json({ message: "数据上报成功" });
  } catch (error) {
    console.error("数据上报失败:", error);
    res.status(500).json({ message: "服务器错误" });
  }
});

// 🔹 获取手机执行数据
app.get("/getExecutionSummary", async (req, res) => {
  try {
    const sql = `
            SELECT id, deviceId, successCount, failureCount, 
            DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') AS created_at
            FROM execution_results ORDER BY created_at DESC
        `;

    const [results] = await pool.query(sql);
    res.json({ message: "查询成功", data: results });
  } catch (error) {
    console.error("查询失败:", error);
    res.status(500).json({ message: "服务器错误" });
  }
});

// 🔹 查询每天每台设备的执行结果

// 🔹 查询每天每台设备的执行结果（支持完整时间）
app.get("/getDailyExecutionResults", async (req, res) => {
  console.log("查询每天每台设备的执行结果");
  const { date } = req.query;

  if (!date) {
    return res.status(400).json({ message: "请提供查询日期" });
  }

  try {
    const startDate = `${date} 00:00:00`;
    const endDate = `${date} 23:59:59`;

    const sql = `
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') AS date, 
                deviceId, 
                CAST(SUM(successCount) AS UNSIGNED) AS totalSuccess, 
                CAST(SUM(failureCount) AS UNSIGNED) AS totalFailure
            FROM execution_results
            WHERE created_at BETWEEN ? AND ?
            GROUP BY DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s'), deviceId
            ORDER BY date DESC, deviceId;
        `;

    const [results] = await pool.query(sql, [startDate, endDate]);
    res.json({ message: "查询成功", data: results });
  } catch (error) {
    console.error("查询失败:", error);
    res.status(500).json({ message: "服务器错误" });
  }
});

// 🔹 更新执行结果
app.post("/updateExecutionResults", async (req, res) => {
  const { deviceId, successCount, failureCount, date } = req.body;

  if (!deviceId || successCount === undefined || failureCount === undefined) {
    return res.status(400).json({ message: "缺少必要参数" });
  }

  try {
    const query = `
            INSERT INTO execution_results (deviceId, successCount, failureCount, created_at)
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
                successCount = successCount + VALUES(successCount),
                failureCount = failureCount + VALUES(failureCount);
        `;

    const executionDate = date
      ? moment(date).format("YYYY-MM-DD")
      : moment().format("YYYY-MM-DD");
    await pool.query(query, [
      deviceId,
      successCount,
      failureCount,
      executionDate,
    ]);

    res.json({ message: "数据更新成功" });
  } catch (error) {
    console.error("数据更新失败:", error);
    res.status(500).json({ message: "服务器错误" });
  }
});

// 🔹 任务执行统计（按时间范围）
// 🔹 任务执行统计（按时间范围）
app.get("/getExecutionSummaryByRange", async (req, res) => {
  let { startDate, endDate } = req.query;

  if (!startDate || !endDate) {
    return res.status(400).json({ message: "请提供开始日期和结束日期" });
  }

  const sql = `
        SELECT DATE_FORMAT(created_at, '%Y-%m-%d') AS date,
               SUM(successCount) AS successCount,
               SUM(failureCount) AS failureCount,
               SUM(successCount) + SUM(failureCount) AS totalCount
        FROM execution_results
        WHERE created_at BETWEEN ? AND ?
        GROUP BY date
        ORDER BY date;
    `;

  try {
    const [results] = await pool.query(sql, [startDate, endDate]);
    res.json({ success: true, data: results });
  } catch (error) {
    console.error("查询失败:", error);
    res.status(500).json({ message: "数据库查询失败" });
  }
});

// 设备日志接口
app.post("/device/log", async (req, res) => {
  const { device_id, status, execution_data } = req.body;

  if (!device_id || !status) {
    return res.status(400).json({ message: "设备ID和状态不能为空" });
  }

  // 调试：打印即将插入的数据
  console.log("插入日志数据:", {
    device_id,
    status,
    execution_data: typeof execution_data,
  });

  const sql = `
        INSERT INTO device_logs (device_id, status, execution_data)
        VALUES (?, ?, ?)
    `;

  try {
    const connection = await pool.getConnection();
    // 明确将execution_data转为字符串
    const dataToInsert = execution_data ? String(execution_data) : null;
    await connection.query(sql, [device_id, status, dataToInsert]);
    connection.release();
    res.json({ message: "日志记录成功" });
  } catch (err) {
    console.error("日志记录失败:", err);
    res.status(500).json({ message: "日志记录失败" });
  }
});

// 查询设备日志
app.get("/api/device/logs", async (req, res) => {
  let {
    device_id,
    status,
    start_time,
    end_time,
    page = 1,
    limit = 20,
  } = req.query;

  // 确保参数为有效数字
  page = parseInt(page) || 1;
  limit = parseInt(limit) || 20;
  const offset = (page - 1) * limit;

  // 验证limit和offset有效性
  if (isNaN(offset) || offset < 0) {
    return res.status(400).json({ message: "分页参数无效" });
  }

  try {
    console.log("获取设备日志连接");
    const connection = await pool.getConnection();

    // 构建基础查询
    let whereClause = "WHERE 1=1";
    let params = [];

    // 添加筛选条件
    if (device_id) {
      whereClause += " AND device_id = ?";
      params.push(device_id);
    }
    if (status) {
      whereClause += " AND status = ?";
      params.push(status);
    }
    if (start_time) {
      whereClause += " AND created_at >= ?";
      params.push(start_time);
    }
    if (end_time) {
      whereClause += " AND created_at <= ?";
      params.push(end_time);
    }

    const query = `
            SELECT 
                id, device_id, status, execution_data,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') AS created_at
            FROM device_logs
            ${whereClause}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?;
        `;

    const countQuery = `SELECT COUNT(*) AS total FROM device_logs ${whereClause}`;

    console.log("执行SQL:", query, [...params, limit, offset]);
    const [results] = await connection.query(query, [...params, limit, offset]);
    const [count] = await connection.query(countQuery, params);

    connection.release();

    res.json({
      code: 0,
      data: {
        list: results,
        total: count[0].total,
        page: page,
        limit: limit,
      },
    });
  } catch (err) {
    console.error("查询设备日志失败:", {
      error: err.message,
      stack: err.stack,
    });
    res.status(500).json({
      code: -1,
      msg: "查询设备日志失败",
    });
  }
});

// 启动服务器前创建所有表
async function initializeTables() {
  await createWsLinkDataTable();
  await createTasksTable();
  await createLinkDataTable();
  await createDeviceLogsTable();
}

initializeTables().then(() => {
  const PORT = 3101;
  const serverIPs = getServerIPs();
  if (serverIPs.length === 0) {
    console.error("无法获取服务器IP地址");
    process.exit(1);
  }
  const serverIP = serverIPs[0]; // 使用第一个有效IP
  app.listen(PORT, serverIP, () => {
    console.log(`MySQL API服务已启动，监听地址: ${serverIP}:${PORT}`);
    console.log(`服务器运行在${serverIP}:${PORT}`);
  });
});

// 导出pool和express路由
module.exports = {
  pool: pool,
  router: app,
};
