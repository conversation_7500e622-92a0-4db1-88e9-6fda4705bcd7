const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// MySQL连接配置
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "123456",
  database: "douyin",
  charset: "utf8mb4",
  multipleStatements: true
};

async function updateTableStructure() {
  console.log('🔧 开始修改promotion_actions表结构...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 读取SQL文件
    const sqlFile = path.join(__dirname, 'db', 'add-province-field.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // 分割SQL语句
    const statements = sqlContent.split(';').filter(stmt => stmt.trim());
    
    console.log(`📋 准备执行 ${statements.length} 条SQL语句...`);
    
    // 执行SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          await connection.execute(statement);
          console.log(`✅ 执行成功 (${i + 1}/${statements.length}): ${statement.substring(0, 50)}...`);
        } catch (error) {
          if (error.code === 'ER_DUP_FIELDNAME') {
            console.log(`⚠️  字段已存在 (${i + 1}/${statements.length}): ${statement.substring(0, 50)}...`);
          } else {
            console.error(`❌ 执行失败 (${i + 1}/${statements.length}):`, error.message);
          }
        }
      }
    }
    
    // 验证表结构
    console.log('\n🔍 验证表结构...');
    
    console.log('\n📋 promotion_actions表结构:');
    const [actionsColumns] = await connection.execute('DESCRIBE promotion_actions');
    actionsColumns.forEach(col => {
      if (col.Field.includes('ip') || col.Field.includes('province')) {
        console.log(`✅ ${col.Field} (${col.Type}) - ${col.Comment || '无注释'}`);
      }
    });
    
    console.log('\n📋 promotion_visits表结构:');
    const [visitsColumns] = await connection.execute('DESCRIBE promotion_visits');
    visitsColumns.forEach(col => {
      if (col.Field.includes('ip') || col.Field.includes('province')) {
        console.log(`✅ ${col.Field} (${col.Type}) - ${col.Comment || '无注释'}`);
      }
    });
    
    // 检查数据
    console.log('\n📊 检查更新后的数据...');
    
    const [actionsData] = await connection.execute(
      `SELECT promotion_user_id, ip_address, ip_province, action_type, 
              DATE_FORMAT(action_time, '%Y-%m-%d %H:%i:%s') as action_time
       FROM promotion_actions 
       ORDER BY action_time DESC 
       LIMIT 5`
    );
    
    console.log('✅ promotion_actions数据示例:');
    actionsData.forEach((action, index) => {
      console.log(`${index + 1}. 推广用户${action.promotion_user_id} - ${action.action_type}`);
      console.log(`   IP: ${action.ip_address}, 省份: ${action.ip_province || '未知'}`);
      console.log(`   时间: ${action.action_time}`);
    });
    
    const [visitsData] = await connection.execute(
      `SELECT promotion_user_id, visitor_ip, ip_province,
              DATE_FORMAT(visit_time, '%Y-%m-%d %H:%i:%s') as visit_time
       FROM promotion_visits 
       ORDER BY visit_time DESC 
       LIMIT 5`
    );
    
    console.log('\n✅ promotion_visits数据示例:');
    visitsData.forEach((visit, index) => {
      console.log(`${index + 1}. 推广用户${visit.promotion_user_id}`);
      console.log(`   IP: ${visit.visitor_ip}, 省份: ${visit.ip_province || '未知'}`);
      console.log(`   时间: ${visit.visit_time}`);
    });
    
    await connection.end();
    
    console.log('\n🎉 表结构修改完成！');
    console.log('\n📋 修改内容:');
    console.log('✅ promotion_actions表添加ip_province字段');
    console.log('✅ promotion_visits表添加ip_province字段');
    console.log('✅ 添加省份字段索引');
    console.log('✅ 更新现有数据的省份信息');
    
    console.log('\n🔗 接下来需要修改的代码:');
    console.log('- API接口：返回省份信息');
    console.log('- 前端界面：显示省份列');
    console.log('- 数据插入：包含省份信息');
    
  } catch (error) {
    console.error('💥 修改失败:', error.message);
    process.exit(1);
  }
}

updateTableStructure();
