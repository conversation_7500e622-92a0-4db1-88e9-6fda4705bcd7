# MySQL配置指南

## 🔧 MySQL权限问题解决方案

### 问题描述
```
Access denied for user 'root'@'localhost' (using password: YES)
```

### 解决方案

#### 方案1: 重置MySQL root密码为空（推荐）

1. **停止MySQL服务**
   ```bash
   # Windows
   net stop mysql
   
   # 或者在服务管理器中停止MySQL服务
   ```

2. **以安全模式启动MySQL**
   ```bash
   # 打开命令提示符（管理员权限）
   mysqld --skip-grant-tables --skip-networking
   ```

3. **打开新的命令提示符，连接MySQL**
   ```bash
   mysql -u root
   ```

4. **重置密码为空**
   ```sql
   USE mysql;
   UPDATE user SET authentication_string='' WHERE User='root';
   UPDATE user SET plugin='mysql_native_password' WHERE User='root';
   FLUSH PRIVILEGES;
   EXIT;
   ```

5. **重启MySQL服务**
   ```bash
   # 关闭安全模式的MySQL
   # 然后正常启动MySQL服务
   net start mysql
   ```

#### 方案2: 修改配置文件使用正确密码

如果您知道MySQL root密码，请修改 `config.json` 文件：

```json
{
  "mysql": {
    "host": "localhost",
    "user": "root",
    "password": "您的实际密码",
    "database": "douyin",
    "connectionLimit": 10,
    "multipleStatements": true,
    "insecureAuth": true
  }
}
```

#### 方案3: 创建新的MySQL用户

```sql
-- 连接到MySQL
mysql -u root -p

-- 创建新用户
CREATE USER 'douyin_user'@'localhost' IDENTIFIED BY 'douyin_pass';

-- 授予权限
GRANT ALL PRIVILEGES ON douyin.* TO 'douyin_user'@'localhost';
FLUSH PRIVILEGES;
```

然后修改 `config.json`：
```json
{
  "mysql": {
    "host": "localhost",
    "user": "douyin_user",
    "password": "douyin_pass",
    "database": "douyin"
  }
}
```

### 🔍 检查MySQL状态

#### 检查MySQL服务是否运行
```bash
# Windows
net start | findstr MySQL

# 或者检查服务
services.msc
```

#### 测试MySQL连接
```bash
# 测试连接（无密码）
mysql -u root

# 测试连接（有密码）
mysql -u root -p
```

#### 检查MySQL版本和配置
```sql
SELECT VERSION();
SELECT User, Host, plugin FROM mysql.user WHERE User='root';
```

### 📋 常见问题

#### 1. MySQL服务未启动
```bash
# 启动MySQL服务
net start mysql

# 如果服务不存在，可能需要重新安装MySQL
```

#### 2. 端口被占用
```bash
# 检查3306端口
netstat -an | findstr 3306
```

#### 3. 权限不足
- 确保以管理员权限运行命令提示符
- 检查MySQL安装目录的权限

### 🚀 验证配置

启动应用程序后，查看日志输出：
```
✅ 已加载MySQL配置文件
📋 数据库配置: root@localhost:douyin
✅ MySQL连接池创建成功
✅ 数据库douyin创建成功
```

如果看到这些信息，说明MySQL配置正确。

### 📞 技术支持

如果仍然遇到问题：

1. 检查MySQL错误日志
2. 确认MySQL版本兼容性
3. 尝试重新安装MySQL
4. 检查防火墙设置

### 🔄 恢复默认配置

如果需要恢复到默认配置，删除 `config.json` 文件，程序将使用内置的默认配置。
