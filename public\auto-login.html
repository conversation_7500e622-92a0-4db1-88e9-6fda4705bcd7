<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
            text-align: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .status {
            font-size: 18px;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        
        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            text-align: left;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007cba;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .manual-login {
            margin-top: 30px;
            padding: 20px;
            background: #e9ecef;
            border-radius: 5px;
        }
        
        .manual-login a {
            color: #007cba;
            text-decoration: none;
            font-weight: bold;
        }
        
        .manual-login a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 自动登录系统</h1>
        
        <div id="status" class="status loading">
            <div class="spinner"></div>
            正在处理登录请求...
        </div>
        
        <div class="log" id="log">初始化自动登录系统...\n</div>
        
        <div class="manual-login">
            <p>如果自动登录失败，您可以：</p>
            <a href="/promoter-login.html">手动登录</a> | 
            <a href="/debug-simple-login.html">调试登录</a>
        </div>
    </div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            
            log.textContent += logMessage;
            log.scrollTop = log.scrollHeight;
            console.log(`${prefix} ${message}`);
        }
        
        function updateStatus(message, type = 'loading') {
            status.className = `status ${type}`;
            if (type === 'loading') {
                status.innerHTML = `<div class="spinner"></div>${message}`;
            } else {
                status.innerHTML = message;
            }
        }
        
        // 自动登录函数
        async function autoLogin() {
            addLog('🚀 开始自动登录流程');
            
            // 解析URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const userId = urlParams.get('user_id');
            const password = urlParams.get('password');
            
            addLog(`URL参数: user_id=${userId}, password=${password ? '***' : '无'}`);
            
            if (!userId || !password) {
                addLog('缺少必要的登录参数', 'error');
                updateStatus('❌ 缺少用户ID或密码参数', 'error');
                return;
            }
            
            try {
                addLog('发送登录请求...');
                updateStatus('正在验证登录信息...');
                
                const response = await fetch('/api/promotion/promoter/login', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: userId, password: password })
                });
                
                addLog(`收到响应: 状态码 ${response.status}`);
                
                const result = await response.json();
                addLog(`响应数据: ${JSON.stringify(result)}`);
                
                if (result.code === 0) {
                    addLog('登录成功！', 'success');
                    updateStatus('✅ 登录成功，正在跳转...', 'success');
                    
                    addLog(`用户信息: ${result.data.username} (ID: ${result.data.user_id})`);
                    addLog('2秒后跳转到仪表板...');
                    
                    setTimeout(() => {
                        addLog('执行跳转到干净版仪表板');
                        window.location.href = '/clean-promoter-dashboard.html';
                    }, 2000);
                    
                } else {
                    addLog(`登录失败: ${result.msg}`, 'error');
                    updateStatus(`❌ 登录失败: ${result.msg}`, 'error');
                    
                    // 提供手动登录选项
                    setTimeout(() => {
                        addLog('5秒后跳转到手动登录页面...');
                        setTimeout(() => {
                            window.location.href = `/promoter-login.html?user_id=${userId}`;
                        }, 5000);
                    }, 1000);
                }
                
            } catch (error) {
                addLog(`登录请求失败: ${error.message}`, 'error');
                updateStatus(`❌ 网络错误: ${error.message}`, 'error');
                
                // 提供手动登录选项
                setTimeout(() => {
                    addLog('5秒后跳转到手动登录页面...');
                    setTimeout(() => {
                        window.location.href = `/promoter-login.html?user_id=${userId}`;
                    }, 5000);
                }, 1000);
            }
        }
        
        // 页面加载时自动执行登录
        document.addEventListener('DOMContentLoaded', function() {
            addLog('自动登录页面加载完成');
            addLog(`当前URL: ${window.location.href}`);
            addLog(`当前cookies: ${document.cookie || '无'}`);
            
            // 延迟1秒后开始登录，确保页面完全加载
            setTimeout(() => {
                autoLogin();
            }, 1000);
        });
        
        // 监听页面卸载
        window.addEventListener('beforeunload', function(e) {
            addLog('页面即将跳转');
        });
    </script>
</body>
</html>
