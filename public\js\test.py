import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog
import ws1
from ws1 import shutdown_ws_server
import os
import sys
from myLogger import Logger
from PJYSDK import *
import subprocess
import websockets
import asyncio
import multiprocessing  # 导入 multiprocessing 模块
from multiprocessing import Process, freeze_support

from jiekou.main import flask_main
import os
import platform
import subprocess
from fontTools.ttLib import TTFont
from fontTools.ttLib.tables._n_a_m_e import NameRecord
from websockets.exceptions import ConnectionClosedOK, ConnectionClosedError

# 系统字体目录
SYSTEM_FONT_DIRS = {
    "Windows": [os.path.join(os.environ.get("WINDIR", "C:\\Windows"), "Fonts")],
    "Darwin": ["/System/Library/Fonts", "/Library/Fonts", "~/Library/Fonts"],
    "Linux": [
        "/usr/share/fonts",
        "/usr/local/share/fonts",
        "~/.fonts",
        "~/.local/share/fonts"
    ]
}


def get_font_family(font):
    """从字体文件中提取本地化字体显示名称"""
    name_table = font['name']
    font_family = None

    # 定义优先语言顺序：简体中文 > 繁体中文 > 英文
    PREFERRED_LANGUAGES = {
        0x0804: "简体中文",  # Chinese (PRC)
        0x0404: "繁体中文",  # Chinese (Taiwan)
        0x0409: "英文"  # English (US)
    }

    # 尝试按优先顺序获取本地化名称
    for platform_id, plat_enc_id, lang_id in [
        (3, 1, 0x0804),  # Windows Unicode - 简体中文
        (3, 1, 0x0404),  # Windows Unicode - 繁体中文
        (3, 1, 0x0409),  # Windows Unicode - 英文
        (1, 0, 0x0000)  # Mac Roman - 默认
    ]:
        for record in name_table.names:
            if (
                    record.platformID == platform_id and
                    record.platEncID == plat_enc_id and
                    record.langID == lang_id and
                    record.nameID in [1, 4]  # 1=Font Family, 4=Full Name
            ):
                try:
                    if platform_id == 3 and plat_enc_id == 1:
                        font_family = record.toUnicode()
                    else:
                        font_family = record.string.decode('mac_roman')

                    # 找到后立即返回
                    return font_family
                except Exception:
                    continue

    # 如果未找到任何本地化名称，使用默认方法
    for record in name_table.names:
        if record.nameID == 1:  # Font Family name
            try:
                if record.platformID == 3 and record.platEncID == 1:
                    return record.toUnicode()
                elif record.platformID == 1 and record.platEncID == 0:
                    return record.string.decode('mac_roman')
            except Exception:
                continue

    return "Unknown"


def get_system_fonts(include_all=True):
    """获取系统所有可用字体及其路径"""
    print("开始获取系统字体...")
    font_dict = {}
    system = platform.system()

    if system not in SYSTEM_FONT_DIRS:
        print(f"不支持的系统: {system}")
        return font_dict

    print(f"检测到系统: {system}，搜索以下字体目录: {SYSTEM_FONT_DIRS[system]}")

    # 遍历字体目录
    for font_dir in SYSTEM_FONT_DIRS[system]:
        expanded_font_dir = os.path.expanduser(font_dir)
        print(f"检查字体目录: {expanded_font_dir}")

        if not os.path.isdir(expanded_font_dir):
            print(f"目录不存在或不可访问: {expanded_font_dir}")
            continue

        # 遍历目录中的所有文件
        font_count = 0
        for root, _, files in os.walk(expanded_font_dir):
            for file in files:
                if file.lower().endswith(('.ttf', '.ttc', '.otf')):
                    font_path = os.path.join(root, file)
                    try:
                        # 尝试解析字体名称
                        if file.lower().endswith('.ttc'):
                            try:
                                font_count += 1
                                font_count_ttc = TTFont(font_path, fontNumber=0).familyCount
                            except Exception:
                                font_count_ttc = 1

                            for font_num in range(font_count_ttc):
                                try:
                                    font = TTFont(font_path, fontNumber=font_num)
                                    font_name = get_font_family(font)
                                    if font_name:
                                        # 为TTC中的不同字体添加后缀
                                        unique_font_name = f"{font_name}"
                                        font_dict[unique_font_name] = font_path
                                        if font_count <= 10:  # 只打印前10个字体信息
                                            print(f"已识别字体 {font_count}: {unique_font_name} -> {font_path}")
                                except Exception as e:
                                    print(f"无法解析字体 {file} (子字体 {font_num}): {e}")
                        else:
                            font_count += 1
                            font = TTFont(font_path)
                            font_name = get_font_family(font)
                            if font_name:
                                font_dict[font_name] = font_path
                                if font_count <= 10:  # 只打印前10个字体信息
                                    print(f"已识别字体 {font_count}: {font_name} -> {font_path}")
                    except Exception as e:
                        print(f"无法解析字体 {file}: {e}")

        print(f"从目录 {expanded_font_dir} 中识别出 {font_count} 个字体")

    print(f"共获取到 {len(font_dict)} 个系统字体")
    return font_dict


# 在创建GUI之前获取系统字体
def load_system_fonts():
    """加载系统字体并返回字体名称到路径的映射"""
    print("开始加载系统字体...")
    try:
        system_fonts = get_system_fonts()
        print(f"成功加载 {len(system_fonts)} 个系统字体")

        # 打印前10个字体用于调试
        if system_fonts:
            print("前10个识别的字体:")
            for i, (name, path) in enumerate(list(system_fonts.items())[:10], 1):
                print(f"  {i}. {name}: {path}")

        return system_fonts
    except Exception as e:
        print(f"加载系统字体时发生错误: {e}")
        # 如果出错，返回默认字体列表
        default_fonts = {
            "微软雅黑": r"C:\Windows\Fonts\msyh.ttc",
            "宋体": r"C:\Windows\Fonts\simsun.ttc",
            "黑体": r"C:\Windows\Fonts\simhei.ttf",
            "楷体": r"C:\Windows\Fonts\simkai.ttf",
        }
        print(f"使用默认字体列表: {list(default_fonts.keys())}")
        return default_fonts


# 统一的配置管理模块
class ConfigManager:
    @staticmethod
    def get_app_dir():
        if getattr(sys, 'frozen', False):
            return os.path.dirname(sys.executable)
        return os.path.dirname(os.path.abspath(__file__))

    @staticmethod
    def get_config_path(filename="user_config.json"):
        return os.path.join(ConfigManager.get_app_dir(), filename)

    @staticmethod
    def load_user_config():
        config_path = ConfigManager.get_config_path()
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ 配置文件不存在：{config_path}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件解析错误：{e}")
            return {}

    @staticmethod
    def save_user_config(config):
        config_path = ConfigManager.get_config_path()
        try:
            lock = multiprocessing.Lock()  # 添加锁
            with lock:
                with open(config_path, "w", encoding="utf-8") as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"✅ 配置已保存：{config_path}")
        except Exception as e:
            print(f"❌ 配置保存失败：{e}")


config_manager = ConfigManager()
user_config = config_manager.load_user_config()

# 在应用启动时调用字体加载函数
print("应用启动：准备加载系统字体...")
system_fonts = load_system_fonts()
print(f"系统字体加载完成，共找到 {len(system_fonts)} 个字体")
print(f"字体列表示例: {list(system_fonts.keys())[:5]}...")  # 打印前5个字体名称

# 设置现代化UI风格
style = ttk.Style()
style.theme_use('clam')  # 使用clam主题

# 定义配色方案
COLOR_PRIMARY = '#3498db'
COLOR_SECONDARY = '#2ecc71'
COLOR_DARK = '#2c3e50'
COLOR_LIGHT = '#ecf0f1'
COLOR_TEXT = '#34495e'

# 配置全局样式
style.configure('TNotebook', tabposition='n', background=COLOR_LIGHT)
style.configure('TNotebook.Tab', 
               padding=[15, 5], 
               font=('微软雅黑', 10, 'bold'),
               background=COLOR_LIGHT,
               foreground=COLOR_DARK)
style.map('TNotebook.Tab', 
          background=[('selected', COLOR_PRIMARY)],
          foreground=[('selected', 'white')])

style.configure('TButton', 
               padding=8, 
               font=('微软雅黑', 10),
               background=COLOR_PRIMARY,
               foreground='white',
               borderwidth=0)
style.map('TButton',
          background=[('active', COLOR_SECONDARY), ('disabled', '#bdc3c7')])

style.configure('TLabel', 
               font=('微软雅黑', 10),
               foreground=COLOR_TEXT,
               background=COLOR_LIGHT)
style.configure('TEntry',
               padding=8,
               font=('微软雅黑', 10),
               fieldbackground='white',
               bordercolor=COLOR_PRIMARY,
               lightcolor=COLOR_PRIMARY,
               darkcolor=COLOR_PRIMARY)

# 创建主窗口
root = tk.Tk()
root.title("管理端 v1.0.6")
root.geometry("1100x850")  # 增大窗口尺寸
root.configure(bg=COLOR_LIGHT)

# 主标题
title_frame = ttk.Frame(root)
title_frame.pack(fill=tk.X, pady=(20, 10), padx=20)
title_label = ttk.Label(title_frame, 
                       text="管理端", 
                       font=("微软雅黑", 24, "bold"), 
                       foreground=COLOR_DARK)
title_label.pack()

# 添加分隔线
separator = ttk.Separator(root, orient='horizontal')
separator.pack(fill=tk.X, padx=20, pady=5)

# 主容器
main_frame = ttk.Frame(root)
main_frame.pack(fill=tk.BOTH, expand=True, padx=25, pady=15)

# 选项卡
notebook = ttk.Notebook(main_frame)
notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

# 设置统一的控件样式
entry_style = {
    'font': ('微软雅黑', 10), 
    'width': 35,
    'foreground': COLOR_TEXT
}
label_style = {
    'font': ('微软雅黑', 10, 'bold'), 
    'foreground': COLOR_DARK,
    'background': COLOR_LIGHT
}
button_style = {
    'font': ('微软雅黑', 10),
    'padding': 8,
    'style': 'TButton'
}

# 自定义控件样式
def create_section(parent, title):
    frame = ttk.Frame(parent)
    label = ttk.Label(frame, text=title, font=('微软雅黑', 12, 'bold'), foreground=COLOR_DARK)
    label.pack(anchor='w', pady=(0, 10))
    return frame

# 自定义图片内容页面
image_tab = create_section(notebook, "自定义图片内容")
notebook.add(image_tab, text="自定义图片内容")

# 使用网格布局
content_frame = ttk.Frame(image_tab)
content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

# 封面序号范围
seq_frame = ttk.Frame(content_frame)
seq_frame.grid(row=0, column=0, sticky='w', pady=10)
ttk.Label(seq_frame, text="封面序号范围:", **label_style).pack(side=tk.LEFT)
cover_seq_frame = ttk.Frame(seq_frame)
cover_seq_frame.pack(side=tk.LEFT, padx=5)
start_cover_seq_entry = ttk.Entry(cover_seq_frame, **entry_style)
start_cover_seq_entry.pack(side=tk.LEFT)
ttk.Label(cover_seq_frame, text="-").pack(side=tk.LEFT, padx=2)
end_cover_seq_entry = ttk.Entry(cover_seq_frame, **entry_style)
end_cover_seq_entry.pack(side=tk.LEFT)
start_cover_seq_entry.insert(0, user_config.get("cover_seq_range", ["", ""])[0])
end_cover_seq_entry.insert(0, user_config.get("cover_seq_range", ["", ""])[1])

# 封面章节范围
chapter_frame = ttk.Frame(content_frame)
chapter_frame.grid(row=1, column=0, sticky='w', pady=10)
ttk.Label(chapter_frame, text="封面章节范围:", **label_style).pack(side=tk.LEFT)
cover_chapter_frame = ttk.Frame(chapter_frame)
cover_chapter_frame.pack(side=tk.LEFT, padx=5)
start_cover_chapter_entry = ttk.Entry(cover_chapter_frame, **entry_style)
start_cover_chapter_entry.pack(side=tk.LEFT)
ttk.Label(cover_chapter_frame, text="-").pack(side=tk.LEFT, padx=2)
end_cover_chapter_entry = ttk.Entry(cover_chapter_frame, **entry_style)
end_cover_chapter_entry.pack(side=tk.LEFT)
start_cover_chapter_entry.insert(0, user_config.get("cover_chapter_range", ["", ""])[0])
end_cover_chapter_entry.insert(0, user_config.get("cover_chapter_range", ["", ""])[1])

# Excel文件路径
excel_frame = ttk.Frame(content_frame)
excel_frame.grid(row=2, column=0, sticky='w', pady=10)
ttk.Label(excel_frame, text="Excel文件路径:", **label_style).pack(side=tk.LEFT)
excel_path_frame = ttk.Frame(excel_frame)
excel_path_frame.pack(side=tk.LEFT, padx=5)
excel_path_entry = ttk.Entry(excel_path_frame, **entry_style)
excel_path_entry.pack(side=tk.LEFT)
excel_path_entry.insert(0, user_config.get("excel_path", ""))


def select_excel_file():
    file_path = filedialog.askopenfilename(filetypes=[("Excel文件", "*.xlsx *.xls")])
    if file_path:
        if not os.path.isfile(file_path):
            logger.log_message("选择的文件路径无效，请重新选择。")
            return
        excel_path_entry.delete(0, tk.END)
        excel_path_entry.insert(0, file_path)


def publish_task():
    config = collect_gui_config()
    user_config.update(config)
    config_manager.save_user_config(user_config)
    logger.log_message("发送至服务端")
    print(json.dumps(config, ensure_ascii=False, indent=2))


excel_browse_button = tk.Button(excel_path_frame, text="选择文件", command=select_excel_file)
excel_browse_button.pack(side=tk.LEFT, padx=5)

excel_use_var = tk.BooleanVar(value=user_config.get("use_excel", False))
tk.Checkbutton(image_tab, text="启用Excel数据", variable=excel_use_var).pack()

# 按钮区域
button_frame = ttk.Frame(image_tab)
button_frame.pack(side=tk.BOTTOM, pady=20, fill=tk.X, padx=10)

start_server_button = ttk.Button(button_frame, text="启动服务端并启动 PC 客户端", 
                                style='Accent.TButton',
                                command=lambda: [start_server()])
start_server_button.pack(side=tk.RIGHT, padx=10)

task_publish_button = tk.Button(button_frame, text="任务发布", command=publish_task)
task_publish_button.pack(side=tk.LEFT, padx=10)

start_message_button = tk.Button(button_frame, text="发送启动消息", command=lambda: start_pc_client())
start_message_button.pack(side=tk.LEFT, padx=10)

# 文字配置页面
config_tab = create_section(notebook, "文字内容配置")
notebook.add(config_tab, text="文字配置")

# 使用网格布局
content_frame = ttk.Frame(config_tab)
content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

# 标题内容
title_frame = ttk.Frame(content_frame)
title_frame.grid(row=0, column=0, sticky='w', pady=10)
ttk.Label(title_frame, text="输入标题内容:", **label_style).pack(side=tk.LEFT)
title_input_entry = ttk.Entry(title_frame, **entry_style)
title_input_entry.pack(side=tk.LEFT, padx=5)
title_input_entry.insert(0, user_config.get("title", ""))

# 文章结尾内容
article_frame = ttk.Frame(content_frame)
article_frame.grid(row=1, column=0, sticky='w', pady=10)
ttk.Label(article_frame, text="文章结尾内容:", **label_style).pack(side=tk.LEFT)
article_entry = ttk.Entry(article_frame, **entry_style)
article_entry.pack(side=tk.LEFT, padx=5)
article_entry.insert(0, user_config.get("article_text", ""))

# 敏感词设置
sensitive_frame = ttk.Frame(content_frame)
sensitive_frame.grid(row=2, column=0, sticky='w', pady=10)
ttk.Label(sensitive_frame, text="自定义敏感词:", **label_style).pack(side=tk.LEFT)
sensitive_entry = ttk.Entry(sensitive_frame, **entry_style)
sensitive_entry.pack(side=tk.LEFT, padx=5)
sensitive_entry.insert(0, ",".join(user_config.get("sensitive_words", [])))
ttk.Label(sensitive_frame, text="(英文逗号分隔)").pack(side=tk.LEFT)

# 自定义排版页面
text_tab = create_section(notebook, "文字排版样式设置")
notebook.add(text_tab, text="文字排版样式")

# 使用网格布局
content_frame = ttk.Frame(text_tab)
content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

# 关键词模式
mode_frame = ttk.Frame(content_frame)
mode_frame.grid(row=0, column=0, sticky='w', pady=10)
ttk.Label(mode_frame, text="关键词模式:", **label_style).pack(side=tk.LEFT)
mode_var = tk.StringVar(value=user_config.get("mode", "prefix"))
mode_menu = ttk.OptionMenu(mode_frame, mode_var, mode_var.get(), "prefix", "suffix")
mode_menu.pack(side=tk.LEFT, padx=5)

# 关键词设置
keywords_frame = ttk.Frame(content_frame)
keywords_frame.grid(row=1, column=0, sticky='w', pady=10)
ttk.Label(keywords_frame, text="关键词设置:", **label_style).pack(side=tk.LEFT)

keywords_container = ttk.Frame(keywords_frame)
keywords_container.pack(side=tk.LEFT, padx=5)
keyword_entries = []
for i in range(6):
    entry_frame = ttk.Frame(keywords_container)
    entry_frame.pack(side=tk.LEFT, padx=2)
    entry = ttk.Entry(entry_frame, **entry_style)
    entry.pack()
    entry.insert(0, user_config.get("first_two_keywords", ["", "", "", "", "", ""])[i])
    keyword_entries.append(entry)

# 字体选择
font_frame = ttk.Frame(content_frame)
font_frame.grid(row=2, column=0, sticky='w', pady=10)
ttk.Label(font_frame, text="选择字体:", **label_style).pack(side=tk.LEFT)
font_var = tk.StringVar(value=user_config.get("font", "宋体"))

# 添加调试信息
print("准备创建字体选择下拉菜单...")
print(f"system_fonts 是否定义: {'是' if 'system_fonts' in globals() else '否'}")
print(f"system_fonts 类型: {type(system_fonts)}")
print(f"system_fonts 长度: {len(system_fonts)}")
print(f"system_fonts 前5个键: {list(system_fonts.keys())[:5]}")

# 创建字体选择下拉菜单
# 在字体选择下拉菜单创建后添加滚轮支持
# 在字体选择下拉菜单创建后添加滚轮支持
try:
    # 自定义排序函数：中文字体优先
    def sort_fonts(font_name):
        # 检查是否包含中文字符
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in font_name)
        # 中文字体排在前面，然后按名称排序
        return (0, font_name) if has_chinese else (1, font_name)


    # 对字体列表进行排序
    sorted_fonts = sorted(system_fonts.keys(), key=sort_fonts)

    font_menu = ttk.OptionMenu(text_tab, font_var, font_var.get(), *sorted_fonts)
    font_menu.pack()
    print(f"字体选择下拉菜单创建成功，共{len(sorted_fonts)}个选项")


    # 添加鼠标滚轮支持
    def on_mousewheel(event):
        # 获取当前下拉框的选项列表
        menu = font_menu['menu']
        items = []
        for i in range(menu.index('end') + 1):
            items.append(menu.entrycget(i, "label"))

        # 获取当前选中项的索引
        current_index = items.index(font_var.get()) if font_var.get() in items else 0

        # 根据滚轮方向调整索引
        if event.delta > 0:  # 向上滚动
            new_index = max(0, current_index - 1)
        else:  # 向下滚动
            new_index = min(len(items) - 1, current_index + 1)

        # 更新选中项
        if 0 <= new_index < len(items):
            font_var.set(items[new_index])

        # 防止事件传播
        return "break"


    # 绑定滚轮事件
    font_menu.bind("<MouseWheel>", on_mousewheel)  # Windows
    font_menu.bind("<Button-4>", on_mousewheel)  # Linux 向上滚动
    font_menu.bind("<Button-5>", on_mousewheel)  # Linux 向下滚动

except Exception as e:
    print(f"创建字体选择下拉菜单时出错: {e}")
    # 出错时创建一个包含默认字体的下拉菜单
    default_fonts = ["宋体", "黑体", "微软雅黑", "楷体"]
    font_menu = ttk.OptionMenu(text_tab, font_var, font_var.get(), *default_fonts)
    font_menu.pack()
    print(f"已使用默认字体列表创建下拉菜单: {default_fonts}")


    # 为默认字体下拉菜单也添加滚轮支持
    def on_mousewheel_default(event):
        menu = font_menu['menu']
        items = []
        for i in range(menu.index('end') + 1):
            items.append(menu.entrycget(i, "label"))

        current_index = items.index(font_var.get()) if font_var.get() in items else 0

        if event.delta > 0:
            new_index = max(0, current_index - 1)
        else:
            new_index = min(len(items) - 1, current_index + 1)

        if 0 <= new_index < len(items):
            font_var.set(items[new_index])

        return "break"


    font_menu.bind("<MouseWheel>", on_mousewheel_default)
    font_menu.bind("<Button-4>", on_mousewheel_default)
    font_menu.bind("<Button-5>", on_mousewheel_default)
tk.Label(text_tab, text="字体大小").pack()
font_size_var = tk.IntVar(value=user_config.get("font_size", 14))
tk.Scale(text_tab, from_=8, to_=48, orient=tk.HORIZONTAL, variable=font_size_var).pack()

# 外部服务配置页面
service_tab = create_section(notebook, "外部服务配置")
notebook.add(service_tab, text="外部服务配置")

# 使用网格布局
content_frame = ttk.Frame(service_tab)
content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

# 豹文助手配置
baowen_frame = ttk.Frame(content_frame)
baowen_frame.grid(row=0, column=0, sticky='w', pady=10)
ttk.Label(baowen_frame, text="豹文助手账号:", **label_style).pack(side=tk.LEFT)
baowen_phone_entry = ttk.Entry(baowen_frame, **entry_style)
baowen_phone_entry.pack(side=tk.LEFT, padx=5)
baowen_phone_entry.insert(0, user_config.get("baowen_phone", ""))

baowen_pwd_frame = ttk.Frame(content_frame)
baowen_pwd_frame.grid(row=1, column=0, sticky='w', pady=10)
ttk.Label(baowen_pwd_frame, text="豹文助手密码:", **label_style).pack(side=tk.LEFT)
baowen_pwd_entry = ttk.Entry(baowen_pwd_frame, show="*", **entry_style)
baowen_pwd_entry.pack(side=tk.LEFT, padx=5)
baowen_pwd_entry.insert(0, user_config.get("baowen_password", ""))

# 星耀联盟配置
xingyao_frame = ttk.Frame(content_frame)
xingyao_frame.grid(row=2, column=0, sticky='w', pady=10)
ttk.Label(xingyao_frame, text="星耀联盟账号:", **label_style).pack(side=tk.LEFT)
xingyao_phone_entry = ttk.Entry(xingyao_frame, **entry_style)
xingyao_phone_entry.pack(side=tk.LEFT, padx=5)
xingyao_phone_entry.insert(0, user_config.get("xingyao_phone", ""))

xingyao_pwd_frame = ttk.Frame(content_frame)
xingyao_pwd_frame.grid(row=3, column=0, sticky='w', pady=10)
ttk.Label(xingyao_pwd_frame, text="星耀联盟密码:", **label_style).pack(side=tk.LEFT)
xingyao_pwd_entry = ttk.Entry(xingyao_pwd_frame, show="*", **entry_style)
xingyao_pwd_entry.pack(side=tk.LEFT, padx=5)
xingyao_pwd_entry.insert(0, user_config.get("xingyao_password", ""))

# 企业微信配置
wecom_frame = ttk.Frame(content_frame)
wecom_frame.grid(row=4, column=0, sticky='w', pady=10)
ttk.Label(wecom_frame, text="企业微信机器人URL:", **label_style).pack(side=tk.LEFT)
wecom_webhook_entry = ttk.Entry(wecom_frame, **entry_style)
wecom_webhook_entry.pack(side=tk.LEFT, padx=5)
wecom_webhook_entry.insert(0, user_config.get("wecom_webhook", ""))


def start_server_operation():
    try:
        ws1.start_ws_server(logger)
        logger.log_message("服务端已启动")
    except Exception as e:
        logger.log_message(f"启动服务端时发生错误: {e}")


def start_server():
    threading.Thread(target=start_server_operation).start()
    start_server_button.config(state="disabled")
    logger.log_message("启动服务端操作已触发")


def collect_gui_config():
    return {
        "title": title_input_entry.get(),
        "font": font_var.get(),  # 保存字体名称
        "font_path": system_fonts.get(font_var.get(), ""),  # 保存字体路径
        "font_size": font_size_var.get(),
        "article_text": article_entry.get(),
        "sensitive_words": [w.strip() for w in sensitive_entry.get().split(",") if w.strip()],
        "cover_seq_range": (start_cover_seq_entry.get(), end_cover_seq_entry.get()),
        "cover_chapter_range": (start_cover_chapter_entry.get(), end_cover_chapter_entry.get()),
        "first_two_keywords": [e.get() for e in keyword_entries],
        "excel_path": excel_path_entry.get(),
        "use_excel": excel_use_var.get(),
        "mode": mode_var.get(),
        "baowen_phone": baowen_phone_entry.get(),
        "baowen_password": baowen_pwd_entry.get(),
        "xingyao_phone": xingyao_phone_entry.get(),
        "xingyao_password": xingyao_pwd_entry.get(),
        "wecom_webhook": wecom_webhook_entry.get(),
    }

# 新增函数：点击按钮时调用发送启动消息的函数
def on_start_message_click(should_send):
    async def connect_and_send():
        if should_send:
            try:
                async with websockets.connect("ws://localhost:9111") as ws:
                    await ws1.send_start_message(ws)
                    logger.log_message("启动消息已发送")
            except Exception as e:
                logger.log_message(f"发送启动消息时发生错误: {e}")
        else:
            logger.log_message("未满足发送条件，不发送启动消息")

    asyncio.run(connect_and_send())


def get_serial_number():
    result = subprocess.run(['wmic', 'bios', 'get', 'serialnumber'], capture_output=True, text=True)
    output_lines = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
    return output_lines[1] if len(output_lines) > 1 else "未找到序列号。"


# 初始化PJYSDK
pjysdk = PJYSDK(app_key='d05jdijdqusqgp5fokp0', app_secret='enEPpSEvE6xrFRbCbmjm5wTj3WCZpGni')
pjysdk.debug = True


# 心跳失败回调
def on_heartbeat_failed(hret):
    print(hret.message)
    if hret.code == 10214:
        sys.exit(0)  # 使用更优雅的退出方式
    print("心跳失败，尝试重登...")
    login_ret = pjysdk.card_login()
    if login_ret.code == 0:
        print("重登成功")
    else:
        print(login_ret.message)
        sys.exit(0)  #使用更优雅的退出方式


def get_card_key():
    user_config1 = config_manager.load_user_config()
    return user_config1.get("card_key", None)

def kill_process_by_port(port: int) -> bool:
    """通过端口号终止进程"""
    try:
        system = platform.system()

        if system == "Windows":
            # Windows 系统
            # 获取占用端口的进程 ID
            cmd = f'netstat -ano | findstr :{port}'
            result = subprocess.check_output(cmd, shell=True, text=True)

            # 解析输出获取 PID
            for line in result.split('\n'):
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.strip().split()
                    pid = parts[-1]

                    # 终止进程
                    kill_cmd = f'taskkill /F /PID {pid}'
                    subprocess.run(kill_cmd, shell=True, check=True)
                    print(f"已终止占用端口 {port} 的进程 (PID: {pid})")
                    return True
            print(f"未找到占用端口 {port} 的进程")

        elif system in ["Linux", "Darwin"]:  # Linux 或 macOS
            # 获取占用端口的进程 ID
            cmd = f'lsof -t -i :{port}'
            result = subprocess.check_output(cmd, shell=True, text=True)

            if result:
                pid = result.strip()
                # 终止进程
                kill_cmd = f'kill -9 {pid}'
                subprocess.run(kill_cmd, shell=True, check=True)
                print(f"已终止占用端口 {port} 的进程 (PID: {pid})")
                return True
            print(f"未找到占用端口 {port} 的进程")

        else:
            print(f"不支持的操作系统: {system}")
            return False

    except Exception as e:
        print(f"终止进程失败: {e}")
        return False
def close_app():
    print("开始退出程序...")

    # 停止WebSocket服务器
    try:

        kill_process_by_port(9111)

        # 等待一段时间让服务器有时间关闭
        import time
        time.sleep(1)
        print("WebSocket 服务已优雅关闭")
    except Exception as e:
        print(f"关闭 WebSocket 服务失败: {e}")

    # 停止Flask子进程
    if flask_process and flask_process.is_alive():
        flask_process.terminate()
        flask_process.join(timeout=2.0)
        if flask_process.is_alive():
            flask_process.kill()
        print("Flask 子进程已终止")

    # 关闭主窗口
    root.destroy()
    sys.exit(0)


root.protocol("WM_DELETE_WINDOW", close_app)

# 配置参数
HEARTBEAT_INTERVAL = 30  # 心跳间隔（秒）
PING_TIMEOUT = 10        # ping超时时间（秒）
MAX_RECONNECT_ATTEMPTS = 5  # 最大重连次数
RECONNECT_DELAY = 2     # 初始重连延迟（秒）
# 新增：PC 客户端连接管理器
class PCClient:
    def __init__(self, logger, server_url="ws://localhost:9111"):
        self.logger = logger
        self.server_url = server_url
        self.websocket = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.last_ping_time = 0

    async def connect(self):
        """建立WebSocket连接并处理消息"""
        while self.reconnect_attempts < MAX_RECONNECT_ATTEMPTS:
            try:
                # 连接到服务器，设置ping间隔和超时
                self.logger.log_message(f"尝试连接到服务器: {self.server_url}")
                self.websocket = await websockets.connect(
                    self.server_url,
                    ping_interval=HEARTBEAT_INTERVAL,  # 自动发送ping的间隔
                    ping_timeout=PING_TIMEOUT,  # ping超时时间
                    close_timeout=5  # 关闭连接超时
                )

                self.is_connected = True
                self.reconnect_attempts = 0
                self.logger.log_message("已成功连接到服务器")

                # 发送注册消息
                await self.register()

                # 启动消息处理循环
                await self.handle_messages()

            except ConnectionClosedOK:
                self.logger.log_message("与服务器的连接已正常关闭")
                break
            except ConnectionClosedError as e:
                self.logger.log_message(f"与服务器的连接意外关闭: {e}")
            except websockets.exceptions.InvalidState as e:
                self.logger.log_message(f"WebSocket状态错误: {e}")
            except Exception as e:
                self.logger.log_message(f"连接服务器时发生错误: {e}")
            finally:
                self.is_connected = False
                if self.websocket:
                    await self.websocket.close()
                    self.websocket = None

                # 准备重连
                self.reconnect_attempts += 1
                if self.reconnect_attempts < MAX_RECONNECT_ATTEMPTS:
                    delay = RECONNECT_DELAY * (2 ** (self.reconnect_attempts - 1))
                    self.logger.log_message(
                        f"将在 {delay} 秒后尝试重连 ({self.reconnect_attempts}/{MAX_RECONNECT_ATTEMPTS})")
                    await asyncio.sleep(delay)

        if not self.is_connected:
            self.logger.log_message(f"达到最大重连次数 ({MAX_RECONNECT_ATTEMPTS})，连接失败")

    async def register(self):
        """向服务器发送注册消息"""
        try:
            register_message = {
                "type": "register",
                "client": "pc"
            }
            await self.websocket.send(json.dumps(register_message))
            self.logger.log_message("已发送注册消息")

            # 接收注册响应
            response = await self.websocket.recv()
            response_data = json.loads(response)

            # 改进的响应处理逻辑
            if response_data.get("type") == "register_ack":
                status = response_data.get("status", "error")
                message = response_data.get("message", "")

                if status == "ok":
                    self.logger.log_message(f"注册成功: {message}")
                    return True
                else:
                    self.logger.log_message(f"注册失败: {message}")
                    return False
            else:
                self.logger.log_message(f"收到非注册响应: {response_data.get('type')}")
                return False

        except Exception as e:
            self.logger.log_message(f"注册过程中发生错误: {e}")
            return False

    async def handle_messages(self):
        """处理从服务器接收的消息"""
        try:
            while self.is_connected and self.websocket:
                # 使用asyncio.wait_for设置接收超时，避免长时间阻塞
                try:
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=60)
                    self.process_message(message)
                except asyncio.TimeoutError:
                    # 超时处理，可选择发送自定义心跳
                    pass
        except ConnectionClosedOK:
            self.logger.log_message("连接正常关闭")
        except ConnectionClosedError as e:
            self.logger.log_message(f"连接意外关闭: {e}")
        except Exception as e:
            self.logger.log_message(f"处理消息时发生错误: {e}")
        finally:
            self.is_connected = False

    def process_message(self, message):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            msg_type = data.get("type")

            if msg_type == "heartbeat":
                # 处理服务器主动发送的心跳
                self.logger.log_message("收到服务器心跳")
                # 可以选择回复pong
            elif msg_type == "heartbeat_ack":
                self.logger.log_message("收到心跳响应")
            elif msg_type == "command":
                self.logger.log_message(f"收到命令: {data.get('command')}")
            else:
                self.logger.log_message(f"收到未知类型消息: {msg_type}")
        except json.JSONDecodeError:
            self.logger.log_message(f"收到非JSON格式消息: {message}")
        except Exception as e:
            self.logger.log_message(f"解析消息时发生错误: {e}")

    async def send_start_message(self):
        """发送启动消息"""
        if not self.is_connected:
            self.logger.log_message("未连接到服务器，无法发送启动消息")
            return

        try:
            start_message = {
                "type": "start",
                "message": None
            }
            await self.websocket.send(json.dumps(start_message, ensure_ascii=False))
            self.logger.log_message("启动消息已发送")
        except Exception as e:
            self.logger.log_message(f"发送启动消息时发生错误: {e}")


# 修改原有的启动函数
pc_client = None
from threading import Thread


def start_pc_client():
    global pc_client
    if pc_client and pc_client.is_connected:
        logger.log_message("PC客户端已连接，直接发送启动消息")
        asyncio.run_coroutine_threadsafe(pc_client.send_start_message(), asyncio.get_event_loop())
        return

    # 创建新的PC客户端实例
    pc_client = PCClient(logger)

    # 在新线程中运行异步事件循环
    def run_client():
        asyncio.run(pc_client.connect())

    Thread(target=run_client, daemon=True).start()
    logger.log_message("PC客户端启动操作已触发，正在连接服务器...")

if __name__ == '__main__':
    # 启动 Flask 接口的子进程
    freeze_support()
    flask_process = multiprocessing.Process(target=flask_main)
    flask_process.start()

    serial_number = get_serial_number()
    print(serial_number)
    pjysdk.on_heartbeat_failed = on_heartbeat_failed
    pjysdk.set_device_id(serial_number)

    pjysdk.set_card(get_card_key())
    pjysdk.set_card(get_card_key())

    ret = pjysdk.card_login()
    if ret.code != 0:
        print(ret.message)
        sys.exit(1)
    if ret.message == "ok":
        print("登录成功")
        root.mainloop()
    # root.mainloop()
    # 确保 Flask 进程继续运行