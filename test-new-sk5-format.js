const axios = require('axios');

// 测试新的SK5格式（包括管道符分隔）
async function testNewSK5Format() {
  console.log('🧪 测试新的SK5格式（包括管道符分隔）...');
  
  const baseURL = 'http://localhost:15001';
  
  // 测试各种SK5格式
  const testCases = [
    {
      name: '管道符分隔格式（真实示例）',
      sk5: '450105.dns36.cn|62222|ad9mn4gx|525257|2025-07-20',
      expectSuccess: true,
      description: '真实的管道符分隔SK5格式'
    },
    {
      name: '管道符分隔格式（标准）',
      sk5: '*************|1080|user|pass|1735689600',
      expectSuccess: true,
      description: '标准的管道符分隔格式'
    },
    {
      name: '管道符分隔格式（无认证）',
      sk5: '127.0.0.1|1080|||1735689600',
      expectSuccess: true,
      description: '无用户名密码的管道符格式'
    },
    {
      name: '冒号分隔格式（5段）',
      sk5: '*************:1080:user:pass:1735689600',
      expectSuccess: true,
      description: '标准的冒号分隔5段格式'
    },
    {
      name: '冒号分隔格式（2段）',
      sk5: '127.0.0.1:1080',
      expectSuccess: true,
      description: '简单的冒号分隔2段格式'
    },
    {
      name: '@符号认证格式',
      sk5: 'user:<EMAIL>:1080',
      expectSuccess: true,
      description: '@符号认证格式'
    },
    {
      name: '混合格式（管道符中包含冒号）',
      sk5: 'proxy.example.com:8080|user|pass|1735689600',
      expectSuccess: false,
      description: '不支持的混合格式'
    },
    {
      name: '管道符格式（3段）',
      sk5: 'host|port|user',
      expectSuccess: false,
      description: '不支持的3段管道符格式'
    },
    {
      name: '管道符格式（6段）',
      sk5: 'host|port|user|pass|time|extra',
      expectSuccess: false,
      description: '不支持的6段管道符格式'
    }
  ];
  
  console.log('\n📋 开始测试各种SK5格式...\n');
  
  let successCount = 0;
  let totalCount = testCases.length;
  
  for (const testCase of testCases) {
    console.log(`🔍 测试: ${testCase.name}`);
    console.log(`   SK5数据: "${testCase.sk5}"`);
    console.log(`   描述: ${testCase.description}`);
    console.log(`   预期结果: ${testCase.expectSuccess ? '成功' : '失败'}`);
    
    try {
      const response = await axios.post(`${baseURL}/api/douyin/test-proxy-detection`, {
        sk5: testCase.sk5
      }, {
        timeout: 15000
      });
      
      // 检查是否是格式错误
      const isFormatError = response.data.msg && (
        response.data.msg.includes('格式错误') ||
        response.data.msg.includes('格式无效') ||
        response.data.msg.includes('不能为空') ||
        response.data.msg.includes('端口号无效') ||
        response.data.msg.includes('无法识别') ||
        response.data.msg.includes('SK5格式错误')
      );
      
      if (testCase.expectSuccess) {
        if (isFormatError) {
          console.log(`   ❌ 预期成功但格式验证失败: ${response.data.msg}`);
        } else {
          console.log(`   ✅ 格式验证通过`);
          if (response.data.code === 0) {
            console.log(`      检测成功: IP=${response.data.data.ip}, 省份=${response.data.data.province}`);
          } else {
            console.log(`      格式正确但连接失败: ${response.data.msg}`);
          }
          successCount++;
        }
      } else {
        if (isFormatError) {
          console.log(`   ✅ 预期失败且正确失败: ${response.data.msg}`);
          successCount++;
        } else {
          console.log(`   ❌ 预期失败但格式验证通过`);
        }
      }
      
    } catch (error) {
      if (testCase.expectSuccess) {
        console.log(`   ❌ 预期成功但请求失败: ${error.message}`);
      } else {
        console.log(`   ✅ 预期失败且请求失败: ${error.message}`);
        successCount++;
      }
    }
    
    console.log(''); // 空行分隔
  }
  
  console.log(`🎉 测试完成！成功率: ${successCount}/${totalCount} (${Math.round(successCount/totalCount*100)}%)`);
  
  if (successCount === totalCount) {
    console.log('✅ 所有测试用例都通过了！新SK5格式解析功能正常。');
  } else {
    console.log('⚠️  部分测试用例失败，需要进一步调整。');
  }
  
  console.log('\n💡 支持的SK5格式:');
  console.log('   1. 管道符分隔：代理服务器地址|端口|用户名|密码|过期时间');
  console.log('      示例：450105.dns36.cn|62222|ad9mn4gx|525257|2025-07-20');
  console.log('   2. 冒号分隔：代理服务器地址:端口:用户名:密码:过期时间');
  console.log('      示例：*************:1080:user:pass:1735689600');
  console.log('   3. 简单格式：代理服务器地址:端口');
  console.log('      示例：127.0.0.1:1080');
  console.log('   4. 认证格式：用户名:密码@代理服务器地址:端口');
  console.log('      示例：user:<EMAIL>:1080');
  
  console.log('\n🌐 现在可以在前端界面测试以下SK5格式:');
  console.log('   - 450105.dns36.cn|62222|ad9mn4gx|525257|2025-07-20');
  console.log('   - *************|1080|user|pass|1735689600');
  console.log('   - 127.0.0.1|1080|||1735689600');
  console.log('   - *************:1080:user:pass:1735689600');
  console.log('   - 127.0.0.1:1080');
  console.log('   - user:<EMAIL>:1080');
}

// 运行测试
if (require.main === module) {
  testNewSK5Format().catch(console.error);
}

module.exports = testNewSK5Format;
