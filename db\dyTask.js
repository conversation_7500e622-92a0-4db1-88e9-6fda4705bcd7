const mongoose = require('./db.js');
const Counter = require('./counter.js'); // 假设计数器模型在 counter.js 文件中

let taskSchema = new mongoose.Schema({
    taskId: {
        type: Number,
        index: { unique: true }
    },
    deviceCode: String,
    taskName: String,
    keywords: [String], // 关键词数组
    provinces: [String], // 省份数组
    maxFollowCount: Number,
    nurtureDuration: Number,
    likeProbability: Number,
    commentProbability: Number,
    comments: [String] // 评论内容数组
});

taskSchema.pre('save', function(next) {
    let doc = this;
    Counter.findByIdAndUpdate(
        { _id: 'taskId' },
        { $inc: { seq: 1 } },
        { new: true, upsert: true },
        function(error, counter) {
            if (error) return next(error);
            doc.taskId = counter.seq;
            next();
        }
    );
});

let taskModel = mongoose.model('Task', taskSchema);
module.exports = taskModel;