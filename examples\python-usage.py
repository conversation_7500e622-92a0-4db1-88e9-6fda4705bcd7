#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
推广操作数据保存API - Python使用示例
"""

import requests
import json
import time
from typing import Dict, List, Optional, Union

class PromotionActionAPI:
    """推广操作数据保存API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:15001"):
        self.base_url = base_url
        self.save_url = f"{base_url}/api/promotion-actions/save"
        self.batch_save_url = f"{base_url}/api/promotion-actions/batch-save"
        self.headers = {"Content-Type": "application/json"}
    
    def save_action(self, 
                   promotion_user_id: str,
                   action_type: str,
                   ip_address: str,
                   ip_province: Optional[str] = None,
                   douyin_name: Optional[str] = None,
                   douyin_id: Optional[str] = None,
                   ck_data: Optional[str] = None,
                   user_agent: Optional[str] = None,
                   extra_data: Optional[Dict] = None) -> Dict:
        """
        保存单条推广操作数据
        
        Args:
            promotion_user_id: 推广用户ID (必填)
            action_type: 操作类型 (必填) - scan/login_success/login_fail/request_expire
            ip_address: IP地址 (必填)
            ip_province: IP省份 (可选)
            douyin_name: 抖音名 (可选)
            douyin_id: 抖音号 (可选)
            ck_data: CK数据 (可选)
            user_agent: 用户代理 (可选)
            extra_data: 额外数据 (可选)
        
        Returns:
            Dict: API响应结果
        """
        data = {
            "promotion_user_id": promotion_user_id,
            "action_type": action_type,
            "ip_address": ip_address
        }
        
        # 添加可选字段
        if ip_province:
            data["ip_province"] = ip_province
        if douyin_name:
            data["douyin_name"] = douyin_name
        if douyin_id:
            data["douyin_id"] = douyin_id
        if ck_data:
            data["ck_data"] = ck_data
        if user_agent:
            data["user_agent"] = user_agent
        if extra_data:
            data["extra_data"] = extra_data
        
        try:
            response = requests.post(
                self.save_url,
                headers=self.headers,
                data=json.dumps(data),
                timeout=30
            )
            return response.json()
        except requests.exceptions.RequestException as e:
            return {
                "code": -1,
                "msg": f"请求失败: {str(e)}",
                "data": None
            }
    
    def batch_save_actions(self, actions: List[Dict]) -> Dict:
        """
        批量保存推广操作数据
        
        Args:
            actions: 操作数据列表
        
        Returns:
            Dict: API响应结果
        """
        data = {"actions": actions}
        
        try:
            response = requests.post(
                self.batch_save_url,
                headers=self.headers,
                data=json.dumps(data),
                timeout=30
            )
            return response.json()
        except requests.exceptions.RequestException as e:
            return {
                "code": -1,
                "msg": f"批量保存请求失败: {str(e)}",
                "data": None
            }
    
    def log_scan(self, promotion_user_id: str, ip_address: str, **kwargs) -> Dict:
        """记录扫码操作"""
        return self.save_action(
            promotion_user_id=promotion_user_id,
            action_type="scan",
            ip_address=ip_address,
            **kwargs
        )
    
    def log_login_success(self, 
                         promotion_user_id: str,
                         ip_address: str,
                         douyin_name: str,
                         douyin_id: str,
                         ck_data: str,
                         **kwargs) -> Dict:
        """记录登录成功操作"""
        return self.save_action(
            promotion_user_id=promotion_user_id,
            action_type="login_success",
            ip_address=ip_address,
            douyin_name=douyin_name,
            douyin_id=douyin_id,
            ck_data=ck_data,
            **kwargs
        )
    
    def log_login_fail(self, 
                      promotion_user_id: str,
                      ip_address: str,
                      fail_reason: Optional[str] = None,
                      **kwargs) -> Dict:
        """记录登录失败操作"""
        extra_data = kwargs.get('extra_data', {})
        if fail_reason:
            extra_data['fail_reason'] = fail_reason
            kwargs['extra_data'] = extra_data
        
        return self.save_action(
            promotion_user_id=promotion_user_id,
            action_type="login_fail",
            ip_address=ip_address,
            **kwargs
        )
    
    def log_request_expire(self, promotion_user_id: str, ip_address: str, **kwargs) -> Dict:
        """记录请求过期操作"""
        return self.save_action(
            promotion_user_id=promotion_user_id,
            action_type="request_expire",
            ip_address=ip_address,
            **kwargs
        )


def main():
    """使用示例"""
    print("🚀 推广操作数据保存API - Python使用示例")
    print("=" * 50)
    
    # 创建API客户端
    api = PromotionActionAPI()
    
    # 1. 单条数据保存示例
    print("\n1. 单条数据保存示例:")
    result = api.save_action(
        promotion_user_id="1001",
        action_type="scan",
        ip_address="*************",
        ip_province="北京",
        extra_data={
            "device_type": "mobile",
            "timestamp": int(time.time())
        }
    )
    print(f"保存结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 2. 使用便捷方法记录扫码
    print("\n2. 记录扫码操作:")
    scan_result = api.log_scan(
        promotion_user_id="1001",
        ip_address="*************",
        ip_province="北京"
    )
    print(f"扫码记录结果: {json.dumps(scan_result, indent=2, ensure_ascii=False)}")
    
    # 3. 记录登录成功
    print("\n3. 记录登录成功:")
    login_success_result = api.log_login_success(
        promotion_user_id="1001",
        ip_address="*************",
        douyin_name="张三",
        douyin_id="zhangsan123",
        ck_data="sessionid=abc123456",
        ip_province="北京",
        user_agent="Mozilla/5.0 (Python Example)"
    )
    print(f"登录成功记录结果: {json.dumps(login_success_result, indent=2, ensure_ascii=False)}")
    
    # 4. 记录登录失败
    print("\n4. 记录登录失败:")
    login_fail_result = api.log_login_fail(
        promotion_user_id="1002",
        ip_address="*************",
        fail_reason="密码错误",
        ip_province="上海"
    )
    print(f"登录失败记录结果: {json.dumps(login_fail_result, indent=2, ensure_ascii=False)}")
    
    # 5. 记录请求过期
    print("\n5. 记录请求过期:")
    expire_result = api.log_request_expire(
        promotion_user_id="1003",
        ip_address="*************",
        ip_province="广东"
    )
    print(f"请求过期记录结果: {json.dumps(expire_result, indent=2, ensure_ascii=False)}")
    
    # 6. 批量保存示例
    print("\n6. 批量保存示例:")
    batch_actions = [
        {
            "promotion_user_id": "BATCH001",
            "action_type": "scan",
            "ip_address": "*************",
            "ip_province": "浙江"
        },
        {
            "promotion_user_id": "BATCH001",
            "action_type": "login_success",
            "ip_address": "*************",
            "ip_province": "浙江",
            "douyin_name": "批量用户1",
            "douyin_id": "batch001",
            "ck_data": "sessionid=batch123"
        },
        {
            "promotion_user_id": "BATCH002",
            "action_type": "login_fail",
            "ip_address": "*************",
            "ip_province": "江苏",
            "extra_data": {
                "fail_reason": "验证码错误"
            }
        }
    ]
    
    batch_result = api.batch_save_actions(batch_actions)
    print(f"批量保存结果: {json.dumps(batch_result, indent=2, ensure_ascii=False)}")
    
    # 7. 错误处理示例
    print("\n7. 错误处理示例:")
    error_result = api.save_action(
        promotion_user_id="",  # 空的推广用户ID
        action_type="invalid_type",  # 无效的操作类型
        ip_address="999.999.999.999"  # 无效的IP地址
    )
    print(f"错误处理结果: {json.dumps(error_result, indent=2, ensure_ascii=False)}")


if __name__ == "__main__":
    main()
