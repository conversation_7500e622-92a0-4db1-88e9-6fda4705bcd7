<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui优化版</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .image-preview {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                
                <fieldset>
                    <legend>【发消息】功能</legend>

                    <div class="layui-form-item">
                        <label class="layui-form-label">功能选项</label>
                        <div class="layui-input-block">
                            <!-- 扩展功能 -->
                            <div style="margin: 15px 0 10px 0;font-weight: bold;">评论区操作</div>
                            <input type="checkbox" name="commentLikeSwitch" lay-skin="switch" lay-text="评论区点赞|评论区点赞" onchange="document.getElementById('commentLikeSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentLikeSwitchValue" name="commentLikeSwitchValue" value="off">
                            
                            <input type="checkbox" name="commentReplySwitch" lay-skin="switch" lay-text="评论区回复|评论区回复" onchange="document.getElementById('commentReplySwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentReplySwitchValue" name="commentReplySwitchValue" value="off">
                            
                            <!-- 基础功能 -->
                            <div style="margin-bottom: 10px;font-weight: bold;">视频操作</div>
                            <input type="checkbox" name="likeSwitch" lay-skin="switch" lay-text="点赞|点赞" onchange="document.getElementById('likeSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="likeSwitchValue" name="likeSwitchValue" value="off">
                            
                            <input type="checkbox" name="commentSwitch" lay-skin="switch" lay-text="评论|评论" onchange="document.getElementById('commentSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentSwitchValue" name="commentSwitchValue" value="off">
                            
                            <input type="checkbox" name="collectSwitch" lay-skin="switch" lay-text="收藏|收藏" onchange="document.getElementById('collectSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="collectSwitchValue" name="collectSwitchValue" value="off">
                            
                            <input type="checkbox" name="followSwitch" lay-skin="switch" lay-text="关注|关注" onchange="document.getElementById('followSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="followSwitchValue" name="followSwitchValue" value="off">

                            <input type="checkbox" name="homeMessageSwitch" lay-skin="switch" lay-text="作者私信|作者私信" onchange="document.getElementById('homeMessageSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="homeMessageSwitchValue" name="homeMessageSwitchValue" value="off">
       
                            <!-- 用户互动功能 -->
                            <div style="margin: 15px 0 10px 0;font-weight: bold;">评论区用户操作</div>
                            <input type="checkbox" name="commentUserFollowSwitch" lay-skin="switch" lay-text="评论区用户关注|评论区用户关注" onchange="document.getElementById('commentUserFollowSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentUserFollowSwitchValue" name="commentUserFollowSwitchValue" value="off">
                            
                            <input type="checkbox" name="commentUserLikeSwitch" lay-skin="switch" lay-text="评论区用户点赞|评论区用户点赞" onchange="document.getElementById('commentUserLikeSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentUserLikeSwitchValue" name="commentUserLikeSwitchValue" value="off">
                            
                            <input type="checkbox" name="commentUserCollectSwitch" lay-skin="switch" lay-text="评论区用户收藏|评论区用户收藏" onchange="document.getElementById('commentUserCollectSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentUserCollectSwitchValue" name="commentUserCollectSwitchValue" value="off">
                            
                            <input type="checkbox" name="commentUserMessageSwitch" lay-skin="switch" lay-text="评论区用户收藏|评论区用户私信" onchange="document.getElementById('commentUserMessageSwitchValue').value=this.checked?'on':'off'">
                            <input type="hidden" id="commentUserMessageSwitchValue" name="commentUserMessageSwitchValue" value="off">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">关键词</label>
                        <div class="layui-input-block">
                            <textarea id="keyword" name="keyword" placeholder="多个关键词请用换行分隔" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">评论内容</label>
                        <div class="layui-input-block">
                            <textarea id="commentContent" name="commentContent" placeholder="请输入评论内容" class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">链接</label>
                        <div class="layui-input-block">
                            <textarea  id="linkUrl" name="linkUrl" placeholder="请输入链接地址，多个链接请用换行分隔" class="layui-input"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">上传图片</label>
                        <div class="layui-input-block">
                            <input type="file" id="imageUpload" accept="image/*" multiple>
                            <div class="image-preview" id="imagePreview"></div>
                            <!-- 新增隐藏字段存储图片数据 -->
                            <textarea id="imageData" name="imageData" style="display:none;"></textarea>
                        </div>
                    </div>

                    <button class="layui-btn layui-btn-sm" value="提交评论区操作数据" lay-submit="" lay-filter="tijiao">执行任务</button>
                    <button class="layui-btn layui-btn-sm" lay-submit="" lay-filter="stopTask">停止任务</button>
                </fieldset>
            </form>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });

        // 图片上传处理
        const imageUpload = document.getElementById('imageUpload');
        const imagePreview = document.getElementById('imagePreview');
        const imageDataField = document.getElementById('imageData');
        let imageList = []; // 存储图片信息

        imageUpload.addEventListener('change', async function (e) {
            const files = e.target.files;
            if (!files || files.length === 0) return;

            imagePreview.innerHTML = '上传中...';
            imageList = [];

            const formData = new FormData();
            // 添加所有文件
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            try {
                const response = await axios.post('/localUpload', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });

                if (response.data && response.data.paths) {
                    const baseUrl = response.data.baseUrl || `http://${window.location.host}`;
                    const fileUrls = response.data.paths.map(path => 
                        path.startsWith('http') ? path : `${baseUrl}${path}`
                    );
                    imageList = fileUrls;
                    
                    // 显示图片预览
                    imagePreview.innerHTML = '';
                    fileUrls.forEach(url => {
                        const div = document.createElement('div');
                        div.className = 'preview-item';
                        div.innerHTML = `
                            <img src="${url}" alt="预览图">
                            <span class="delete-btn" data-url="${url}">×</span>
                        `;
                        imagePreview.appendChild(div);
                    });
                    
                    // 更新隐藏字段，只包含URL
                    imageDataField.value = fileUrls.join(',');
                }
            } catch (error) {
                console.error('上传失败:', error);
                imagePreview.innerHTML = '上传失败';
                layer.msg('图片上传失败: ' + error.message, {icon: 2});
            }
        });

        // 点击删除预览图
        imagePreview.addEventListener('click', function (e) {
            if (e.target.classList.contains('delete-btn')) {
                const imageUrl = e.target.dataset.url;
                // 从数组中移除对应图片
                imageList = imageList.filter(url => url !== imageUrl);
                e.target.parentElement.remove();
                
                // 更新隐藏字段
                updateImageDataField();
            }
        });

        // 更新隐藏字段内容
        function updateImageDataField() {
        // 更新隐藏字段，只包含URL
        imageDataField.value = imageList.join(',');
        }

        // 任务执行按钮
        document.querySelector(".layui-btn[lay-filter='tijiao']").addEventListener("click", function (event) {
            event.preventDefault();

            const keyword = document.getElementById("keyword").value.trim();
            const keywords = keyword.split('\n').filter(k => k.trim()); // 使用换行符分割关键词并过滤空值
            const commentContent = document.getElementById("commentContent").value.trim();
            const linkUrl = document.getElementById("linkUrl").value.trim();
            const imageData = imageDataField.value;

            // 验证设备选择 - 使用与tongYong.js一致的验证逻辑
            const groupsCheck = JSON.parse(document.getElementById('inputaaa').value || '{}');
            const hasSelected = Object.values(groupsCheck).some(selected => selected);
            
            if (!hasSelected) {
                console.log('groupsCheck:', groupsCheck);
                return layui.layer.msg('请至少选择一个设备');
            }

            if (!keyword || !commentContent) {
                return layui.layer.msg('请填写关键词和评论内容');
            }

            // 获取开关状态
            const likeSwitch = document.querySelector('input[name="likeSwitch"]');
            const commentSwitch = document.querySelector('input[name="commentSwitch"]');
            const collectSwitch = document.querySelector('input[name="collectSwitch"]');
            const commentLikeSwitch = document.querySelector('input[name="commentLikeSwitch"]');
            const commentReplySwitch = document.querySelector('input[name="commentReplySwitch"]');
            const followSwitch = document.querySelector('input[name="followSwitch"]');
            
            // 构建请求数据（从表单字段获取）
            const requestData = {
                data: {
                    keywords: keywords, // 确保使用分割后的关键词数组
                    commentContent: commentContent,
                    linkUrl: linkUrl.split('\n').filter(url => url.trim()), // 同样处理链接
                    images: imageData ? imageData.split(',') : [],
                    // 包含开关状态
                    switches: {
                        like: likeSwitch ? likeSwitch.checked : false,
                        comment: commentSwitch ? commentSwitch.checked : false,
                        collect: collectSwitch ? collectSwitch.checked : false
                    }
                }
            };

            // 构建完整的任务数据
            const taskData = {
                "云控账号": localStorage.getItem("userName") || "",
                "云控编号": "defaultDevice", // 应根据实际情况获取
                "配置信息": {
                    // 三个开关状态
                    likeSwitch: document.querySelector('[name="likeSwitch"]').checked ? "on" : "off",
                    commentSwitch: document.querySelector('[name="commentSwitch"]').checked ? "on" : "off", 
                    collectSwitch: document.querySelector('[name="collectSwitch"]').checked ? "on" : "off",
                    
                    // 其他表单数据
                    keywords: keywords, // 确保使用分割后的关键词数组
                    commentContent: commentContent,
                    linkUrl: linkUrl.split('\n').filter(url => url.trim()), // 同样处理链接
                    imageData: imageData || ""
                },
                "data_name": this.value, // 任务类型
                "ws": {}, // WebSocket客户端信息
                "主线程": "", // 主线程ID
                "timestamp": Date.now()
            };

            // 这里可以替换成你的实际接口
            // axios.post('http://your-api-endpoint', data)
            //     .then(response => {
            //         layui.layer.msg('任务提交成功');
            //         // 重置表单
            //         document.getElementById('keyword').value = '';
            //         document.getElementById('commentContent').value = '';
            //         document.getElementById('linkUrl').value = '';
            //         imageDataField.value = '';
            //         imageUpload.value = '';
            //         imagePreview.innerHTML = '';
            //         imageList = [];
            //     })
            //     .catch(error => {
            //         layui.layer.msg('任务提交失败，请重试');
            //         console.error('请求失败:', error);
            //     });
        });

        // 停止任务按钮
        document.querySelector(".layui-btn[lay-filter='stopTask']").addEventListener("click", function (event) {
            event.preventDefault();
            layui.layer.confirm('确定要停止当前任务吗？', { icon: 3, title: '提示' }, function (index) {
                axios.post('/wsRouter/faTast', {
                    data: {
                        taskName: "停止任务",
                        userName: localStorage.getItem("userName") || "",
                        timeStamp: Date.now()
                    }
                }).then(response => {
                    if (response.data.code === 1) {
                        layui.layer.msg('任务已停止', { icon: 1 });
                    } else {
                        layui.layer.msg('停止任务失败: ' + response.data.msg, { icon: 2 });
                    }
                }).catch(error => {
                    layui.layer.msg('停止任务失败: ' + error.message, { icon: 2 });
                }).finally(() => {
                    layer.close(index);
                });
            });
        });
    </script>
</body>

</html>