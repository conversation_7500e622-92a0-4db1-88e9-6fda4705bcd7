const express = require("express");
const router = express.Router();
const fs = require("fs");
const { localDate, layuiMenu, publicPath, adminName } = require("../serverOpt");

// 使用禁用版本的依赖
const userMod = require("../db/userMod-disabled");

// MongoDB功能已禁用，提供基本的路由响应

//获取指定用户的角色菜单接口
router.get("/getOneUserMenu", async function (req, res) {
  console.log("获取指定用户的角色菜单接口 - MongoDB已禁用");

  // 模拟session检查
  const userName = req.session?.userName || adminName;

  let newMenu = layuiMenu;
  try {
    // 始终返回管理员菜单
    let menuStr = fs.readFileSync(publicPath + "/api/menu.json", "utf-8");
    let menuJson = JSON.parse(menuStr);
    newMenu.menuInfo[0].child = menuJson;
    return res.end(JSON.stringify(newMenu));
  } catch (error) {
    console.error("读取菜单文件失败:", error);
    return res.json({ code: -1, msg: "菜单加载失败" });
  }
});

//添加用户接口
router.post("/addUser", async function (req, res) {
  console.log("添加用户接口 - MongoDB已禁用");
  res.json({
    code: -1,
    msg: "MongoDB用户管理功能已禁用，请使用MySQL数据库进行用户管理",
  });
});

router.post("/editUser", async function (req, res) {
  console.log("编辑用户接口 - MongoDB已禁用");
  res.json({
    code: -1,
    msg: "MongoDB用户管理功能已禁用，请使用MySQL数据库进行用户管理",
  });
});

//修改本账号占用控数
router.post("/editKong", async function (req, res) {
  console.log("修改控数接口 - MongoDB已禁用");
  res.json({
    code: -1,
    msg: "MongoDB用户管理功能已禁用，请使用MySQL数据库进行用户管理",
  });
});

router.post("/changePass", async function (req, res) {
  console.log("修改密码接口 - MongoDB已禁用");
  res.json({
    code: -1,
    msg: "MongoDB用户管理功能已禁用，请使用MySQL数据库进行用户管理",
  });
});

router.post("/delUser", async function (req, res) {
  console.log("删除用户接口 - MongoDB已禁用");
  res.json({
    code: -1,
    msg: "MongoDB用户管理功能已禁用，请使用MySQL数据库进行用户管理",
  });
});

//获取树形列表
router.get("/allUser", async function (req, res) {
  console.log("获取用户列表接口 - MongoDB已禁用");
  res.json({
    code: -1,
    msg: "MongoDB用户管理功能已禁用，请使用MySQL数据库进行用户管理",
    count: 0,
    data: [],
  });
});

router.get("/touPing", function (req, res) {
  res.json({ code: -1, msg: "MongoDB功能已禁用" });
});

router.get("/userLog", function (req, res) {
  res.json({
    code: -1,
    msg: "MongoDB功能已禁用",
  });
});

// 更新用户设备分配权限
router.post("/updateAssignPermission", async (req, res) => {
  console.log("更新用户权限接口 - MongoDB已禁用");
  res.json({
    code: -1,
    msg: "MongoDB用户管理功能已禁用，请使用MySQL数据库进行用户管理",
  });
});

module.exports = router;
