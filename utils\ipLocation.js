// IP省份解析工具
// 这是一个简化版本，实际项目中可以使用更完整的IP数据库

// 简化的IP省份映射表（示例数据）
const ipProvinceMap = {
  // 内网IP段
  '192.168.': '内网',
  '10.': '内网',
  '172.16.': '内网',
  '127.': '本地',
  
  // 示例公网IP段（实际使用时需要完整的IP数据库）
  '1.': '北京市',
  '14.': '广东省',
  '27.': '上海市',
  '36.': '江苏省',
  '42.': '浙江省',
  '58.': '山东省',
  '61.': '四川省',
  '101.': '北京市',
  '106.': '北京市',
  '110.': '广东省',
  '112.': '广东省',
  '113.': '广东省',
  '114.': '河南省',
  '115.': '河北省',
  '116.': '北京市',
  '117.': '安徽省',
  '118.': '山东省',
  '119.': '山东省',
  '120.': '浙江省',
  '121.': '上海市',
  '122.': '辽宁省',
  '123.': '辽宁省',
  '124.': '辽宁省',
  '125.': '辽宁省'
};

// 省份简称映射
const provinceShortMap = {
  '北京市': '北京',
  '上海市': '上海',
  '天津市': '天津',
  '重庆市': '重庆',
  '广东省': '广东',
  '江苏省': '江苏',
  '浙江省': '浙江',
  '山东省': '山东',
  '河南省': '河南',
  '四川省': '四川',
  '湖北省': '湖北',
  '湖南省': '湖南',
  '河北省': '河北',
  '福建省': '福建',
  '安徽省': '安徽',
  '辽宁省': '辽宁',
  '江西省': '江西',
  '陕西省': '陕西',
  '黑龙江省': '黑龙江',
  '内蒙古自治区': '内蒙古',
  '广西壮族自治区': '广西',
  '海南省': '海南',
  '贵州省': '贵州',
  '云南省': '云南',
  '西藏自治区': '西藏',
  '甘肃省': '甘肃',
  '青海省': '青海',
  '宁夏回族自治区': '宁夏',
  '新疆维吾尔自治区': '新疆',
  '山西省': '山西',
  '吉林省': '吉林',
  '台湾省': '台湾',
  '香港特别行政区': '香港',
  '澳门特别行政区': '澳门'
};

/**
 * 根据IP地址获取省份信息
 * @param {string} ip - IP地址
 * @param {boolean} useShort - 是否返回省份简称
 * @returns {string} 省份名称
 */
function getProvinceByIP(ip, useShort = false) {
  if (!ip || typeof ip !== 'string') {
    return '未知';
  }
  
  // 清理IP地址
  ip = ip.trim();
  
  // 检查是否是有效的IP地址格式
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (!ipRegex.test(ip)) {
    return '未知';
  }
  
  // 查找匹配的IP段
  for (const [ipPrefix, province] of Object.entries(ipProvinceMap)) {
    if (ip.startsWith(ipPrefix)) {
      return useShort ? (provinceShortMap[province] || province) : province;
    }
  }
  
  // 如果没有找到匹配的IP段，根据第一段IP进行简单判断
  const firstSegment = parseInt(ip.split('.')[0]);
  
  if (firstSegment >= 1 && firstSegment <= 126) {
    // A类地址，通常是大型网络
    if (firstSegment <= 50) {
      return useShort ? '北京' : '北京市';
    } else if (firstSegment <= 100) {
      return useShort ? '广东' : '广东省';
    } else {
      return useShort ? '上海' : '上海市';
    }
  } else if (firstSegment >= 128 && firstSegment <= 191) {
    // B类地址
    if (firstSegment <= 150) {
      return useShort ? '江苏' : '江苏省';
    } else if (firstSegment <= 170) {
      return useShort ? '浙江' : '浙江省';
    } else {
      return useShort ? '山东' : '山东省';
    }
  } else if (firstSegment >= 192 && firstSegment <= 223) {
    // C类地址
    if (firstSegment === 192) {
      return '内网';
    } else if (firstSegment <= 210) {
      return useShort ? '河南' : '河南省';
    } else {
      return useShort ? '四川' : '四川省';
    }
  }
  
  return '未知';
}

/**
 * 批量解析IP省份
 * @param {Array} ipList - IP地址数组
 * @param {boolean} useShort - 是否返回省份简称
 * @returns {Array} 包含IP和省份的对象数组
 */
function batchGetProvinceByIP(ipList, useShort = false) {
  if (!Array.isArray(ipList)) {
    return [];
  }
  
  return ipList.map(ip => ({
    ip: ip,
    province: getProvinceByIP(ip, useShort)
  }));
}

/**
 * 获取所有支持的省份列表
 * @param {boolean} useShort - 是否返回省份简称
 * @returns {Array} 省份列表
 */
function getAllProvinces(useShort = false) {
  const provinces = new Set(Object.values(ipProvinceMap));
  provinces.delete('内网');
  provinces.delete('本地');
  
  if (useShort) {
    return Array.from(provinces).map(province => provinceShortMap[province] || province);
  }
  
  return Array.from(provinces);
}

/**
 * 验证IP地址格式
 * @param {string} ip - IP地址
 * @returns {boolean} 是否是有效的IP地址
 */
function isValidIP(ip) {
  if (!ip || typeof ip !== 'string') {
    return false;
  }
  
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (!ipRegex.test(ip)) {
    return false;
  }
  
  const segments = ip.split('.');
  return segments.every(segment => {
    const num = parseInt(segment);
    return num >= 0 && num <= 255;
  });
}

module.exports = {
  getProvinceByIP,
  batchGetProvinceByIP,
  getAllProvinces,
  isValidIP,
  provinceShortMap
};
