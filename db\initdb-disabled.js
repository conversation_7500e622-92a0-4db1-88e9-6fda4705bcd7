// MongoDB功能已禁用
// 这个文件替代原来的initdb.js，避免MongoDB相关错误

const { maxDeviceNum, adminName } = require('../serverOpt');

console.log('🚫 MongoDB功能已禁用');
console.log(`📋 配置信息:`);
console.log(`   - 管理员账户: ${adminName}`);
console.log(`   - 最大设备数: ${maxDeviceNum}`);
console.log(`   - Session存储: 内存存储（重启后丢失）`);
console.log('✅ 系统将使用MySQL数据库和内存Session继续运行');

// 模拟创建默认管理员的函数（实际不执行）
function createDefaultAdmin() {
    console.log('💡 提示: MongoDB用户管理已禁用，请使用MySQL数据库进行用户管理');
    console.log(`💡 默认管理员账户: ${adminName}`);
    console.log('💡 如需用户管理功能，请在MySQL数据库中手动创建用户表');
}

// 导出空函数以保持兼容性
module.exports = {
    createDefaultAdmin,
    mongoDisabled: true
};
