const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 直接创建今日的统计数据
async function createTodayStats() {
  console.log('🔧 直接创建今日的统计数据...');
  
  const testUserId = '1001';
  const today = new Date().toISOString().split('T')[0];
  
  console.log(`\n📋 用户ID: ${testUserId}`);
  console.log(`📅 今日日期: ${today}`);
  console.log('─'.repeat(60));
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 删除今日可能存在的错误数据
    console.log('\n1️⃣ 清理今日可能存在的数据...');
    const [deleteResult] = await connection.execute(
      `DELETE FROM promotion_daily_stats 
       WHERE promotion_user_id = ? AND stat_date = ?`,
      [testUserId, today]
    );
    console.log(`清理了 ${deleteResult.affectedRows} 条记录`);
    
    // 2. 获取历史总数据作为今日数据
    console.log('\n2️⃣ 获取历史总数据...');
    
    // 获取总访问统计
    const [totalVisitStats] = await connection.execute(
      `SELECT
        COUNT(*) as visit_count,
        COUNT(DISTINCT visitor_ip) as unique_ip_count
       FROM promotion_visits
       WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    // 获取总操作统计
    const [totalActionStats] = await connection.execute(
      `SELECT
        COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
        COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
        COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count,
        COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as expire_count
       FROM promotion_actions
       WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    const statsData = {
      visit_count: totalVisitStats[0]?.visit_count || 0,
      unique_ip_count: totalVisitStats[0]?.unique_ip_count || 0,
      scan_count: totalActionStats[0]?.scan_count || 0,
      success_count: totalActionStats[0]?.success_count || 0,
      fail_count: totalActionStats[0]?.fail_count || 0,
      expire_count: totalActionStats[0]?.expire_count || 0,
    };
    
    console.log('历史总数据:', statsData);
    
    // 3. 创建今日统计数据
    console.log('\n3️⃣ 创建今日统计数据...');
    const [insertResult] = await connection.execute(
      `INSERT INTO promotion_daily_stats 
       (promotion_user_id, stat_date, visit_count, unique_ip_count, 
        scan_count, success_count, fail_count, expire_count, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        testUserId,
        today,
        statsData.visit_count,
        statsData.unique_ip_count,
        statsData.scan_count,
        statsData.success_count,
        statsData.fail_count,
        statsData.expire_count
      ]
    );
    
    console.log(`✅ 今日统计数据已创建，插入ID: ${insertResult.insertId}`);
    
    // 4. 验证创建结果
    console.log('\n4️⃣ 验证创建结果...');
    const [verifyStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats 
       WHERE promotion_user_id = ? AND stat_date = ?`,
      [testUserId, today]
    );
    
    if (verifyStats.length > 0) {
      console.log('✅ 验证成功！今日统计数据:');
      console.log('   数据:', JSON.stringify(verifyStats[0], null, 2));
      
      console.log('\n📊 统计摘要:');
      console.log(`   访问次数: ${verifyStats[0].visit_count}`);
      console.log(`   独立IP数: ${verifyStats[0].unique_ip_count}`);
      console.log(`   扫码数量: ${verifyStats[0].scan_count}`);
      console.log(`   成功数量: ${verifyStats[0].success_count}`);
      console.log(`   失败数量: ${verifyStats[0].fail_count}`);
      console.log(`   过期数量: ${verifyStats[0].expire_count}`);
      
      // 5. 测试API调用
      console.log('\n5️⃣ 测试API调用...');
      
      // 模拟API查询
      const [apiTestStats] = await connection.execute(
        `SELECT
          visit_count,
          unique_ip_count,
          scan_count,
          success_count,
          fail_count,
          expire_count
         FROM promotion_daily_stats
         WHERE promotion_user_id = ? AND stat_date = ?`,
        [testUserId, today]
      );
      
      if (apiTestStats.length > 0) {
        console.log('✅ API查询测试成功:');
        const apiData = apiTestStats[0];
        console.log(`   API返回数据: {`);
        console.log(`     visit_count: ${parseInt(apiData.visit_count) || 0},`);
        console.log(`     unique_ip_count: ${parseInt(apiData.unique_ip_count) || 0},`);
        console.log(`     scan_count: ${parseInt(apiData.scan_count) || 0},`);
        console.log(`     success_count: ${parseInt(apiData.success_count) || 0},`);
        console.log(`     fail_count: ${parseInt(apiData.fail_count) || 0},`);
        console.log(`     expire_count: ${parseInt(apiData.expire_count) || 0}`);
        console.log(`   }`);
      } else {
        console.log('❌ API查询测试失败');
      }
      
    } else {
      console.log('❌ 验证失败，今日统计数据不存在');
    }
    
    // 6. 显示所有统计数据
    console.log('\n6️⃣ 显示所有统计数据...');
    const [allStats] = await connection.execute(
      `SELECT promotion_user_id, stat_date, visit_count, unique_ip_count, 
              scan_count, success_count, fail_count, expire_count
       FROM promotion_daily_stats 
       WHERE promotion_user_id = ?
       ORDER BY stat_date DESC`,
      [testUserId]
    );
    
    console.log('所有统计数据:');
    allStats.forEach((stat, index) => {
      const dateStr = stat.stat_date.toISOString().split('T')[0];
      console.log(`   ${index + 1}. ${dateStr}: 访问=${stat.visit_count}, IP=${stat.unique_ip_count}, 成功=${stat.success_count}`);
    });
    
  } catch (error) {
    console.error('❌ 创建失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
  
  console.log('\n🎉 今日统计数据创建完成！');
  
  console.log('\n🌐 现在可以测试:');
  console.log('1. 访问推广用户登录页面: http://localhost:15001/page/promotion/promoter-login.html');
  console.log('2. 使用用户ID: 1001, 密码: 123456 登录');
  console.log('3. 应该能看到正确的统计数据（不再是0）');
  console.log('4. 打开浏览器控制台查看API响应确认数据正确');
}

// 运行创建
if (require.main === module) {
  createTodayStats().catch(console.error);
}

module.exports = createTodayStats;
