<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户浏览器调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .debug-result { background: #f5f5f5; padding: 10px; border-radius: 3px; margin: 10px 0; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 推广用户浏览器调试工具</h1>
    
    <div class="debug-section">
        <div class="debug-title">1. 登录测试</div>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">2. Cookie检查</div>
        <button onclick="checkCookies()">检查Cookies</button>
        <div id="cookieResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">3. 统计数据测试</div>
        <button onclick="testStats()">测试统计数据</button>
        <div id="statsResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">4. 用户信息测试</div>
        <button onclick="testUserInfo()">测试用户信息</button>
        <div id="userInfoResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">5. 操作记录测试</div>
        <button onclick="testActions()">测试操作记录</button>
        <div id="actionsResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">6. 完整流程测试</div>
        <button onclick="testFullFlow()">完整流程测试</button>
        <div id="fullFlowResult" class="debug-result"></div>
    </div>

    <script>
        // 测试用户
        const testUser = { user_id: '1001', password: '123456' };
        
        // 通用请求函数
        async function makeRequest(url, options = {}) {
            const defaultOptions = {
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            try {
                console.log(`🔄 请求: ${url}`, finalOptions);
                const response = await fetch(url, finalOptions);
                const data = await response.json();
                
                console.log(`✅ 响应: ${url}`, {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: data
                });
                
                return { success: true, status: response.status, data: data, headers: response.headers };
            } catch (error) {
                console.error(`❌ 请求失败: ${url}`, error);
                return { success: false, error: error.message };
            }
        }
        
        // 1. 测试登录
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '🔄 正在测试登录...';
            
            const result = await makeRequest('/api/promotion/promoter/login', {
                method: 'POST',
                body: JSON.stringify(testUser)
            });
            
            if (result.success) {
                if (result.data.code === 0) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 登录成功</div>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                        <div>响应头: ${JSON.stringify(Object.fromEntries(result.headers.entries()), null, 2)}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 登录失败</div>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                }
            } else {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${result.error}</div>`;
            }
        }
        
        // 2. 检查Cookies
        function checkCookies() {
            const resultDiv = document.getElementById('cookieResult');
            const cookies = document.cookie;
            
            console.log('🍪 当前cookies:', cookies);
            
            if (cookies) {
                const cookieArray = cookies.split(';').map(c => c.trim());
                const sessionCookie = cookieArray.find(c => c.startsWith('connect.sid='));
                
                resultDiv.innerHTML = `
                    <div class="success">✅ 找到Cookies</div>
                    <div><strong>所有Cookies:</strong></div>
                    <pre>${cookieArray.join('\n')}</pre>
                    <div><strong>Session Cookie:</strong> ${sessionCookie || '未找到'}</div>
                `;
            } else {
                resultDiv.innerHTML = '<div class="warning">⚠️ 没有找到任何cookies</div>';
            }
        }
        
        // 3. 测试统计数据
        async function testStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.innerHTML = '🔄 正在测试统计数据...';
            
            const result = await makeRequest('/api/promotion/promoter/today-stats');
            
            if (result.success) {
                if (result.data.code === 0) {
                    const stats = result.data.data;
                    const hasNonZeroData = Object.values(stats).some(val => val > 0);
                    
                    resultDiv.innerHTML = `
                        <div class="${hasNonZeroData ? 'success' : 'warning'}">
                            ${hasNonZeroData ? '✅' : '⚠️'} 统计数据获取成功
                        </div>
                        <div><strong>统计数据:</strong></div>
                        <pre>访问次数: ${stats.visit_count}
独立IP数: ${stats.unique_ip_count}
扫码数量: ${stats.scan_count}
成功数量: ${stats.success_count}
失败数量: ${stats.fail_count}
过期数量: ${stats.expire_count}</pre>
                        <div><strong>原始响应:</strong></div>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 统计数据获取失败</div>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                }
            } else {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${result.error}</div>`;
            }
        }
        
        // 4. 测试用户信息
        async function testUserInfo() {
            const resultDiv = document.getElementById('userInfoResult');
            resultDiv.innerHTML = '🔄 正在测试用户信息...';
            
            const result = await makeRequest('/api/promotion/promoter/user-info');
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <div class="${result.data.code === 0 ? 'success' : 'error'}">
                        ${result.data.code === 0 ? '✅' : '❌'} 用户信息
                    </div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${result.error}</div>`;
            }
        }
        
        // 5. 测试操作记录
        async function testActions() {
            const resultDiv = document.getElementById('actionsResult');
            resultDiv.innerHTML = '🔄 正在测试操作记录...';
            
            const result = await makeRequest('/api/promotion/promoter/today-actions?page=1&limit=5');
            
            if (result.success) {
                if (result.data.code === 0) {
                    const actions = result.data.data;
                    resultDiv.innerHTML = `
                        <div class="success">✅ 操作记录获取成功</div>
                        <div><strong>总记录数:</strong> ${actions.total}</div>
                        <div><strong>当前页记录数:</strong> ${actions.list.length}</div>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 操作记录获取失败</div>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                }
            } else {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${result.error}</div>`;
            }
        }
        
        // 6. 完整流程测试
        async function testFullFlow() {
            const resultDiv = document.getElementById('fullFlowResult');
            resultDiv.innerHTML = '🔄 正在进行完整流程测试...<br>';
            
            let log = '';
            
            // 步骤1: 登录
            log += '1️⃣ 登录测试...<br>';
            const loginResult = await makeRequest('/api/promotion/promoter/login', {
                method: 'POST',
                body: JSON.stringify(testUser)
            });
            
            if (loginResult.success && loginResult.data.code === 0) {
                log += '✅ 登录成功<br>';
                
                // 步骤2: 检查cookies
                log += '2️⃣ 检查cookies...<br>';
                const cookies = document.cookie;
                if (cookies.includes('connect.sid')) {
                    log += '✅ Session cookie存在<br>';
                    
                    // 步骤3: 测试统计数据
                    log += '3️⃣ 测试统计数据...<br>';
                    const statsResult = await makeRequest('/api/promotion/promoter/today-stats');
                    
                    if (statsResult.success && statsResult.data.code === 0) {
                        const stats = statsResult.data.data;
                        const hasNonZeroData = Object.values(stats).some(val => val > 0);
                        
                        log += `${hasNonZeroData ? '✅' : '⚠️'} 统计数据: 访问=${stats.visit_count}, 成功=${stats.success_count}<br>`;
                        
                        if (!hasNonZeroData) {
                            log += '<div class="warning">⚠️ 所有数据都为0，可能需要检查数据库或API逻辑</div>';
                        }
                    } else {
                        log += `❌ 统计数据获取失败: ${statsResult.data?.msg || '未知错误'}<br>`;
                    }
                } else {
                    log += '❌ Session cookie不存在<br>';
                }
            } else {
                log += `❌ 登录失败: ${loginResult.data?.msg || '未知错误'}<br>`;
            }
            
            resultDiv.innerHTML = log;
        }
        
        // 页面加载时自动检查cookies
        window.onload = function() {
            console.log('🚀 推广用户浏览器调试工具已加载');
            checkCookies();
        };
    </script>
</body>
</html>
