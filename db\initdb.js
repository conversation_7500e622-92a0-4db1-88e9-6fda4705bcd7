const { ppxxxkkk, maxDeviceNum, adminName, localDate, maxLevel } = require('../serverOpt');
const userMod = require('./userMod'); // 更新为正确的路径
const crypto = require('crypto');
createDefaultAdmin();

async function createDefaultAdmin() {
    try {
        const useDoc = await userMod.findOne({ userName: adminName })
        if (useDoc) {
            console.log(`超级管理员账户已存,无需创建:${adminName},最大控数${maxDeviceNum},本账号可用控数:${useDoc.userDeviceNum}`);
        } else {
            const newAdmin = new userMod({
                userName: adminName,
                userPass: encrypt('123456'),
                maxDeviceNum: maxDeviceNum,//本代理最大控数
                userDeviceNum: maxLevel == 0 ? maxDeviceNum : 10,//本账号可用控数
                beiZhu: "超级管理员",
                parent: "111111111111111111111111",
                userLevel: 0,
                checkStatus: true,//是否审核通过
                creatTime: localDate(),//加入时间
                touPing: true
                // userObj: JSON.stringify({})
            });
            await newAdmin.save();
            console.log(`超级管理员创建成功:${adminName},初始密码:${newAdmin.userPass},最大控数${maxDeviceNum},本账号占用控数:${newAdmin.userDeviceNum}`);
            console.log(JSON.stringify(newAdmin.userPass));
            
        }
    } catch (error) {
        console.error("创建超级管理员出错:", error);
    }
}
function encrypt(text) {
    const iv = crypto.randomBytes(16); // 初始化向量
    const cipher = crypto.createCipheriv('aes-256-cbc', ppxxxkkk, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return { iv: iv.toString('hex'), encryptedData: encrypted };
}