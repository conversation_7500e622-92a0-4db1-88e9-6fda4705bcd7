<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>layui优化版</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/css/public.css" media="all">
    <style>
        .image-preview {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <form class="layui-form layui-form-pane" action="">
                <fieldset style="border-color: blue;border-width: 3px; background-color: rgb(255, 252, 194);">
                    <legend style="color: blue;font-weight: bold;font-size: 16px;">注意：请选择分组(点击全选,双击展开组内设备,可单选可多选) <input id="inputaaa" />
                    </legend>
                    <input type="checkbox" lay-filter="quanXuan1" title="全选" />
                    <br>
                    <br>
                    <div class="layui-form-item" id="groups1"></div>
                </fieldset>
                
                <fieldset>
                    <legend>【获取链接】功能</legend>
                    
                    <div class="layui-form-item">
                        <button class="layui-btn layui-btn-normal" id="queryMessagesBtn">查询消息数据</button>
                    </div>
                    
                    <!-- 消息数据表格 -->
                    <table class="layui-table" id="messagesTable" lay-filter="messagesTable"></table>

                    <!-- <div class="layui-form-item">
                        <label class="layui-form-label">名称</label>
                        <div class="layui-input-block">
                            <input type="myName" id="myName" name="myName" placeholder="请输入名称" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">个性签名</label>
                        <div class="layui-input-block">
                            <textarea id="PersonalizedSignature" name="PersonalizedSignature" placeholder="请输入个性签名" class="layui-textarea"></textarea>
                        </div>
                    </div> -->


                    <!-- <div class="layui-form-item">
                        <label class="layui-form-label">上传头像</label>
                        <div class="layui-input-block">
                            <input type="file" id="imageUpload" accept="image/*" multiple>
                            <div class="image-preview" id="imagePreview"></div>
                             新增隐藏字段存储图片数据 
                            <textarea id="headImageData" name="headImageData" style="display:none;"></textarea>
                        </div>
                    </div> -->
                        
                    <button class="layui-btn layui-btn-sm" value="获取链接" lay-submit="" lay-filter="tijiao">执行任务</button>
                    <button class="layui-btn layui-btn-sm" lay-submit="" lay-filter="stopTask">停止任务</button>
                </fieldset>
                <fieldset>
                    <legend>得到的数据</legend>

                </fieldset>
            </form>
            
            <!-- 添加数据展示表格 -->
            <div class="layui-card">
                <div class="layui-card-header">链接数据</div>
                <div class="layui-card-body">
                    <table id="linkTable" lay-filter="linkTable"></table>
                </div>
            </div>
        </div>
    </div>

    <script src="/lib/layui/layui.js" charset="utf-8"></script>
    <script src="/lib/axios/axios.js" charset="utf-8"></script>
    <script src="/js/lay-config.js?v=2.0.4" charset="utf-8"></script>
    
    <script>
        // 初始化表格和数据
        var tableData = [];
        var tableIndex = 1;
        
        layui.use(['table', 'layer'], function(){
            var table = layui.table;
            var layer = layui.layer;
            
            // 渲染表格 - 添加调试
            table.render({
                elem: '#linkTable',
                data: tableData,
                cols: [[
                    {field: 'index', title: '序号', width: 80},
                    {field: 'phoneNumber', title: '设备编号', width: 150},
                    {field: 'username', title: '名称', width: 150},
                    {field: 'links', title: '链接', minWidth: 200}
                ]],
                done: function(res, curr, count) {
                    console.log('表格渲染完成，数据:', res.data);
                    console.log('当前页:', curr);
                    console.log('总数据量:', count);
                }
            });
                        
            // 页面加载时立即加载数据
            loadLinkData();
            // 获取当前用户名并验证
            // function initUserSession() {
            //     console.log('开始初始化用户会话...');
            //     var userName = sessionStorage.getItem('userName') || 
            //                   (layui.sessionData && layui.sessionData('userInfo') && layui.sessionData('userInfo').userName);
                
            //     console.log('获取到的userName:', userName);
                
            //     if (!userName) {
            //         var msg = '无法获取用户信息，请重新登录';
            //         console.error(msg);
            //         layui.layer.msg(msg, {icon: 2, time: 3000});
            //         setTimeout(() => {
            //             window.location.href = '/page/login/login.html';
            //         }, 2000);
            //         return null;
            //     }
            //     return userName;
            // }
            
            // // 初始化并加载数据
            // var userName = initUserSession();
            // if (userName) {
            //     console.log('用户会话验证通过，开始加载数据...');
            //     loadLinkData();
            // }
            
            // 每10秒自动刷新数据
            setInterval(() => {
                console.log('定时刷新数据...');
                loadLinkData();
            }, 10000);
            
            /**
             * 数据流说明：
             * 1. 任务执行后数据由后端处理并存入数据库
             * 2. 此函数通过API从后端获取数据
             * 3. 后端负责数据库查询和权限验证
             * 4. 定时刷新确保数据最新
             */
            function loadLinkData() {
                console.log('从MySQL加载所有数据');
                axios.get('/wsRouter/queryAllLinkData')
                .then(function(response) {
                    console.log('MySQL查询结果:', response.data);
                    
                    if (response.data && response.data.code === 1) {
                        const mysqlData = response.data.data || [];
                        
                        if (mysqlData.length > 0) {
                            tableData = mysqlData.map((item, index) => {
                                return {
                                    index: index + 1,
                                    phoneNumber: item.phoneNumber || '无',
                                    username: item.username || '无',
                                    links: item.links || '无'
                                };
                            });
                            
                            console.log('格式化后的表格数据:', tableData);
                            return table.reload('linkTable', {
                                data: tableData
                            });
                        } else {
                            layer.msg('未查询到数据', {icon: 2});
                            return Promise.reject('空数据集');
                        }
                    } else {
                        const errMsg = response.data?.msg || '查询失败';
                        throw new Error(errMsg);
                    }
                })
                .catch(function(error) {
                    console.error('加载数据失败 - 完整错误:', {
                        message: error.message,
                        stack: error.stack,
                        response: error.response
                    });
                    
                    layer.msg('数据加载失败，请检查控制台', {icon: 2, time: 3000});
                });
            }
        });
        
        // 页面加载时立即请求数据
        loadLinkData();
        
        // 初始化消息数据表格
        layui.use(['table'], function() {
            var table = layui.table;
            
            // 渲染消息数据表格
            table.render({
                elem: '#messagesTable',
                url: '/getMessages',
                page: true,
                limit: 10,
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'type', title: '类型', width: 100},
                    {field: 'phoneNumber', title: '电话号码', width: 120},
                    {field: 'username', title: '用户名', width: 120},
                    {field: 'links', title: '链接', minWidth: 200},
                    {field: 'create_time', title: '创建时间', width: 160, sort: true}
                ]]
            });
            
            // 查询消息按钮点击事件
            $('#queryMessagesBtn').click(function() {
                table.reload('messagesTable');
            });
        });
    </script>
    <script>
        layui.use('tongYong', function () {
            var tongYong = layui.tongYong;
            tongYong.tongYong1();
        });

        // 图片上传处理
        const imageUpload = document.getElementById('imageUpload');
        const imagePreview = document.getElementById('imagePreview');
        const imageDataField = document.getElementById('headImageData');
        let imageList = []; // 存储图片信息

        // 生成唯一ID
        function generateUniqueId() {
            return 'img_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
        }

        imageUpload.addEventListener('change', function (e) {
            const files = e.target.files;
            imageList = []; // 清空原有列表
            imagePreview.innerHTML = ''; // 清空预览

            Array.from(files).forEach(file => {
                const reader = new FileReader();
                const imageId = generateUniqueId(); // 为每张图片生成唯一ID
                
                reader.onload = function (event) {
                    const base64 = event.target.result;
                    
                    // 存储图片信息（包含ID和base64）
                    imageList.push({
                        id: imageId,
                        base64: base64,
                        name: file.name,
                        type: file.type
                    });
                    
                    // 创建预览元素
                    const div = document.createElement('div');
                    div.className = 'preview-item';
                    div.innerHTML = `
                        <img src="${base64}" alt="预览图">
                        <span class="delete-btn" data-id="${imageId}">×</span>
                    `;
                    imagePreview.appendChild(div);
                    
                    // 更新隐藏字段
                    updateImageDataField();
                };
                reader.readAsDataURL(file);
            });
        });

        // 点击删除预览图
        imagePreview.addEventListener('click', function (e) {
            if (e.target.classList.contains('delete-btn')) {
                const imageId = e.target.dataset.id;
                // 从数组中移除对应图片
                imageList = imageList.filter(img => img.id !== imageId);
                e.target.parentElement.remove();
                
                // 更新隐藏字段
                updateImageDataField();
            }
        });

        // 更新隐藏字段内容
        function updateImageDataField() {
            // 将图片数组转换为JSON字符串
            const jsonString = JSON.stringify(imageList);
            // 设置到隐藏字段中
            imageDataField.value = jsonString;
        }

        // 任务执行按钮
        // document.querySelector(".layui-btn[lay-filter='tijiao']").addEventListener("click", function (event) {
        //     event.preventDefault();

        //     const keyword = document.getElementById("keyword").value.trim();
        //     const commentContent = document.getElementById("commentContent").value.trim();
        //     const linkUrl = document.getElementById("linkUrl").value.trim();
        //     const imageData = imageDataField.value;

        //     if (!keyword || !commentContent) {
        //         return layui.layer.msg('请填写关键词和评论内容');
        //     }

        //     // 构建请求数据（从表单字段获取）
        //     const data = {
        //         keyword,
        //         commentContent,
        //         linkUrl,
        //         images: imageData ? JSON.parse(imageData) : []
        //     };

        //     // 这里可以替换成你的实际接口
        //     // axios.post('http://your-api-endpoint', data)
        //     //     .then(response => {
        //     //         layui.layer.msg('任务提交成功');
        //     //         // 重置表单
        //     //         document.getElementById('keyword').value = '';
        //     //         document.getElementById('commentContent').value = '';
        //     //         document.getElementById('linkUrl').value = '';
        //     //         imageDataField.value = '';
        //     //         imageUpload.value = '';
        //     //         imagePreview.innerHTML = '';
        //     //         imageList = [];
        //     //     })
        //     //     .catch(error => {
        //     //         layui.layer.msg('任务提交失败，请重试');
        //     //         console.error('请求失败:', error);
        //     //     });
        // });

        // 停止任务按钮
        document.querySelector(".layui-btn[lay-filter='stopTask']").addEventListener("click", function () {
            layui.layer.confirm('确定要停止当前任务吗？', { icon: 3, title: '提示' }, function (index) {
                // 这里添加停止任务的逻辑
                layui.layer.msg('任务已停止', { icon: 5 });
                layer.close(index);
            });
        });
    </script>
</body>

</html>