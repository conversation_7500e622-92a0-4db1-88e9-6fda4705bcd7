const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'douyin',
  charset: 'utf8mb4'
};

// 测试API查询逻辑
async function testAPIQuery() {
  console.log('🔧 测试API查询逻辑...');
  
  const testUserId = '1001';
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 测试API中的查询逻辑
    console.log('\n1️⃣ 测试API中的查询逻辑...');
    console.log(`查询用户ID: ${testUserId}`);
    console.log(`当前日期: ${new Date().toISOString().split('T')[0]}`);
    
    // 模拟API中的查询
    const [dailyStats] = await connection.execute(
      `SELECT
        visit_count,
        unique_ip_count,
        scan_count,
        success_count,
        fail_count,
        expire_count
       FROM promotion_daily_stats
       WHERE promotion_user_id = ? AND DATE(stat_date) = CURDATE()`,
      [testUserId]
    );
    
    console.log(`从promotion_daily_stats表查询结果: ${dailyStats.length} 条记录`);
    
    if (dailyStats.length > 0) {
      console.log('✅ 找到今日统计数据:');
      const stats = dailyStats[0];
      console.log(`   访问次数: ${stats.visit_count}`);
      console.log(`   独立IP数: ${stats.unique_ip_count}`);
      console.log(`   扫码数量: ${stats.scan_count}`);
      console.log(`   成功数量: ${stats.success_count}`);
      console.log(`   失败数量: ${stats.fail_count}`);
      console.log(`   过期数量: ${stats.expire_count}`);
      
      // 模拟API返回的数据处理
      const apiResponse = {
        visit_count: parseInt(stats.visit_count) || 0,
        unique_ip_count: parseInt(stats.unique_ip_count) || 0,
        scan_count: parseInt(stats.scan_count) || 0,
        success_count: parseInt(stats.success_count) || 0,
        fail_count: parseInt(stats.fail_count) || 0,
        expire_count: parseInt(stats.expire_count) || 0,
      };
      
      console.log('\n📡 API将返回的数据:');
      console.log(JSON.stringify(apiResponse, null, 2));
      
    } else {
      console.log('⚠️ 没有找到今日统计数据，开始实时计算...');
      
      // 实时计算访问统计
      const [visitStats] = await connection.execute(
        `SELECT
          COUNT(*) as visit_count,
          COUNT(DISTINCT visitor_ip) as unique_ip_count
         FROM promotion_visits
         WHERE promotion_user_id = ? AND DATE(visit_time) = CURDATE()`,
        [testUserId]
      );
      
      // 实时计算操作统计
      const [actionStats] = await connection.execute(
        `SELECT
          COUNT(CASE WHEN action_type = 'scan' THEN 1 END) as scan_count,
          COUNT(CASE WHEN action_type = 'login_success' THEN 1 END) as success_count,
          COUNT(CASE WHEN action_type = 'login_fail' THEN 1 END) as fail_count,
          COUNT(CASE WHEN action_type = 'request_expire' THEN 1 END) as expire_count
         FROM promotion_actions
         WHERE promotion_user_id = ? AND DATE(action_time) = CURDATE()`,
        [testUserId]
      );
      
      console.log('实时计算结果:');
      console.log(`   访问统计: ${JSON.stringify(visitStats[0])}`);
      console.log(`   操作统计: ${JSON.stringify(actionStats[0])}`);
      
      const statsData = {
        visit_count: visitStats[0]?.visit_count || 0,
        unique_ip_count: visitStats[0]?.unique_ip_count || 0,
        scan_count: actionStats[0]?.scan_count || 0,
        success_count: actionStats[0]?.success_count || 0,
        fail_count: actionStats[0]?.fail_count || 0,
        expire_count: actionStats[0]?.expire_count || 0,
      };
      
      console.log('\n📡 实时计算的API返回数据:');
      console.log(JSON.stringify(statsData, null, 2));
    }
    
    // 2. 检查数据库中的实际数据
    console.log('\n2️⃣ 检查数据库中的实际数据...');
    
    // 检查所有统计数据
    const [allStats] = await connection.execute(
      `SELECT * FROM promotion_daily_stats WHERE promotion_user_id = ? ORDER BY stat_date DESC`,
      [testUserId]
    );
    
    console.log(`promotion_daily_stats表中共有 ${allStats.length} 条记录:`);
    allStats.forEach((stat, index) => {
      console.log(`   ${index + 1}. 日期=${stat.stat_date}, 访问=${stat.visit_count}, 成功=${stat.success_count}`);
    });
    
    // 检查访问记录
    const [visitCount] = await connection.execute(
      `SELECT COUNT(*) as total, COUNT(DISTINCT visitor_ip) as unique_ips
       FROM promotion_visits WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    console.log(`promotion_visits表中的数据: 总访问=${visitCount[0].total}, 独立IP=${visitCount[0].unique_ips}`);
    
    // 检查操作记录
    const [actionCount] = await connection.execute(
      `SELECT action_type, COUNT(*) as count
       FROM promotion_actions WHERE promotion_user_id = ?
       GROUP BY action_type`,
      [testUserId]
    );
    
    console.log('promotion_actions表中的数据:');
    actionCount.forEach(action => {
      console.log(`   ${action.action_type}: ${action.count}次`);
    });
    
    // 3. 检查日期匹配问题
    console.log('\n3️⃣ 检查日期匹配问题...');
    
    const [dateCheck] = await connection.execute(
      `SELECT 
        stat_date,
        DATE(stat_date) as date_only,
        CURDATE() as current_date,
        DATE(stat_date) = CURDATE() as is_today
       FROM promotion_daily_stats 
       WHERE promotion_user_id = ?`,
      [testUserId]
    );
    
    console.log('日期匹配检查:');
    dateCheck.forEach((row, index) => {
      console.log(`   ${index + 1}. 存储日期=${row.date_only}, 当前日期=${row.current_date}, 是否匹配=${row.is_today}`);
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
  
  console.log('\n🎉 API查询测试完成！');
}

// 运行测试
testAPIQuery().catch(console.error);
